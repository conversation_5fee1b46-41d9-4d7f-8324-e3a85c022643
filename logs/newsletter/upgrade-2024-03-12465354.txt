15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.5 MB/512 MB - tb<PERSON><PERSON> > Start upgrade from 7.8.8
15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tb<PERSON>baum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tb<PERSON>baum > <PERSON><PERSON>y
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tb<PERSON>baum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tb<PERSON>baum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tb<PERSON><PERSON> > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.6 MB/512 MB - tblobaum > Backing up options of 7.8.8
15-03-2024 19:32:40 - INFO  - 8.2.3 - 18.7 MB/512 MB - tblobaum > End
15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.2 MB/512 MB - tblobaum > Start upgrade from 7.8.8
15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

15-03-2024 19:32:42 - INFO  - 8.2.3 - 18.4 MB/512 MB - tblobaum > End
29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Start upgrade from 8.2.3
29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.5 MB/512 MB - tblobaum > Backing up options of 8.2.3
29-03-2024 12:25:27 - INFO  - 8.2.5 - 12.6 MB/512 MB - tblobaum > End
