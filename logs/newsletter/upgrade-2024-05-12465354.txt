02-05-2024 06:03:24 - INFO  - 8.3.1 - 13.3 MB/512 MB - tb<PERSON><PERSON> > Start upgrade from 8.3.0
02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tb<PERSON>baum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tb<PERSON>baum > Array
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tb<PERSON>baum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tb<PERSON>baum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tb<PERSON><PERSON> > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.7 MB/512 MB - tblobaum > Backing up options of 8.3.0
02-05-2024 06:03:24 - INFO  - 8.3.1 - 12.8 MB/512 MB - tblobaum > End
29-05-2024 09:17:22 - INFO  - 8.3.4 - 13.3 MB/512 MB - tblobaum > Start upgrade from 8.3.1
29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

29-05-2024 09:17:22 - INFO  - 8.3.4 - 12.9 MB/512 MB - tblobaum > Backing up options of 8.3.1
29-05-2024 09:17:22 - INFO  - 8.3.4 - 13.0 MB/512 MB - tblobaum > End
30-05-2024 11:24:00 - INFO  - 8.3.5 - 11.2 MB/512 MB - tblobaum > Start upgrade from 8.3.4
30-05-2024 11:24:00 - INFO  - 8.3.5 - 11.2 MB/512 MB - tblobaum > Start upgrade from 8.3.4
30-05-2024 11:24:00 - INFO  - 8.3.5 - 11.2 MB/512 MB - tblobaum > Start upgrade from 8.3.4
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_emails`] => Created table `wp_newsletter_emails`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_sent`] => Created table `wp_newsletter_sent`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter`] => Created table `wp_newsletter`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_logs`] => Created table `wp_newsletter_user_logs`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_user_meta`] => Created table `wp_newsletter_user_meta`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_stats`] => Created table `wp_newsletter_stats`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Array
(
    [`wp_newsletter_logs`] => Created table `wp_newsletter_logs`
)

30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Deleting settings backup newsletter_backup_7.8.8
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Deleting settings backup newsletter_backup_7.8.8
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Deleting settings backup newsletter_backup_7.8.8
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > Backing up options of 8.3.4
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Backing up options of 8.3.4
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.5 MB/512 MB - tblobaum > Backing up options of 8.3.4
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.7 MB/512 MB - tblobaum > End
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > End
30-05-2024 11:24:01 - INFO  - 8.3.5 - 11.6 MB/512 MB - tblobaum > End
