== Changelog ==

= 4.3.1 (2025-05-29) =
* `[Removed]` Filter `acfbs_search_post_object_fields` to select post fields using to search
* `[Added]` Filter `post_search_columns` to change the columns to search in a WP_Query search

= 4.3.0 (2025-04-29) =
* `[Security]` Update of Axios HTTP client
* `[Added]` Support for WordPress 6.8

= 4.2.1 (2024-04-12) =
* `[Fixed]` Searching for content in file field when Lite Mode is active
* `[Fixed]` UX of plugin settings page
* `[Added]` Support for WordPress 6.5

= 4.2.0 (2023-09-11) =
* `[Fixed]` Duplicated search results for modified SQL query

= 4.1.1 (2023-06-29) =
* `[Added]` Filter `acfbs_field_types` to add new supported field types

= 4.1.0 (2023-03-02) =
* `[Changed]` Appearance of plugin settings page
* `[Added]` Support for Table field type generated by Advanced Custom Fields: Table Field plugin
* `[Added]` Support for WordPress 6.2

= 4.0.1 (2022-01-17) =
* `[Fixed]` Fixed disabling suppress_filters option for WP_Query

= 4.0.0 (2021-09-25) =
* `[Fixed]` Searching for phrases in database with mixed data structure
* `[Added]` Escaping functions for translated phrases
* `[Added]` Escaping phrases for SQL Query
* `[Added]` Modifications to appearance of plugin settings page

= 3.8.1 (2021-05-22) =
* `[Fixed]` Closing notice in admin panel

= 3.8.0 (2021-01-13) =
* `[Fixed]` Search using regular expression
* `[Added]` Option to use optional implementation of regular expression by Henry Spencer

= 3.7.0 (2020-12-22) =
* `[Changed]` Regular expressions in SQL query to Henry Spencer's implementation
* `[Added]` Filter `acfbs_sql_where` to change WHERE part of SQL query
* `[Added]` Filter `acfbs_sql_join` to change INNER JOIN part of SQL query

= 3.6.0 (2020-10-28) =
* `[Changed]` Required PHP version to 7.0

= 3.5.3 (2020-04-05) =
* `[Removed]` Support for inverted values in `_postmeta` table
* `[Added]` Support for incorrect data structure in `_postmeta` table *(fixes search for imported and duplicated posts)*

= 3.5.2 (2020-03-31) =
* `[Fixed]` Displaying new values after saving settings
* `[Fixed]` Support for empty array returned by `acfbs_search_post_object_fields` filter
* `[Changed]` Static methods for filters `posts_join`, `pre_get_posts`, `posts_request` and `posts_search`
* `[Added]` Support for inverted values in `_postmeta` table *(fixes search for imported posts)*

= 3.5.1 (2020-03-19) =
* `[Fixed]` Search based only on `post_title`, `post_content` or `post_excerpt`

= 3.5.0 (2020-03-11) =
* `[Changed]` Improved SQL query performance
* `[Added]` The ability to search whole words
* `[Added]` Support for field name prefixes other than `field_`

= 3.4.3 (2019-10-03) =
* `[Added]` Filter `acfbs_search_post_object_fields` to select post fields using to search

= 3.4.2 (2019-09-26) =
* `[Fixed]` Fix for `posts_join` filter
* `[Changed]` Update priority of `posts_search` filter *(from 10 do 0)*

= 3.4.1 (2019-09-20) =
* `[Added]` Filter `acfbs_search_is_available` to block search

= 3.4.0 (2019-09-16) =
* `[Added]` New way to start search engine
* `[Added]` Filter `acfbs_is_available` to turn off search engine

= 3.3.2 (2019-07-23) =
* `[Added]` Validation for fields types on settings page

= 3.3.1 (2019-06-26) =
* `[Fixed]` Security changes

= 3.3.0 (2019-06-17) =
* `[Changed]` Settings page
* `[Changed]` Admin notice

= 3.2.0 (2019-05-27) =
* `[Changed]` Plugin structure
* `[Added]` Mode to ability using only selected fields for searching
* `[Added]` Filters to extend plugin capabilities

= 3.1.3 (2019-03-29) =
* `[Fixed]` Full path for loaded PHP files

= 3.1.2 (2018-10-24) =
* `[Added]` Support for free version of ACF 5

= 3.1.1 (2018-10-22) =
* `[Added]` Default hidden admin notice

= 3.1.0 (2018-10-18) =
* `[Changed]` Improved search engine
* `[Changed]` Settings page
* `[Added]` Support for AUTO_INCREMENT field in database other than 1
* `[Added]` Possibility of permanent turn off admin notice

= 3.0.1 (2018-04-18) =
* `[Removed]` Support for old WordPress versions
* `[Fixed]` Other changes

= 3.0.0 (2018-04-13) =
* `[Changed]` Plugin structure
* `[Added]` Support for `get_posts()` function
* `[Added]` Search in Admin Panel
* `[Added]` Support for internationalization

= 2.2.0 (2018-02-26) =
* `[Added]` Cleaning database after removing plugin

= 2.1.3 (2018-01-30) =
* `[Fixed]` Compatibility for Polylang plugin

= 2.1.2 (2018-01-15) =
* `[Added]` Support for File type field

= 2.1.1 (2017-12-21) =
* `[Changed]` Admin notice

= 2.1.0 (2017-11-06) =
* `[Changed]` Small changes
* `[Added]` Lite mode for faster seach

= 2.0.7 (2017-08-24) =
* `[Fixed]` Turn off plugin core while searching uploads media items

= 2.0.6 (2017-08-20) =
* `[Added]` Support for WordPress Multisite

= 2.0.5 (2017-07-25) =
* `[Fixed]` Closing notice in Admin panel

= 2.0.4 (2017-07-20) =
* `[Changed]` Access to settings page
* `[Added]` Support for WP AJAX

= 2.0.3 (2017-07-15) =
* `[Fixed]` Text search with apostrophe and quotation marks

= 2.0.2 (2017-06-29) =
* `[Removed]` Support for `get_posts()`

= 2.0.1 (2017-06-16) =
* `[Fixed]` Support for PHP 7

= 2.0.0 (2017-06-15) =
* `[Changed]` Search engine
* `[Changed]` Settings page
* `[Added]` Notifications in admin panel
* `[Added]` The ability to search whole phrases

= 1.0.0 (2016-12-26) =
* The first stable release
