<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'AcfBetterSearch\\AcfBetterSearch' => $baseDir . '/src/AcfBetterSearch.php',
    'AcfBetterSearch\\Admin\\Assets' => $baseDir . '/src/Admin/Assets.php',
    'AcfBetterSearch\\Admin\\Install' => $baseDir . '/src/Admin/Install.php',
    'AcfBetterSearch\\Admin\\Plugin' => $baseDir . '/src/Admin/Plugin.php',
    'AcfBetterSearch\\Admin\\Uninstall' => $baseDir . '/src/Admin/Uninstall.php',
    'AcfBetterSearch\\Helper\\ViewLoader' => $baseDir . '/src/Helper/ViewLoader.php',
    'AcfBetterSearch\\HookableInterface' => $baseDir . '/src/HookableInterface.php',
    'AcfBetterSearch\\Notice\\AcfRequiredNotice' => $baseDir . '/src/Notice/AcfRequiredNotice.php',
    'AcfBetterSearch\\Notice\\ConverterPluginNotice' => $baseDir . '/src/Notice/ConverterPluginNotice.php',
    'AcfBetterSearch\\Notice\\NoticeAbstract' => $baseDir . '/src/Notice/NoticeAbstract.php',
    'AcfBetterSearch\\Notice\\NoticeIntegration' => $baseDir . '/src/Notice/NoticeIntegration.php',
    'AcfBetterSearch\\Notice\\NoticeInterface' => $baseDir . '/src/Notice/NoticeInterface.php',
    'AcfBetterSearch\\Notice\\ThanksNotice' => $baseDir . '/src/Notice/ThanksNotice.php',
    'AcfBetterSearch\\PluginInfo' => $baseDir . '/src/PluginInfo.php',
    'AcfBetterSearch\\Search\\Init' => $baseDir . '/src/Search/Init.php',
    'AcfBetterSearch\\Search\\Join' => $baseDir . '/src/Search/Join.php',
    'AcfBetterSearch\\Search\\Query' => $baseDir . '/src/Search/Query.php',
    'AcfBetterSearch\\Search\\Request' => $baseDir . '/src/Search/Request.php',
    'AcfBetterSearch\\Search\\Where' => $baseDir . '/src/Search/Where.php',
    'AcfBetterSearch\\Settings\\Acf' => $baseDir . '/src/Settings/Acf.php',
    'AcfBetterSearch\\Settings\\Config' => $baseDir . '/src/Settings/Config.php',
    'AcfBetterSearch\\Settings\\Options' => $baseDir . '/src/Settings/Options.php',
    'AcfBetterSearch\\Settings\\Page' => $baseDir . '/src/Settings/Page.php',
    'AcfBetterSearch\\Settings\\Save' => $baseDir . '/src/Settings/Save.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
);
