<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    name="Yoast SEO ACF Analysis"
    xsi:noNamespaceSchemaLocation="./vendor/squizlabs/php_codesniffer/phpcs.xsd">

	<description>Yoast SEO ACF Analysis Coding Standards</description>

	<!--
	#############################################################################
	COMMAND LINE ARGUMENTS
	https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki/Annotated-ruleset.xml
	#############################################################################
	-->

	<file>.</file>

	<exclude-pattern>tests/js/system/data/acf*\.php$</exclude-pattern><!-- Code exported from ACF. -->
	<exclude-pattern>tests/Unit/Doubles/acf\.php$</exclude-pattern><!-- ACF mock class. -->

	<!-- Only check PHP files. -->
	<arg name="extensions" value="php"/>

	<!-- Show progress, show the error codes for each message (source). -->
	<arg value="ps"/>

	<!-- Strip the filepaths down to the relevant bit. -->
	<arg name="basepath" value="./"/>

	<!-- Check up to 8 files simultaneously. -->
	<arg name="parallel" value="8"/>

	<!-- Cache the results between runs. -->
	<arg name="cache" value="./.cache/phpcs.cache"/>


	<!--
	#############################################################################
	USE THE YoastCS RULESET
	#############################################################################
	-->

	<rule ref="Yoast">
		<properties>
			<!-- Provide the plugin specific prefixes for all naming related sniffs. -->
			<property name="prefixes" type="array">
				<element value="Yoast\WP\ACF"/>
				<element value="yoast_acf"/>
			</property>

			<property name="psr4_paths" type="array">
				<element key="Yoast\WP\ACF\Tests\\" value="tests/"/>
			</property>
		</properties>
	</rule>


	<!--
	#############################################################################
	SNIFF SPECIFIC CONFIGURATION
	#############################################################################
	-->

	<!-- Verify that all gettext calls use the correct text domain. -->
	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="acf-content-analysis-for-yoast-seo"/>
			</property>
		</properties>
	</rule>

	<rule ref="Yoast.Files.FileName">
		<properties>
			<!-- Don't trigger on the main file as renaming it would deactivate the plugin. -->
			<property name="excluded_files_strict_check" type="array">
				<element value="yoast-acf-analysis.php"/>
			</property>

			<!-- Remove the following prefixes from the names of object structures. -->
			<property name="oo_prefixes" type="array">
				<element value="Yoast_ACF_Analysis"/>
				<element value="yoast_acf"/>
			</property>
		</properties>
	</rule>


	<!--
	#############################################################################
	TEMPORARY ADJUSTMENTS
	Adjustments which should be removed once the associated issue has been resolved.
	#############################################################################
	-->

	<!-- Until all prefixes are fixed, some exceptions are allowed to the PrefixAllGlobals sniff. -->
	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array" extend="true">
				<element value="AC_Yoast"/>
				<element value="AC_SEO"/>
			</property>
		</properties>
	</rule>

	<!-- Namespaced test classes imported via a use statement are not yet correctly excluded
		 by WPCS. -->
	<rule ref="WordPress.NamingConventions.PrefixAllGlobals.NonPrefixedConstantFound">
		<exclude-pattern>/tests/Unit/Dependencies/Dependency_Yoast_SEO_Test\.php$</exclude-pattern>
	</rule>

</ruleset>
