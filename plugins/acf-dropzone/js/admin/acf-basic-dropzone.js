!function(){var e={};(function(t){(function(){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o,r=(o="undefined"!=typeof window?window.jQuery:void 0!==t?t.jQuery:null)&&o.__esModule?o:{default:o};function i(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(void 0,i=function(e,t){if("object"!==n(e)||null===e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,"string");if("object"!==n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(r.key),"symbol"===n(i)?i:String(i)),r)}var i}var a,f,l,d=(a=function e(t){var n=t.el;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!(0,r.default)(n).is(".acf-dropzone-inited")){var o=(0,r.default)(n).find('[type="file"]').get(0);(0,r.default)(n).addClass("acf-dropzone-inited").on("dragover",(function(e){(0,r.default)(n).addClass("drag-over"),e.preventDefault()})).on("dragleave",(function(e){(0,r.default)(n).removeClass("drag-over")})).on("drop",(function(e){(0,r.default)(n).removeClass("drag-over"),e.preventDefault(),o.files=e.originalEvent.dataTransfer.files}))}},f&&i(a.prototype,f),l&&i(a,l),Object.defineProperty(a,"prototype",{writable:!1}),a);e=d}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var t,n=(t=e)&&t.__esModule?t:{default:t},o=function(e){var t;e.$el.is(".dropzone")&&(e.parent(),(t=e.$('[data-uploader="basic"]').get(0))&&new n.default({el:t,field:e}))};acf_dropzone.file_fields.forEach((function(e){acf.addAction("ready_field/type=".concat(e),o),acf.addAction("append_field/type=".concat(e),o)}))}();