.widget.open {
    z-index: inherit !important
}

.menu-item-settings {
    position: initial
}

.acf-meta-box-wrap.-grid #acf-admin-tool-acfe-fg-local {
    display: none
}

.acf-tab-group li a .acfe-tab-badge {
    border-radius: 100px;
    background: #ddd;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: inline-block;
    vertical-align: text-bottom;
    padding: 0;
    text-align: center;
    margin-left: 5px;
    line-height: 18px
}

.acf-tab-group li a:hover .acfe-tab-badge,
.acf-tab-group li.active a .acfe-tab-badge {
    background: #f1f1f1
}

.acf-field>.acf-input>.acf-input-wrap {
    overflow: initial
}

.acf-field>.acf-input>.acf-input-append+.acf-input-wrap,
.acf-field>.acf-input>.acf-input-prepend+.acf-input-wrap {
    overflow: hidden
}

.post-type-attachment #post-body-content #acf_after_title-sortables {
    margin: 20px 0 0
}

.plugins tr.acfe-plugin-tr.acfe-plugin-tr-update td {
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.plugins tr.acfe-plugin-tr.acfe-plugin-tr-update .update-message {
    margin-bottom: 0
}

.acf-admin-toolbar i.acf-icon,
.acf-btn i.acf-icon {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    width: 20px;
    height: 20px;
    background-color: currentColor;
    border: none;
    border-radius: 0;
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    text-indent: 500%;
    white-space: nowrap;
    overflow: hidden
}

body:not(.acf-admin-single-field-group) .acf-tooltip {
    font-size: 13px
}

@media only screen and (max-width:1300px) {
    .acf-admin-toolbar i.acf-icon {
        display: none !important
    }
}

code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
    font-size: 1em
}

pre {
    display: block;
    padding: 9.5px;
    margin: 0;
    line-height: 1.42857143;
    color: #333;
    word-break: break-all;
    word-wrap: break-word;
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 1px;
    white-space: pre-wrap;
    overflow: auto
}

pre>code {
    -webkit-user-select: inherit;
    -moz-user-select: inherit;
    -ms-user-select: inherit;
    user-select: inherit;
    padding: 0;
    margin: 0;
    background: 0 0;
    font-size: 1em;
    display: block
}

code {
    -webkit-user-select: all;
    -moz-user-select: all;
    user-select: all
}

code[contenteditable=true] {
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
    user-select: auto
}

.code-codemirror>.CodeMirror,
.pre-codemirror>.CodeMirror {
    border: 1px solid #ccc;
    height: auto;
    width: auto;
    background: #f9f9f9;
    padding: 9px 6px
}

.code-codemirror>.CodeMirror .CodeMirror-selected,
.pre-codemirror>.CodeMirror .CodeMirror-selected {
    background: #ddd !important
}

.code-codemirror>.CodeMirror .CodeMirror-lines,
.pre-codemirror>.CodeMirror .CodeMirror-lines {
    padding: 0
}

.code-codemirror {
    display: inline;
    vertical-align: middle
}

.code-codemirror>.CodeMirror {
    display: inline-block;
    padding: 0
}

.acfe-pre-highlight {
    display: block;
    padding: 9.5px;
    margin: 0;
    line-height: 1.42857143;
    color: #333;
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 1px;
    overflow: auto;
    font-family: Menlo, Monaco, Consolas, Courier New, monospace;
    font-size: 1em;
    white-space: nowrap
}

.acf-columns-2 {
    margin-right: 300px;
    clear: both
}

.acf-columns-2:after {
    display: block;
    clear: both;
    content: ""
}

.acf-columns-2 .acf-column-1 {
    float: left;
    width: 100%
}

.acf-columns-2 .acf-column-2 {
    float: right;
    margin-right: -300px;
    width: 280px
}

html[dir=rtl] .acf-columns-2 {
    margin-right: 0;
    margin-left: 300px
}

html[dir=rtl] .acf-columns-2 .acf-column-1 {
    float: right
}

html[dir=rtl] .acf-columns-2 .acf-column-2 {
    float: left;
    margin-right: 0;
    margin-left: -300px
}

@media only screen and (max-width:850px) {
    .acf-columns-2 {
        margin-right: 0
    }

    .acf-columns-2 .acf-column-1,
    .acf-columns-2 .acf-column-2 {
        float: none;
        width: auto;
        margin: 0
    }
}

.misc-pub-acfe-object-category::before,
.misc-pub-acfe-object-data::before,
.misc-pub-acfe-object-id::before,
.misc-pub-acfe-object-meta::before,
.misc-pub-acfe-object-performance::before,
.misc-pub-acfe-object-role::before,
.misc-pub-acfe-object-type::before,
.misc-pub-acfe-object::before {
    font: normal 19px/1 dashicons;
    speak: never;
    display: inline-block;
    margin-left: -1px;
    padding-right: 3px;
    vertical-align: top;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #8c8f94;
    content: "\f173";
    margin-right: 1px
}

.misc-pub-acfe-object-id::before {
    content: "\f464"
}

.misc-pub-acfe-object-type::before {
    content: "\f109"
}

.misc-pub-acfe-object-role::before {
    content: "\f338"
}

.misc-pub-acfe-object-category::before {
    content: "\f318"
}

.misc-pub-acfe-object-data::before {
    content: "\f206"
}

.misc-pub-acfe-object-meta::before {
    content: "\f17e"
}

.misc-pub-acfe-object-performance::before {
    content: "\f226"
}

.misc-pub-acfe-yes::before {
    content: "\f147";
    font-size: 24px;
    line-height: 20px;
    width: 18px;
    margin-left: -3px
}

.misc-pub-acfe-no::before {
    content: "\f335";
    font-size: 22px;
    width: 15px
}

.misc-pub-acfe-import::before {
    content: "\f17b";
    font-size: 19px;
    width: 15px;
    margin-left: 1px
}

.misc-pub-acfe-info::before {
    content: "\f14c";
    font-size: 17px;
    line-height: 19px;
    width: 13px;
    margin-left: 2px
}

.misc-pub-acfe-dashboard::before {
    content: "\f226";
    font-size: 18px;
    line-height: 19px;
    width: 14px;
    margin-left: 1px
}

.misc-pub-acfe-warning::before {
    content: "\f534";
    font-size: 18px;
    line-height: 19px;
    width: 14px;
    margin-left: 1px
}

.acfe-list-postboxes .postbox>.postbox-header>h2.hndle {
    cursor: initial
}

.acfe-list-postboxes .postbox>.postbox-header>.handle-actions>.acf-hndle-cog {
    width: 2.2rem
}

.acfe-list-postboxes .postbox>.postbox-header>.handle-actions>button,
.acfe-list-postboxes .postbox>.postbox-header>.handle-actions>span {
    display: none
}

.acfe-list-postboxes .postbox>.acf-fields>#acf-form-data+.acf-field {
    border-top: none;
    margin-top: 0
}

.acfe-list-postboxes.-side {
    min-width: auto !important
}

#acfe-clean-meta>.inside {
    margin: 0 !important;
    padding: 15px 12px !important
}

.postbox#acfe-acf-custom-fields>.inside,
.postbox#acfe-wp-custom-fields>.inside {
    padding: 0;
    margin: 0
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table {
    border: 0
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table .col-name,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table .col-name {
    width: 30%
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table .col-value,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table .col-value {
    width: auto
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table .col-field-type,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table .col-field-type {
    width: 100px
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table .col-field-group,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table .col-field-group {
    width: 120px
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table .col-autoload,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table .col-autoload {
    width: 65px
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table tbody tr,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table tbody tr {
    -webkit-transition: background-color .5s linear;
    -o-transition: background-color .5s linear;
    transition: background-color .5s linear
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table tbody tr.updated,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table tbody tr.updated {
    background: rgba(34, 113, 177, .2)
}

.postbox#acfe-acf-custom-fields>.inside .wp-list-table tbody tr.deleted,
.postbox#acfe-wp-custom-fields>.inside .wp-list-table tbody tr.deleted {
    background: #faafaa
}

.postbox#acfe-acf-custom-fields+.acfe-dev-bulk,
.postbox#acfe-wp-custom-fields+.acfe-dev-bulk {
    padding-top: 0;
    margin-top: -8px;
    margin-bottom: 20px
}

.postbox#acfe-acf-custom-fields+.acfe-dev-bulk:last-child,
.postbox#acfe-wp-custom-fields+.acfe-dev-bulk:last-child {
    margin-bottom: 0
}

.postbox#acfe-acf-custom-fields em,
.postbox#acfe-wp-custom-fields em {
    color: #aaa
}

.postbox#acfe-acf-custom-fields .acfe-dev-meta-count,
.postbox#acfe-wp-custom-fields .acfe-dev-meta-count {
    background: #72777c;
    padding: 1px 5px;
    border-radius: 4px;
    color: #fff;
    margin-left: 7px;
    font-size: 12px;
    margin-right: auto
}

.postbox#acfe-acf-custom-fields pre,
.postbox#acfe-wp-custom-fields pre {
    max-height: 200px;
    overflow: auto;
    white-space: pre
}

.postbox#acfe-acf-custom-fields pre.raw,
.postbox#acfe-wp-custom-fields pre.raw {
    white-space: unset;
    margin-top: 10px;
    max-width: 100%
}

@media only screen and (max-width:1100px) {

    .postbox#acfe-acf-custom-fields thead,
    .postbox#acfe-wp-custom-fields thead {
        display: none
    }

    .postbox#acfe-acf-custom-fields tbody tr,
    .postbox#acfe-wp-custom-fields tbody tr {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }

    .postbox#acfe-acf-custom-fields tbody td,
    .postbox#acfe-acf-custom-fields tbody th,
    .postbox#acfe-wp-custom-fields tbody td,
    .postbox#acfe-wp-custom-fields tbody th {
        display: block
    }

    .postbox#acfe-acf-custom-fields tbody td:first-of-type,
    .postbox#acfe-wp-custom-fields tbody td:first-of-type {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    @media only screen and (max-width:850px) {

        .postbox#acfe-acf-custom-fields tbody td:first-of-type,
        .postbox#acfe-wp-custom-fields tbody td:first-of-type {
            line-height: 2.2
        }
    }

    .postbox#acfe-acf-custom-fields tbody td:first-of-type strong,
    .postbox#acfe-wp-custom-fields tbody td:first-of-type strong {
        display: inline-block;
        word-break: break-all
    }

    .postbox#acfe-acf-custom-fields tbody td:first-of-type .row-actions,
    .postbox#acfe-wp-custom-fields tbody td:first-of-type .row-actions {
        display: inline-block;
        padding: 0;
        float: right
    }

    @media only screen and (max-width:850px) {

        .postbox#acfe-acf-custom-fields tbody td:first-of-type .row-actions,
        .postbox#acfe-wp-custom-fields tbody td:first-of-type .row-actions {
            left: auto
        }
    }

    .postbox#acfe-acf-custom-fields tbody td:first-of-type .row-actions .delete,
    .postbox#acfe-acf-custom-fields tbody td:first-of-type .row-actions a,
    .postbox#acfe-wp-custom-fields tbody td:first-of-type .row-actions .delete,
    .postbox#acfe-wp-custom-fields tbody td:first-of-type .row-actions a {
        padding: 0
    }

    .postbox#acfe-acf-custom-fields tbody td:nth-child(3),
    .postbox#acfe-wp-custom-fields tbody td:nth-child(3) {
        -webkit-box-flex: 1;
        -ms-flex: 1 1 100%;
        flex: 1 1 100%
    }
}

.acf-settings-wrap .acf-box {
    margin: 16px 0
}

.acf-box .title {
    padding: 8px 12px
}

.acf-box .title h3 {
    line-height: 1.48
}

.acfe-module.acfe-module-post #minor-publishing-actions,
.acfe-module.acfe-module-post #misc-publishing-actions #visibility,
.acfe-module.acfe-module-post #misc-publishing-actions .edit-post-status,
.acfe-module.acfe-module-post #misc-publishing-actions .edit-timestamp {
    display: none !important
}

.acfe-module.acfe-module-post .acfe-misc-export {
    padding-top: 2px
}

.acfe-module.acfe-module-post .acfe-misc-export span.dashicons {
    font-size: 18px;
    color: #82878c;
    line-height: 1.2;
    width: 18px;
    margin-right: 4px
}

.acfe-module.acfe-module-post.post-new-php .acfe-misc-export {
    display: none
}

.acfe-module.acfe-module-posts .status-acf-disabled .column-title a,
.acfe-module.acfe-module-posts .status-acf-disabled .column-title code {
    color: #555 !important
}

.acfe-module.acfe-module-posts .status-acf-disabled .column-title strong {
    color: #a0a5aa
}

.acfe-module.acfe-module-posts .column-acfe-autoload,
.acfe-module.acfe-module-posts .column-acfe-position,
.acfe-module.acfe-module-posts .column-acfe-posts,
.acfe-module.acfe-module-posts .column-acfe-terms {
    width: 120px
}

.acfe-module.acfe-module-posts .column-acfe-post-id {
    width: 200px
}

.acfe-module.acfe-module-posts .column-acfe-name {
    width: 15%
}

.acfe-module.acfe-module-posts .column-acfe-load {
    width: 70px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.acfe-module.acfe-module-posts .column-acfe-autosync-json,
.acfe-module.acfe-module-posts .column-acfe-autosync-php {
    width: 90px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

ul.acfe-module-export-choices {
    -webkit-column-width: 200px;
    -moz-column-width: 200px;
    column-width: 200px
}

.wrap .acfe-edit-module-button {
    display: inline-block;
    padding-left: 4px;
    padding-right: 4px
}

.wrap .acfe-edit-module-button:active {
    padding-left: 4px;
    padding-right: 4px
}

.wrap .acfe-edit-module-button span {
    font-size: 16px;
    vertical-align: text-top;
    height: 15px;
    width: 19px;
    line-height: 19px
}

#acfe-author>.inside {
    margin: 0;
    padding: 0
}

#acfe-author>.inside>.acf-field {
    margin: 0;
    padding: 15px 12px
}

.wp-list-table .column-acfe-load {
    width: 70px
}

.wp-list-table .column-acfe-source {
    width: 150px
}

.wp-list-table .column-acfe-autosync-json,
.wp-list-table .column-acfe-autosync-php {
    width: 90px;
    text-align: center !important
}

.wp-list-table .column-acfe-autosync-json .secondary,
.wp-list-table .column-acfe-autosync-php .secondary {
    color: #ccc
}

.wp-list-table .column-acfe-autosync-json .dashicons-warning,
.wp-list-table .column-acfe-autosync-php .dashicons-warning {
    font-size: 15px;
    vertical-align: middle
}

.wp-list-table .column-acfe-autosync-json .dashicons-update,
.wp-list-table .column-acfe-autosync-php .dashicons-update {
    font-size: 18px
}

.wp-list-table .column-acfe-autosync-json .dashicons+.dashicons-warning,
.wp-list-table .column-acfe-autosync-php .dashicons+.dashicons-warning {
    margin-left: -3px
}

.wp-list-table .column-acfe-autosync-json a:focus,
.wp-list-table .column-acfe-autosync-php a:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.acf-field[data-name=acfe_autosync] .dashicons-warning {
    color: #ccc;
    font-size: 15px;
    height: 15px;
    vertical-align: text-bottom
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=fields]>.acf-input>.acf-fields>.acf-field,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=form]>.acf-input>.acf-fields>.acf-field {
    border: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=form]>.acf-input>.acf-fields>.acf-field>.acf-input>.acf-input-prepend {
    width: 82px
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=form]>.acf-input>.acf-fields>.acf-field[data-name=class],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=form]>.acf-input>.acf-fields>.acf-field[data-name=id] {
    padding-left: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=fields]>.acf-input>.acf-fields>.acf-field>.acf-input>.acf-input-prepend {
    width: 82px
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=fields]>.acf-input>.acf-fields>.acf-field[data-name=class],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=fields]>.acf-input>.acf-fields>.acf-field[data-name=instruction],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=fields]>.acf-input>.acf-fields>.acf-field[data-name=wrapper_class] {
    padding-left: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=fields]>.acf-input>.acf-fields>.acf-field[data-name=instruction],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=fields]>.acf-input>.acf-fields>.acf-field[data-name=label] {
    padding-top: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-type=acfe_dynamic_render][data-key*="_doc"] {
    padding: 0 !important;
    border: 0 !important;
    position: absolute !important;
    top: 12px;
    right: 11px;
    z-index: 3
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-type=acfe_dynamic_render][data-key*="_doc"]::before {
    background: 0 0 !important;
    border: 0 !important
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-type=acfe_dynamic_render][data-key*="_doc"]>.acf-label {
    min-height: 0 !important
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-type=acfe_dynamic_render][data-key*="_doc"]>.acf-input {
    float: none !important;
    width: auto !important;
    padding: 0 !important
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-type=acfe_dynamic_render][data-key*="_doc"]+.acf-field,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-type=acfe_dynamic_render][data-key*="_doc"]+.acf-tab-wrap>.acf-tab-group {
    border-top: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields] {
    padding: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields]>.acf-label,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields]>.acf-label {
    padding: 15px 12px
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields]>.acf-input,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields]>.acf-input {
    padding: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields]>.acf-input>ul,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields]>.acf-input>ul {
    border: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields]>.acf-input>ul>li>strong,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields]>.acf-input>ul>li>strong {
    display: block;
    padding: 15px 12px 8px;
    line-height: 1.4;
    margin-top: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields]>.acf-input>ul>li>ul>li label,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields]>.acf-input>ul>li>ul>li label {
    display: block;
    padding: 8px 12px;
    border-bottom: 1px solid #e1e1e1
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields]>.acf-input>ul>li>ul>li:last-child label,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields]>.acf-input>ul>li>ul>li:last-child label {
    border-bottom: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=load_acf_fields]>.acf-input>ul>li>ul>li input,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_acf_fields]>.acf-input>ul>li>ul>li input {
    margin-right: 7px
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field>.acf-input.acfe-display-related-message>:not(.related-message):not(.append-terms) {
    display: none
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field>.acf-input.acfe-display-related-message .related-message {
    font-size: 14px
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_post_terms]>.acf-input {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_post_terms]>.acf-input .select2.select2-container {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
    width: 83% !important
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_post_terms]>.acf-input .related-message {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_post_terms]>.acf-input .acf-input.append-terms {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    margin-top: 3px;
    width: 17%;
    padding-left: 15px
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_post_terms]>.acf-input.acfe-display-related-message .acf-input.append-terms {
    margin-top: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=save_post_terms]>.acf-input.acfe-display-related-message .related-message {
    width: 83%
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=content_editor],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=content_html],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=description_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=parent_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=post_author_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=post_content_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=post_date_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=post_excerpt_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=post_parent_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=post_thumbnail_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=source_custom],
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=target_custom] {
    border-top: 0;
    padding-top: 0
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=files]>.acf-input .acf-actions,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=files_static]>.acf-input .acf-actions {
    text-align: left
}

.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=files]>.acf-input .acf-actions .acf-button,
.acfe-module.acfe-module-form.acfe-module-post .acf-field[data-name=files_static]>.acf-input .acf-actions .acf-button {
    float: none
}

.acfe-module.acfe-module-form.acfe-module-post .select2-results__options .loading-results,
.acfe-module.acfe-module-form.acfe-module-post .select2-results__options .select2-results__message {
    display: none
}

.settings_page_acfe-options .column-option_id {
    width: 65px
}

.settings_page_acfe-options .column-option_name {
    width: 435px
}

.settings_page_acfe-options .column-autoload {
    width: 100px;
    text-align: center
}

#acfe-admin-settings .inside>.acf-field {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0
}

#acfe-admin-settings .inside>.acf-field>.acf-label {
    padding-top: 15px;
    padding-bottom: 15px;
    float: none;
    display: block
}

#acfe-admin-settings .inside>.acf-field>.acf-input {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    float: none;
    padding: 0
}

#acfe-admin-settings .inside>.acf-field>.acf-input>div {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 50%;
    flex: 1 1 50%;
    max-width: 50%;
    padding: 8px;
    min-width: 0
}

#acfe-admin-settings .inside>.acf-field>.acf-input>div:nth-child(2) {
    border-left: 1px solid #eee
}

#acfe-admin-settings .inside>.acf-field>.acf-input .acfe-settings-text {
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
    display: inline-block
}

#acfe-admin-settings .inside>.acf-field>.acf-input .acfe-settings-text>code {
    font-size: 12px
}

#acfe-admin-settings .inside>.acf-field>.acf-input .dashicons-saved {
    font-size: 21px;
    display: inline
}

#acfe-admin-settings .inside>.acf-field>.acf-input .dashicons-no-alt {
    color: #ccc;
    font-size: 21px;
    display: inline
}

#acfe-admin-settings .inside>.acf-field.acfe-settings-thead>.acf-input {
    padding: 0;
    border-bottom: 1px solid #eee
}

#acfe-admin-settings .inside>.acf-field.acfe-settings-thead+.acf-field {
    border-top: 0
}

.acf-postbox.seamless>.inside>.acf-field {
    padding: 15px 12px
}

.acf-flexible-content .layout.ui-sortable-helper:first-child {
    margin-top: 20px
}

.acf-flexible-content .layout.ui-sortable-helper:first-child+.layout.ui-sortable-placeholder {
    margin-top: 0
}

.acf-fields.-top>.acf-field-flexible-content>.acf-input>.acf-flexible-content>.values>.layout.ui-sortable-helper:first-child {
    margin-top: 10px
}

.acfe-postbox.acfe-postbox-no-handle h2.hndle {
    cursor: initial
}

.acfe-postbox.acfe-postbox-no-handle .handlediv {
    display: none
}

.acfe-postbox-top>.inside {
    position: relative
}

.acfe-postbox-top>.inside.-border {
    border: #ccd0d4 solid 1px;
    background: #fff
}

.acfe-postbox-top>.inside>.acf-field {
    position: relative;
    margin: 0;
    padding: 15px 12px;
    border-top: #eee solid 1px
}

.acfe-postbox-top>.inside>.acf-field:first-child {
    border-top: none;
    margin-top: 0
}

.acfe-postbox-left>.inside {
    position: relative
}

.acfe-postbox-left>.inside.-border {
    border: #ccd0d4 solid 1px;
    background: #fff
}

.acfe-postbox-left>.inside:after {
    display: block;
    clear: both;
    content: ""
}

.acfe-postbox-left>.inside>.acf-field {
    position: relative;
    margin: 0;
    border-top: #eee solid 1px;
    padding: 15px 0
}

.acfe-postbox-left>.inside>.acf-field:first-child {
    border-top: none;
    margin-top: 0
}

.acfe-postbox-left>.inside>.acf-field:after {
    display: block;
    clear: both;
    content: ""
}

.acfe-postbox-left>.inside>.acf-field:before {
    content: "";
    display: block;
    position: absolute;
    z-index: 0;
    background: #f9f9f9;
    border-color: #e1e1e1;
    border-style: solid;
    border-width: 0 1px 0 0;
    top: 0;
    bottom: 0;
    left: 0;
    width: 20%
}

.acfe-postbox-left>.inside>.acf-field[data-width] {
    float: none;
    width: auto !important;
    border-left-width: 0 !important;
    border-right-width: 0 !important
}

.acfe-postbox-left>.inside>.acf-field>.acf-label {
    float: left;
    width: 20%;
    margin: 0;
    padding: 0 12px
}

.acfe-postbox-left>.inside>.acf-field>.acf-input {
    float: left;
    width: 80%;
    margin: 0;
    padding: 0 12px
}

.acfe-postbox-left>.inside.-clear>.acf-field {
    border: none;
    padding: 0;
    margin: 15px 0
}

html[dir=rtl] .acfe-postbox-left>.inside>.acf-field:before {
    border-width: 0 0 0 1px;
    left: auto;
    right: 0
}

html[dir=rtl] .acfe-postbox-left>.inside>.acf-field>.acf-label {
    float: right
}

html[dir=rtl] .acfe-postbox-left>.inside>.acf-field>.acf-input {
    float: right
}

#side-sortables .acfe-postbox-left>.inside>.acf-field:before {
    display: none
}

#side-sortables .acfe-postbox-left>.inside>.acf-field>.acf-label {
    width: 100%;
    margin-bottom: 10px
}

#side-sortables .acfe-postbox-left>.inside>.acf-field>.acf-input {
    width: 100%
}

@media screen and (max-width:640px) {
    .acfe-postbox-left>.inside>.acf-field:before {
        display: none
    }

    .acfe-postbox-left>.inside>.acf-field>.acf-label {
        width: 100%;
        margin-bottom: 10px
    }

    .acfe-postbox-left>.inside>.acf-field>.acf-input {
        width: 100%
    }
}

@media screen and (min-width:783px) {

    .postbox .handle-order-higher,
    .postbox .handle-order-lower {
        visibility: hidden
    }

    .postbox:hover .handle-order-higher,
    .postbox:hover .handle-order-lower {
        visibility: visible
    }
}

body.acfe-fix-postboxes .postbox .handle-actions .acf-hndle-cog {
    line-height: 28px;
    height: 26px
}

.postbox .handlediv {
    width: 1.62rem
}

.postbox .handlediv .toggle-indicator::before {
    text-indent: -7px;
    top: .1rem
}

.postbox .handle-order-higher,
.postbox .handle-order-lower {
    vertical-align: bottom;
    width: 1.42rem
}

.postbox .handle-order-higher .order-higher-indicator::before,
.postbox .handle-order-higher .order-lower-indicator::before,
.postbox .handle-order-lower .order-higher-indicator::before,
.postbox .handle-order-lower .order-lower-indicator::before {
    font-size: 15px;
    top: .23rem
}

#poststuff .stuffbox>h3,
#poststuff h2,
#poststuff h3.hndle {
    line-height: 1.48
}

body.is-dragging-metaboxes #acf_after_title-sortables {
    outline: 3px dashed #646970;
    display: flow-root;
    min-height: 60px;
    margin-bottom: 3px !important
}

.edit-post-layout__metaboxes:not(:empty) {
    background: #f3f4f5;
    padding: 10px 10px 0 10px !important
}

.edit-post-layout__metaboxes .edit-post-meta-boxes-area .postbox {
    margin-bottom: 10px;
    border: 1px solid #e1e1e1
}

.edit-post-layout__metaboxes .edit-post-meta-boxes-area .postbox>.postbox-header {
    border-top: 0;
    border-bottom: 1px solid #ddd
}

.edit-post-layout__metaboxes .edit-post-meta-boxes-area .postbox>.inside {
    border-bottom: 0
}

.edit-post-layout__metaboxes .edit-post-meta-boxes-area .postbox.closed>.postbox-header {
    border-bottom: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single {
    border-radius: 3px;
    height: 30px;
    outline: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single .select2-selection__rendered {
    font-size: 14px;
    height: 28px;
    line-height: 27px;
    padding-right: 23px
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single .select2-selection__clear {
    line-height: 26px;
    height: 28px;
    font-size: 16px;
    margin-right: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single button.select2-selection__clear {
    position: absolute;
    right: 24px;
    padding: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single button.select2-selection__clear span {
    line-height: 26px;
    height: 28px;
    display: inline-block
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single .select2-selection__placeholder {
    color: #444
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single .select2-selection__arrow {
    height: 28px
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--single .select2-selection__arrow b {
    background: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E') no-repeat;
    background-size: 16px 16px;
    border: 0;
    width: 16px;
    height: 16px;
    margin-left: -11px;
    margin-top: -7px
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf.select2-container--open .select2-selection--single,
body:not(.acf-admin-6) .acf-field .select2-container.-acf:focus .select2-selection--single {
    border-color: #007cba;
    color: #016087;
    -webkit-box-shadow: 0 0 0 1px #007cba;
    box-shadow: 0 0 0 1px #007cba
}

body:not(.acf-admin-6) .select2-container .select2-dropdown {
    margin-top: 0;
    border-color: #aaa !important
}

body:not(.acf-admin-6) .select2-container .select2-dropdown .select2-search--dropdown .select2-search__field {
    min-height: 30px;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0 2px
}

body:not(.acf-admin-6) .select2-container .select2-dropdown .select2-search--dropdown .select2-search__field::-webkit-input-placeholder {
    color: #777
}

body:not(.acf-admin-6) .select2-container .select2-dropdown .select2-search--dropdown .select2-search__field::-moz-placeholder {
    color: #777
}

body:not(.acf-admin-6) .select2-container .select2-dropdown .select2-search--dropdown .select2-search__field:-ms-input-placeholder {
    color: #777
}

body:not(.acf-admin-6) .select2-container .select2-dropdown .select2-search--dropdown .select2-search__field::-ms-input-placeholder {
    color: #777
}

body:not(.acf-admin-6) .select2-container .select2-dropdown .select2-search--dropdown .select2-search__field::placeholder {
    color: #777
}

body:not(.acf-admin-6) .select2-container .select2-dropdown .select2-results__option {
    margin-bottom: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple {
    min-height: 30px;
    line-height: 1;
    border-radius: 3px;
    padding: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__rendered {
    padding: 0 2px;
    display: inline-block;
    margin: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__choice {
    margin-right: 2px;
    margin-top: 2px;
    line-height: 1.6;
    border-radius: 3px;
    font-size: 14px;
    margin-left: 0;
    display: inline-block;
    vertical-align: unset;
    padding: 0 5px
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__choice .select2-selection__choice__display {
    padding: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove {
    float: right;
    margin-left: 3px;
    margin-right: 0;
    line-height: 23px;
    font-size: 15px;
    vertical-align: text-top;
    position: static;
    padding: 0;
    border: 0;
    margin-top: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__choice .select2-selection__choice__remove:hover {
    background: 0 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__clear {
    margin-top: 0;
    margin-right: 6px;
    font-size: 16px;
    padding: 0;
    line-height: 26px;
    height: 27px;
    position: relative
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple button.select2-selection__clear {
    float: right
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline {
    float: left;
    width: auto
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline .select2-search__field {
    margin-top: 4px;
    font-size: 14px;
    padding-left: 2px;
    margin-left: 0
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline .select2-search__field::-webkit-input-placeholder {
    color: #444
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline .select2-search__field::-moz-placeholder {
    color: #444
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline .select2-search__field:-ms-input-placeholder {
    color: #444
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline .select2-search__field::-ms-input-placeholder {
    color: #444
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline .select2-search__field::placeholder {
    color: #444
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-search--inline:first-child:last-child .select2-search__field {
    padding-left: 6px
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__rendered+.select2-search--inline {
    float: none;
    display: inline-block;
    vertical-align: top
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf .select2-selection--multiple .select2-selection__rendered+.select2-search--inline .select2-search__field {
    height: 22px;
    line-height: 1.4;
    padding-left: 0;
    font-family: inherit;
    border-radius: 0;
    margin-top: 4px !important
}

body:not(.acf-admin-6) .acf-field .select2-container.-acf.select2-container--focus .select2-selection--multiple,
body:not(.acf-admin-6) .acf-field .select2-container.-acf.select2-container--open .select2-selection--multiple {
    border-color: #7e8993
}

body:not(.acf-admin-6) .select2-selection__choice>code,
body:not(.acf-admin-6) .select2-selection__rendered>code {
    font-size: 12px;
    padding: 3px;
    vertical-align: 1px;
    line-height: 12px
}

body:not(.acf-admin-6) .select2-results__option>code {
    font-size: 12px
}

body:not(.acf-admin-6) .acf-input-wrap .acf-is-prepended+.select2>.selection>.select2-selection {
    border-radius: 0 3px 3px 0 !important
}

body:not(.acf-admin-6) .acf-input-wrap .acf-is-appended+.select2>.selection>.select2-selection {
    border-radius: 3px 0 0 3px !important
}

body:not(.acf-admin-6) .acf-input-wrap .acf-is-prepended.acf-is-appended+.select2>.selection>.select2-selection {
    border-radius: 0 !important
}

body:not(.acf-admin-5-3) .acf-field .select2-container .select2-selection {
    border-color: #dfdfdf !important;
    border-radius: 0 !important
}

body:not(.acf-admin-5-3) .acf-field .select2-container .select2-selection__choice {
    border-color: #dfdfdf !important;
    border-radius: 0 !important
}

body:not(.acf-admin-5-3) .acf-field .select2-container .select2-search--inline .select2-search__field {
    margin-top: 0 !important
}

body:not(.acf-admin-5-3) .acf-field .select2-dropdown {
    border-color: #dfdfdf !important;
    border-radius: 0 !important
}