.submitbox .misc-pub-acfe-field-group-key {
    padding-top: 2px
}

.submitbox .misc-pub-acfe-field-group-key span {
    font-size: 16px;
    color: #82878c;
    width: 20px;
    margin-right: 2px
}

.submitbox .misc-pub-acfe-field-group-key code {
    font-size: 12px
}

.submitbox .misc-pub-acfe-field-group-export {
    padding-top: 2px
}

.submitbox .misc-pub-acfe-field-group-export span {
    font-size: 17px;
    color: #82878c;
    line-height: 1.3;
    width: 20px;
    margin-right: 2px
}

.acf-fields .acf-field-tab {
    display: block !important;
    height: 0;
    overflow: hidden;
    padding: 0 !important;
    border: 0 !important
}

.acf-field-setting-acfe_bidirectional>.acf-input>.acf-fields,
.acf-field-setting-acfe_hide>.acf-input>.acf-fields,
.acf-field-setting-acfe_permissions>.acf-input>.acf-fields,
.acf-field-setting-acfe_settings>.acf-input>.acf-fields,
.acf-field-setting-acfe_settings>.acf-input>.acf-fields>.acf-field.acf-field-acfe-settings-rules-or,
.acf-field-setting-acfe_update>.acf-input>.acf-fields,
.acf-field-setting-acfe_update>.acf-input>.acf-fields>.acf-field.acf-field-acfe-update-functions,
.acf-field-setting-acfe_validate>.acf-input>.acf-fields,
.acf-field-setting-acfe_validate>.acf-input>.acf-fields>.acf-field.acf-field-acfe-validate-rules-or {
    border: 0
}

.acf-field-setting-acfe_bidirectional>.acf-input>.acf-fields>.acf-field,
.acf-field-setting-acfe_hide>.acf-input>.acf-fields>.acf-field,
.acf-field-setting-acfe_permissions>.acf-input>.acf-fields>.acf-field,
.acf-field-setting-acfe_settings>.acf-input>.acf-fields>.acf-field,
.acf-field-setting-acfe_update>.acf-input>.acf-fields>.acf-field,
.acf-field-setting-acfe_validate>.acf-input>.acf-fields>.acf-field {
    padding: 0
}

.acf-field-setting-acfe_field_data {
    display: none
}

.acf-field-hide-front.acfe_width_auto {
    margin-right: 30px
}

.acf-field-setting-acfe_bidirectional>.acf-input>.acf-fields>.acf-field.acf-field-acfe-bidirectional-enabled>.acf-label,
.acf-field-setting-acfe_bidirectional>.acf-input>.acf-fields>.acf-field.acf-field-acfe-bidirectional-related>.acf-label,
.acf-field-setting-acfe_settings>.acf-input>.acf-fields>.acf-field.acf-field-acfe-settings-activate>.acf-label,
.acf-field-setting-acfe_update>.acf-input>.acf-fields>.acf-field.acf-field-acfe-update-activate>.acf-label,
.acf-field-setting-acfe_validate>.acf-input>.acf-fields>.acf-field.acf-field-acfe-validate-activate>.acf-label {
    display: none
}

.acf-field-acfe-meta>.acf-input .acf-repeater.-empty.-table>table,
.acf-field-setting-acfe_settings>.acf-input .acf-repeater.-empty.-table>table,
.acf-field-setting-acfe_update>.acf-input .acf-repeater.-empty.-table>table,
.acf-field-setting-acfe_validate>.acf-input .acf-repeater.-empty.-table>table {
    border: 0;
    display: none
}

.acf-field-acfe-meta>.acf-input .acf-repeater.-empty.-table>table>thead,
.acf-field-setting-acfe_settings>.acf-input .acf-repeater.-empty.-table>table>thead,
.acf-field-setting-acfe_update>.acf-input .acf-repeater.-empty.-table>table>thead,
.acf-field-setting-acfe_validate>.acf-input .acf-repeater.-empty.-table>table>thead {
    display: none
}

.acf-field-acfe-meta>.acf-input>.acf-repeater .acf-actions,
.acf-field-setting-acfe_settings>.acf-input>.acf-repeater .acf-actions,
.acf-field-setting-acfe_update>.acf-input>.acf-repeater .acf-actions,
.acf-field-setting-acfe_validate>.acf-input>.acf-repeater .acf-actions {
    text-align: left
}

.acf-repeater.-block>table,
.acf-repeater.-row>table {
    border-collapse: separate;
    border-spacing: 0 15px;
    background: 0 0;
    border: 0;
    margin-top: -15px;
    margin-bottom: -7px
}

.acf-repeater.-block>table>*,
.acf-repeater.-row>table>* {
    border-collapse: collapse;
    border-spacing: 0
}

.acf-repeater.-block>table>tbody>tr>td,
.acf-repeater.-row>table>tbody>tr>td {
    border-top: 1px solid #ccd0d4 !important;
    border-bottom: 1px solid #ccd0d4 !important
}

.acf-repeater.-block>table>tbody>tr>td:first-of-type,
.acf-repeater.-row>table>tbody>tr>td:first-of-type {
    border-left: 1px solid #ccd0d4 !important
}

.acf-repeater.-block>table>tbody>tr>td:last-of-type,
.acf-repeater.-row>table>tbody>tr>td:last-of-type {
    border-right: 1px solid #ccd0d4 !important
}

body:not(.acf-admin-5-3) .acf-repeater.-block>table>tbody>tr>td,
body:not(.acf-admin-5-3) .acf-repeater.-row>table>tbody>tr>td {
    border-top: 1px solid #e1e1e1;
    border-bottom: 1px solid #e1e1e1
}

body:not(.acf-admin-5-3) .acf-repeater.-block>table>tbody>tr>td:first-of-type,
body:not(.acf-admin-5-3) .acf-repeater.-row>table>tbody>tr>td:first-of-type {
    border-left: 1px solid #e1e1e1
}

body:not(.acf-admin-5-3) .acf-repeater.-block>table>tbody>tr>td:last-of-type,
body:not(.acf-admin-5-3) .acf-repeater.-row>table>tbody>tr>td:last-of-type {
    border-right: 1px solid #e1e1e1
}

.acf-field-setting-acfe_bidirectional>.acf-input>.acf-fields>.acf-field.acf-field-acfe-bidirectional-related {
    margin-left: 10px
}

.acfe_width_auto {
    width: auto !important
}

.status-acf-disabled .column-title a,
.status-acf-disabled .column-title code {
    color: #555 !important
}

.status-acf-disabled .column-title strong {
    color: #a0a5aa
}

.column-acf-field-group-category {
    width: 125px
}

td.acf-field-group-category a {
    word-wrap: break-word;
    padding: 2px 5px;
    margin: 0 1px;
    border-radius: 2px;
    background: rgba(0, 0, 0, .06);
    color: #23282d
}

.column-acfe-location {
    width: 100px
}

.column-acf-description,
.column-acf-location {
    width: 230px
}

#acf-field-group-wrap .wp-list-table .column-acf-fg-status {
    width: 70px
}

#acf-field-group-wrap .wp-list-table .column-acf-fg-count,
.post-type-acf-field-group .wp-list-table .column-acf-count {
    width: 80px
}

.post-type-acf-field-group .wp-list-table .column-title .post-state {
    color: #a0a5aa
}

.column-title .acfe-key {
    color: #555;
    font-size: 12px
}

.acf-field-image[data-name=acfe_flexible_thumbnail]>.acf-input>.acf-image-uploader.has-value>.show-if-value.image-wrap {
    max-width: 85px !important
}

.acf-diff-content table.diff tbody tr td:nth-child(2) {
    width: auto
}

body.acf-admin-5-3 .acf-field-setting-fc_layout .acf-input-wrap.select {
    border-color: #7e8993
}

body.acf-admin-5-3 .acf-field-setting-fc_layout .acf-input-wrap.select select {
    min-height: 28px
}

.acfe-field-setting-flex>.acf-input>.acf-fields {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border: 0
}

.acfe-field-setting-flex>.acf-input>.acf-fields>.acf-field:first-child {
    padding: 0;
    margin-right: 10px
}

.acfe-field-setting-flex>.acf-input>.acf-fields>.acf-field:first-child .acf-label {
    display: none
}

.acfe-field-setting-flex>.acf-input>.acf-fields>.acf-field:first-child~.acf-field {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
    border-top: 0;
    border-left: 0 !important
}

.acfe-field-setting-flex>.acf-input>.acf-fields>.acf-field:first-child~.acf-field[data-type=true_false] {
    padding-top: 2px
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field[data-name=acfe_flexible_category],
.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field[data-name=acfe_flexible_settings_label],
.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field[data-name=acfe_flexible_thumbnail] {
    margin: 0
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta>.acf-fc-meta-label:first-of-type {
    margin: 0
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting {
    margin: 0;
    margin-top: 15px
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting>.acf-input,
.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting>.acf-label {
    margin: 0
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting>.acf-input label,
.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting>.acf-label label {
    margin: 0
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting+li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    margin-bottom: 0;
    margin-top: 10px
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting+li>ul {
    margin: 0;
    width: 50%
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting+li>ul:first-child+ul {
    border-left: 0;
    margin-left: 10px
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting+li>ul>.acf-input {
    margin-bottom: 0
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting.acfe-flexible-field-setting-row+li {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting.acfe-flexible-field-setting-row+li>ul {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    margin: 10px 0 0 0;
    width: 100%
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting.acfe-flexible-field-setting-row+li>ul:first-child {
    margin-top: 0
}

.acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting.acfe-flexible-field-setting-row+li>ul:first-child+ul {
    margin-left: 0
}

.acf-field-setting-fc_layout .select2-container--default .select2-selection--multiple li {
    margin-bottom: 0
}

.acf-field-object-acfe-column .acf-field-setting-instructions,
.acf-field-object-acfe-column .acf-field-setting-label,
.acf-field-object-acfe-column .acf-field-setting-name,
.acf-field-object-acfe-column .acf-field-setting-required,
.acf-field-object-acfe-column .acf-field-setting-warning,
.acf-field-object-acfe-column .acf-field-setting-wrapper {
    display: none
}

.acf-field-object-acfe-column .li-field-name {
    visibility: hidden
}

.acf-field-object-acfe-column.open>.handle a span {
    color: #fff
}

.acf-field-object+.acf-field-object-acfe-column:before {
    display: block;
    content: "";
    height: 5px;
    width: 100%;
    background: #f9f9f9;
    border-bottom: #f0f0f0 solid 1px
}

.acf-field-setting-allow_terms>.acf-input>ul>li:first-of-type {
    width: calc(100% - 125px)
}

.acf-field-setting-allow_terms>.acf-input>ul>li:last-of-type {
    width: 125px
}

#acf-field-group-locations .rule-groups>.rule-group>table>tbody>tr>td.value>.acf-field {
    margin: 0
}

.acf-hl[data-cols="5"]>li {
    width: 20%
}

.acf-hl[data-cols="6"]>li {
    width: 16.667%
}

.acf-admin-6.acf-admin-field-groups .wp-list-table td.check-column,
.acf-admin-6.acf-admin-field-groups .wp-list-table th.check-column {
    padding-right: 25px;
    padding-left: 15px
}

.acf-admin-6.acf-admin-field-groups .wp-list-table tfoot td,
.acf-admin-6.acf-admin-field-groups .wp-list-table tfoot th,
.acf-admin-6.acf-admin-field-groups .wp-list-table thead td,
.acf-admin-6.acf-admin-field-groups .wp-list-table thead th {
    padding-right: 10px;
    padding-left: 10px
}

.acf-admin-6.acf-admin-field-groups .wp-list-table tbody td,
.acf-admin-6.acf-admin-field-groups .wp-list-table tbody th {
    padding-right: 10px;
    padding-left: 10px
}

.acf-admin-6.acf-admin-field-groups .wp-list-table .column-acf-count {
    width: 70px
}

.acf-admin-6.acf-admin-field-groups .wp-list-table .column-acf-location {
    width: 200px
}

.acf-admin-6.acf-admin-field-groups .wp-list-table .column-acfe-load {
    width: 70px
}

.acf-admin-6.acf-admin-field-groups .wp-list-table .column-acfe-autosync-json,
.acf-admin-6.acf-admin-field-groups .wp-list-table .column-acfe-autosync-php {
    width: 70px
}

.acf-admin-6.acf-admin-field-groups .wp-list-table a.row-title {
    font-size: 14px !important
}

.acf-admin-6.acf-admin-field-groups .wp-list-table .row-actions {
    margin-top: 4px
}

.acf-admin-6.acf-admin-field-groups .subsubsub {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.acf-admin-6.acf-admin-single-field-group .wrap>#message.updated {
    margin-bottom: 16px !important
}

.acf-admin-6.acf-admin-single-field-group .acf-field-object .handle li.li-field-label::before {
    width: 16px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-object .handle li.li-field-label>strong a {
    font-size: 14px
}

.acf-admin-6.acf-admin-single-field-group .acf-fields .acf-field-tab {
    display: none !important
}

.acf-admin-6.acf-admin-single-field-group #submitdiv .button {
    height: 32px
}

.acf-admin-6.acf-admin-single-field-group .acf-repeater .acf-actions .acf-button {
    float: left
}

.acf-admin-6.acf-admin-single-field-group .acf-input .select2-container--default .select2-selection--single {
    height: auto
}

.acf-admin-6.acf-admin-single-field-group .acf-input .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding: 0 6px
}

.acf-admin-6.acf-admin-single-field-group .acf-input .select2-container--default .select2-selection--single .select2-selection__rendered .acf-selection {
    padding: 10px 5px;
    display: block;
    line-height: 1.5
}

.acf-admin-6.acf-admin-single-field-group .acf-input .select2-container--default .select2-selection--single .select2-selection__rendered .select2-selection__clear {
    margin-top: 13px;
    margin-right: 11px
}

.acf-admin-6.acf-admin-single-field-group .acf-input .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 40px
}

.acf-admin-6.acf-admin-single-field-group #acf-field-group-options .acf-field .acf-table>tbody>tr>td,
.acf-admin-6.acf-admin-single-field-group #acf-field-group-options .acf-field .acf-table>tbody>tr>th,
.acf-admin-6.acf-admin-single-field-group #acf-field-group-options .acf-field .acf-table>thead>tr>td,
.acf-admin-6.acf-admin-single-field-group #acf-field-group-options .acf-field .acf-table>thead>tr>th {
    padding: 8px
}

.acf-admin-6.acf-admin-single-field-group #acf-field-group-options .acf-field .acf-table td.acf-field,
.acf-admin-6.acf-admin-single-field-group #acf-field-group-options .acf-field .acf-table tr.acf-field {
    margin: 0;
    width: auto
}

.acf-admin-6.acf-admin-single-field-group #acf-field-group-options .acf-field[data-name=acfe_form] .acf-label {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex
}

.acf-admin-6.acf-admin-single-field-group #acf-field-group-acfe .acf-label {
    display: block
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field[data-setting].acf-field-flexible-content>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field[data-setting].acf-field-flexible-content>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field[data-setting].acf-field-group>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field[data-setting].acf-field-group>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field[data-setting].acf-field-repeater>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field[data-setting].acf-field-repeater>.acf-label {
    max-width: 930px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-fields.-left>.acf-field {
    padding: 15px 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-fields>.acf-field {
    margin: 0;
    padding: 15px 12px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-fields>.acf-field>.acf-label {
    display: block
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-fields>.acf-field>.acf-input {
    max-width: initial
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-table>tbody>tr>td,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-table>tbody>tr>th,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-table>thead>tr>td,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-table>thead>tr>th {
    padding: 8px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-table td.acf-field,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .acf-table tr.acf-field {
    margin: 0;
    width: auto
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field .CodeMirror-wrap.cm-s-default {
    width: 100%
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-add-actions>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-add-actions>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-async>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-async>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-grid-container>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-grid-container>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-grid>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-grid>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-layouts-state>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-layouts-state>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-modal-edit>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-modal-edit>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-modal>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-modal>.acf-label,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-remove-button>.acf-input,
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field.acf-field-acfe-flexible-remove-button>.acf-label {
    max-width: 930px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .select2-container.-acf .select2-selection--multiple .select2-selection__rendered .select2-selection__choice {
    margin-top: 5px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .select2-container.-acf .select2-selection--multiple .select2-selection__rendered .select2-selection__choice .acf-selection {
    line-height: 1.5
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .select2-container.-acf .select2-selection--multiple .select2-selection__rendered .select2-selection__choice .select2-selection__choice__remove {
    margin-top: 2px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-allow_terms input[type=number] {
    width: 56px;
    height: 43px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-min>.acf-input input[type=number] {
    max-width: 98%
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-acfe_bidirectional>.acf-input>.acf-fields>.acf-field {
    padding: 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-acfe_bidirectional>.acf-input>.acf-fields>.acf-field+.acf-field {
    border-left: 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-hide_field>.acf-input ul {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-hide_field>.acf-input ul li {
    width: 50%
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-hide_field>.acf-input ul li:nth-child(3),
.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-hide_field>.acf-input ul li:nth-child(4) {
    margin-top: 10px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-settings .acf-field-setting-hide_field>.acf-input ul li select {
    min-width: 50px
}

.acf-admin-6.acf-admin-single-field-group .acfe-field-setting-flex>.acf-input>.acf-fields>.acf-field {
    padding: 0 5px 0 0
}

.acf-admin-6.acf-admin-single-field-group .acfe-field-setting-flex>.acf-input>.acf-fields>.acf-field.acf-field-acfe-flexible-modal-col {
    width: 25% !important
}

.acf-admin-6.acf-admin-single-field-group .acfe-field-setting-flex>.acf-input>.acf-fields>.acf-field select {
    min-width: 50px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta>.acf-fc-meta-name {
    float: none;
    margin-left: 0;
    width: auto
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting {
    padding-left: 0;
    padding-right: 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting+li>ul {
    padding-left: 0;
    padding-right: 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta select {
    min-width: 50px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .select2-container.-acf {
    border-radius: 0 6px 6px 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .select2-container.-acf .select2-selection__rendered {
    border-radius: 0 6px 6px 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .select2-container.-acf .select2-selection__rendered .select2-search input {
    height: 38px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acfe-flexible-layout-location-rules {
    padding-left: 0;
    padding-right: 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acfe-flexible-layout-location-rules td:first-of-type {
    padding-left: 0
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-fc-meta-max+li {
    clear: both
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field[data-name=acfe_flexible_category],
.acf-admin-6.acf-admin-single-field-group .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field[data-name=acfe_flexible_settings_label] {
    padding-left: 0;
    padding-right: 0;
    width: 100%
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-acfe_permissions>.acf-input>ul {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.acf-admin-6.acf-admin-single-field-group .acf-field-setting-acfe_field_group_condition {
    padding-right: 72px;
    padding-left: 72px;
    margin-top: 32px
}

.acf-admin-6.acf-admin-single-field-group .acfe-modal>.acfe-modal-wrapper>.acfe-modal-title>.title {
    min-height: unset;
    display: block;
    padding: 0
}

.acf-admin-6.acf-admin-single-field-group .acf-hl[data-cols="6"] {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.acf-admin-6.acf-admin-single-field-group .acf-hl[data-cols="6"]>li {
    width: 33.33%
}

.acf-admin-6.acf-admin-single-field-group .acf-hl[data-cols="6"]>li:nth-child(4),
.acf-admin-6.acf-admin-single-field-group .acf-hl[data-cols="6"]>li:nth-child(5),
.acf-admin-6.acf-admin-single-field-group .acf-hl[data-cols="6"]>li:nth-child(6) {
    margin-top: 10px
}

.acf-admin-6.acf-admin-single-field-group .acf-field-type-settings .acf-field {
    border-top-width: 0
}

.acf-admin-6.acf-admin-single-field-group #acf-field-group-fields.hide-tabs .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting {
    margin-bottom: 0
}

.acf-admin-6.acf-admin-single-field-group #acf-field-group-fields.hide-tabs .acf-field-setting-fc_layout>.acf-input>.acf-fc-meta .acf-field.acfe-flexible-field-setting+li>ul {
    margin-bottom: 0
}