jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acf.addAction("new_field/name=acfe_author",function(e){e.on("change",function(e){e.stopPropagation()})}),function(n){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({wait:"prepare",actions:{refresh_post_screen:"onRefreshScreen"},events:{"click .acfe-dev-delete-meta":"onDeleteSingle",'click .acfe-dev-bulk [type="submit"]':"onDeleteBulk","change #acfe-wp-custom-fields-hide":"onCheckPostbox","change #acfe-acf-custom-fields-hide":"onCheckPostbox"},$acf:function(){return n("#acfe-acf-custom-fields")},$wp:function(){return n("#acfe-wp-custom-fields")},$bulk:function(){return n(".acfe-dev-bulk")},count:function(e){return this["$"+e]().find("tbody tr").length},hideBulk:function(){this.$bulk().hide()},showBulk:function(){this.$bulk().show()},initialize:function(){this.$bulk().insertAfter(this.$bulk().closest(".postbox")),this.$acf().is(":visible")||this.$wp().is(":visible")||this.hideBulk(),n(".metabox-prefs .acfe-dev-meta-count").remove()},syncMetaboxes:function(){this.$acf().find(".acfe-dev-meta-count").text(this.count("acf")),this.$wp().find(".acfe-dev-meta-count").text(this.count("wp")),this.count("acf")||this.$acf().remove(),this.count("wp")||this.$wp().remove(),(this.count("acf")||this.count("wp"))&&(this.$acf().is(":visible")||this.$wp().is(":visible"))||this.hideBulk()},onDeleteSingle:function(e,t){e.preventDefault();var a=this,i=t.closest("tr");n.ajax({url:acf.get("ajaxurl"),type:"post",data:{action:"acfe/dev/single_delete_meta",id:t.attr("data-meta-id"),key:t.attr("data-meta-key"),type:t.attr("data-meta-type"),_wpnonce:t.attr("data-nonce")},beforeSend:function(){i.addClass("deleted").delay(200).fadeOut(250,function(){i.remove(),a.syncMetaboxes()})},success:function(e){"1"!==e&&(i.removeClass("deleted"),i.show())}})},onDeleteBulk:function(e,t){e.preventDefault();var a,i,s=this,c=t.prevAll(".acfe-dev-bulk-action").val(),e=t.prevAll(".acfe-dev-bulk-meta-type").val(),t=t.prevAll(".acfe-dev-bulk-nonce").val();"delete"===c&&(a=[],i=[],n("input.acfe-dev-bulk-checkbox:checked").each(function(){a.push(n(this).val()),i.push(n(this).closest("tr"))}),a.length&&n.ajax({url:acf.get("ajaxurl"),type:"post",data:{action:"acfe/dev/bulk_delete_meta",ids:a,type:e,_wpnonce:t},beforeSend:function(){i.map(function(e){n(e).addClass("deleted").delay(200).fadeOut(250,function(){n(e).remove(),s.syncMetaboxes()})})}}))},onCheckPostbox:function(e,t){var a=t.val();t.prop("checked")?this.showBulk():("acfe-wp-custom-fields"===a&&!this.$acf().is(":visible")||"acfe-acf-custom-fields"===a&&!this.$wp().is(":visible"))&&this.hideBulk()},onRefreshScreen:function(e){e.hidden.map(function(e){"acfe-wp-custom-fields"!==e&&"acfe-acf-custom-fields"!==e&&"acfe-performance"!==e||acf.getPostbox(e).showEnable()})}})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{new_field:"newField"},isRepeater:function(e){return"repeater"===e.get("type")||"flexible_content"===e.get("type")},getCondition:function(e){return this.isRepeater(e)?0===e.val():!e.val().length},newField:function(e){e.get("enableSwitch")?this.enableSwitch(e):(e.get("switched")||e.get("switcher"))&&this.enableSwitcher(e)},enableSwitcher:function(e){var a,i,s=this;e.get("switched")?(a=acf.getField(e.$el.prev()),i=e):e.get("switcher")&&(a=e,i=acf.getField(e.$el.next())),s.getCondition(i)?(a.switchOff(),a.show("switcher"),i.hide("switcher")):(a.hide("switcher"),i.show("switcher")),e.get("switcher")&&(a.on("change",function(){a.$input().prop("checked")&&(a.hide("switcher"),i.show("switcher"),s.isRepeater(i)&&i.add())}),i.on("change",function(e,t){s.getCondition(i)&&(a.switchOff(),a.show("switcher"),i.hide("switcher"))}))},enableSwitch:function(e){var t=e.$el.clone();t.removeAttr("data-enable-switch"),t.attr("data-switcher",!0),t.attr("data-name",e.get("name")+"_acfe_switch"),t.attr("data-key",e.get("name")+"_acfe_switch"),t.attr("data-type","true_false"),t.find(">.acf-input").html('<div class="acf-true-false">\n<input type="hidden" value="0"><label>\n<input type="checkbox" value="1" class="acf-switch-input" autocomplete="off">\n<div class="acf-switch"><span class="acf-switch-on" style="min-width: 18px;">'+acf.__("Yes")+'</span><span class="acf-switch-off" style="min-width: 18px;">'+acf.__("No")+'</span><div class="acf-switch-slider"></div></div></label>\n</div>'),t=t.insertBefore(e.$el),acf.getField(t),e.$el.removeAttr("data-enable-switch"),e.set("enableSwitch",!1),e.$el.attr("data-switched",!0),e.set("switched",!0)}}),function(s){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({wait:"prepare",priority:1,initialize:function(){acfe.get("module")&&"post"===acfe.get("module.screen")&&new e(acfe.get("module"))}}),e=acf.Model.extend({setup:function(e){this.inherit(e)},filters:{validation_complete:"onValidationComplete"},onValidationComplete:function(e,t,a){var i=s("#titlewrap #title");return i.val()||(e.valid=0,e.errors=e.errors||[],e.errors.push({input:"",message:this.get("messages.label")}),i.focus()),e},initialize:function(){s("#post-status-display").html(this.get("messages.status")),s(".acfe-misc-export").insertAfter(".misc-pub-post-status")}}))}(jQuery),function(c){var i,e,t;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({wait:"prepare",priority:1,initialize:function(){"form"===acfe.get("module.name")&&"post"===acfe.get("module.screen")&&new e}}),i=function(){acf.getFields({name:"load_acf_fields"}).map(function(e){var t=[];acf.getFields({type:"select",sibling:e.$el}).map(function(e){e.has("relatedField")&&e.val()&&t.push(e.val())}),e.$inputs().each(function(){var e=c(this).closest("li");acfe.inArray(c(this).val(),t)?acf.hide(e):acf.show(e)}),e.$control().find("> li").each(function(){var e=c(this);0===e.find("li").not(".acf-hidden").length?acf.hide(e):acf.show(e)}),0===e.$control().find("> li").not(".acf-hidden").length?e.hideDisable("acfe_form_field_groups","acfe_form_field_groups"):e.showEnable("acfe_form_field_groups","acfe_form_field_groups")})},e=acf.Model.extend({actions:{new_select2:"newSelect2","new_field/key=field_post_action_save_append_terms":"newAppendTerms","new_field/key=field_actions":"newActions","new_field/key=field_email_action_files":"newFiles","new_field/key=field_email_action_files_static":"newFiles","new_field/name=save_acf_fields":"newCheckboxes","new_field/name=load_acf_fields":"newCheckboxes","new_field/key=field_field_groups":"newFieldGroups"},filters:{select2_args:"select2Args","select2_ajax_data/action=acfe/form/map_field_ajax":"mapFieldAjax","select2_ajax_data/action=acfe/form/map_field_groups_ajax":"mapFieldGroupsAjax","select2_ajax_data/key=field_post_action_save_target_custom":"customAjaxData","select2_ajax_data/key=field_post_action_load_source_custom":"customAjaxData","select2_ajax_data/key=field_post_action_save_post_author_custom":"customAjaxData","select2_ajax_data/key=field_post_action_save_post_parent_custom":"customAjaxData","select2_ajax_data/key=field_term_action_save_target_custom":"customAjaxData","select2_ajax_data/key=field_term_action_save_parent_custom":"customAjaxData","select2_ajax_data/key=field_term_action_load_source_custom":"customAjaxData","select2_ajax_data/key=field_user_action_save_target_custom":"customAjaxData","select2_ajax_data/key=field_user_action_load_source_custom":"customAjaxData"},newSelect2:function(e){e.get("field")&&e.get("field").has("relatedField")&&new t(e)},newAppendTerms:function(e){var t=acf.getFields({key:"field_post_action_save_post_terms",sibling:e.$el}).shift();t&&(e.$inputWrap().addClass("append-terms").appendTo(t.$inputWrap()),e.$el.remove())},newActions:function(e){e.on("click",'[data-name="add-layout"]',function(e){c("body").find(".acf-fc-popup").addClass("acfe-fc-popup-grey")})},newFiles:function(e){e.$("> .acf-input > .acf-repeater > .acf-actions > .acf-button").removeClass("button-primary")},select2Args:function(e,t,a,i,s){return i.get("acfeAllowCustom")&&(e.templateSelection=i=function(e){e.text&&-1===e.text.indexOf("<code>")&&(e.text=e.text.replace(/({[\w: +-\\]+}*)/g,"<code>$1</code>"));var t=c('<span class="acf-selection"></span>');return t.html(acf.escHtml(e.text)),t.data("element",e.element),t},e.templateResult=i),e},mapFieldAjax:function(e,t,a,i,s){e.field_label="",e.value=i.val(),e.choices=[],e.custom_choices=[],e.field_groups=[],e.is_load=i.has("relatedField")?1:0;var c,n=acfe.getTextNode(i.$labelWrap().find("label"));n||(c=acf.getInstance(i.$el.closest(".acf-field-group")))&&(n=acfe.getTextNode(c.$labelWrap().find("label"))),e.field_label=n;n=i.get("choices");n&&(e.choices=n);i=i.get("customChoices");i&&(e.custom_choices=i);i=acf.getField("field_field_groups").val();return i.length&&(e.field_groups=i),e},mapFieldGroupsAjax:function(e,t,a,i,s){e.value=i.val(),e.choices=[],e.custom_choices=[];var c=i.get("choices");c&&(e.choices=c);i=i.get("customChoices");return i&&(e.custom_choices=i),e},customAjaxData:function(e,t,a,i,s){return e.is_form=1,e},newCheckboxes:function(e){this.refreshCheckboxChoicesHtml(e)},newFieldGroups:function(e){e.on("change",this.proxy(function(e){this.refreshFieldGroupsMetabox(),this.refreshCheckboxesChoices()}))},refreshFieldGroupsMetabox:function(){var e=acf.getField("field_field_groups").val(),t=acf.getPostbox("acfe-field-groups");if(t)return e.length?void c.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax({action:"acfe/form/field_groups_metabox",field_groups:e}),type:"post",dataType:"html",context:this,success:function(e){t.show(),t.html(e)}}):t.hide()},refreshCheckboxesChoices:function(){var e=acf.getFields({name:"save_acf_fields"}),t=acf.getFields({name:"load_acf_fields"});[].concat(e,t).map(function(e){this.refreshCheckboxChoicesHtml(e)},this)},refreshCheckboxChoicesHtml:function(a){var e=acf.getField("field_field_groups").val();if(!e.length)return a.hideDisable("acfe_form_field_groups","acfe_form_field_groups");c.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax({action:"acfe/form/map_checkbox_ajax",name:a.getInputName(),_name:a.get("name"),key:a.get("key"),value:a.val(),field_groups:e}),type:"post",dataType:"html",context:this,success:function(e){a.$inputWrap().html(e),a.$control().find("> li").length?((e=a.$inputWrap().find("> [data-labels]").data("labels")).length&&e.map(function(e,t){a.$control().find("> li:eq("+t+")").prepend("<strong>"+e+"</strong>")}),a.showEnable("acfe_form_field_groups","acfe_form_field_groups")):a.hideDisable("acfe_form_field_groups","acfe_form_field_groups"),i()}})}}),t=acf.Model.extend({field:{},select2:{},relatedField:!1,events:{change:"onChange",hideField:"onHideField",showField:"onShowField"},setup:function(e){this.select2=e,this.field=e.get("field"),this.$el=this.field.$el},initialize:function(){this.relatedField=this.getRelatedField(),this.relatedField&&(this.setupRelatedHTML(),this.showRelatedMessage(!0))},onChange:function(){this.field.val()?this.showRelatedMessage():this.hideRelatedMessage()},onHideField:function(e,t,a){this.relatedField&&"conditional_logic"===a&&this.hideRelatedMessage()},onShowField:function(e,t,a){this.relatedField&&"conditional_logic"===a&&this.showRelatedMessage()},setupRelatedHTML:function(){this.relatedField.$inputWrap().find(".related-message").length||this.relatedField.$inputWrap().append('<div class="related-message" />')},showRelatedMessage:function(e){e=e||!1;this.field.val();var t,a=this.field.val();!this.select2||(t=this.select2.getValue().shift())&&t.id&&(t.id,a=t.text),a&&(this.relatedField.$inputWrap().addClass("acfe-display-related-message"),this.relatedField.$inputWrap().find(".related-message").html("Field: "+a),e||acf.isset(this.relatedField,"select2")&&(this.relatedField.select2.$el.val(null),this.relatedField.select2.$el.trigger("change")),i())},hideRelatedMessage:function(){this.relatedField.$inputWrap().removeClass("acfe-display-related-message"),this.relatedField.$inputWrap().find(".related-message").html(""),i()},getRelatedField:function(){var e=this.field.get("relatedField"),t=this.field.$el.closest(".layout");return acf.getFields({key:e,parent:t}).shift()}}))}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acf.addAction("show_postbox",function(e){e.$el.removeClass("acfe-postbox-left acfe-postbox-top")});