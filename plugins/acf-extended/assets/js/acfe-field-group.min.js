!function(c){var i;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({actions:{append_field_object:"appendCodeEditor"},appendCodeEditor:function(e){var t;"acfe_code_editor"===e.get("type")&&(1<(t=e.$setting("default_value").find("> .acf-input > .acf-input-wrap > .CodeMirror")).length&&t.last().remove(),1<(e=e.$setting("placeholder").find("> .acf-input > .acf-input-wrap > .CodeMirror")).length&&e.last().remove())}}),new acf.Model({actions:{"change_field_label/type=acfe_column":"renderTitle","change_field_type/type=acfe_column":"renderTitle","render_field_settings/type=acfe_column":"renderField"},ucFirst:function(e){return e.charAt(0).toUpperCase()+e.slice(1)},renderTitle:function(e){var t=acf.getInstance(e),a=t.$setting("columns"),e=acf.getInstance(a).getValue(),a=t.$setting("endpoint"),a=acf.getInstance(a).getValue();t.set("label","(Column "+this.ucFirst(e=a?"Endpoint":e)+")")},renderField:function(e){var t=acf.getFieldObject(e),e=function(){t.set("label",!0)};t.on("change",".acfe-field-columns",e),t.on("change",".acfe-field-columns-endpoint",e)}}),new acf.Model({filters:{"select2_ajax_data/action=acfe/fields/taxonomy_terms/allow_query":"taxonomyTermsAjax"},taxonomyTermsAjax:function(e,t,a,n,i){var f=a.closest(".acf-field-settings").find(".acf-field-setting-taxonomy > .acf-input > select > option:selected"),l=[];f.each(function(){l.push(c(this).val())}),e.taxonomies=l;a=a.closest(".acf-field-settings").find('.acf-field-setting-allow_terms > .acf-input input[type="number"]');return e.level=a.val(),e}}),new acf.Model({wait:"prepare",initialize:function(){c(".button.edit-field").each(function(){var e=c(this),t=e.closest("tbody, .acf-field-settings");c(t).find(".acfe-data-button:first").insertAfter(e),c(t).find(".acfe-modal:first").appendTo(c("body")),c(t).find(".acf-field-setting-acfe_field_data:first").remove()})}}),new acf.Model({actions:{new_field:"onNewField"},onNewField:function(e){var t,a,n;(e.has("before")||e.has("after"))&&"tab"!==e.get("type")&&(t=e.has("before")?"before":"after",(n=e.$el.closest(".acf-field-object")).length&&(a=acf.getFieldObject(n).get("key")),!(n=!(n=e.$el.closest(".acf-fields")).length?e.$el.closest(".acf-table"):n).length||(n=(a?n.find('[data-name="'+e.get(t)+'"]').not('.acf-input-sub .acf-field-object[data-key!="'+a+'"] [data-name="'+e.get(t)+'"]'):n.find('[data-name="'+e.get(t)+'"]')).first()).length&&n[t](e.$el))}}),i=acf.models.TabField,acf.models.TabField=i.extend({initialize:function(){var e,t,a,n;(this.has("before")||this.has("after"))&&(e=this.has("before")?"before":"after",n=this.$el.closest(".acf-fields"),(t=this.$el.closest(".acf-field-object")).length&&(a=acf.getFieldObject(t).get("key")),!(n=!n.length?this.$el.closest(".acf-table"):n).length||(n=(a?n.find('[data-name="'+this.get(e)+'"]').not('.acf-input-sub .acf-field-object[data-key!="'+a+'"] [data-name="'+this.get(e)+'"]'):n.find('[data-name="'+this.get(e)+'"]')).first()).length&&n[e](this.$el)),i.prototype.initialize.apply(this,arguments)}}),new acf.Model({wait:"ready",actions:{append:"onAppend","acfe/field_group/rule_refresh":"refreshFields"},initialize:function(){this.$el=c("#acf-field-group-locations, .field-group-locations")},onAppend:function(e){(e.is(".rule-group")||e.parent().parent().parent().is(".rule-group"))&&this.refreshFields()},refreshFields:function(){acf.getFields({parent:this.$("td.value")}).map(function(e){acfe.inArray(e.get("type"),["date_picker","date_time_picker","time_picker"])&&(e.$inputText().removeClass("hasDatepicker").removeAttr("id"),e.initialize())})}}),new acf.Model({actions:{"new_field/name=acfe_meta":"renderClass","new_field/name=acfe_settings":"renderClass","new_field/name=acfe_validate":"renderClass"},renderClass:function(e){e.$(".acf-button").removeClass("button-primary")}}),new acf.Model({events:{"keyup #post_name":"onInput"},onInput:function(e,t){var a=t.val();a.startsWith("group_")||t.val(a="group_"+a),c('[name="acf_field_group[key]"]').val(a),c(".misc-pub-acfe-field-group-key code").html(a)}}))}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{duplicate:"onAppend"},onAppend:function(e,t){!acfe.versionCompare(acf.get("acf_version"),">=","6.0")||(t=acf.findClosestField(t)).is('[data-type="repeater"]')&&acf.getField(t).render()}});