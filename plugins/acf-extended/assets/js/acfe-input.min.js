jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.newCondition=function(e,t){var a=t.get("field"),i=a.getField(e.field);if(a&&!i&&acf.findField(e.field).length&&(i=acf.getField(e.field)),!a||!i)return!1;t={rule:e,target:a,conditions:t,field:i},i=i.get("type"),e=e.operator;return new(acf.getConditionTypes({fieldType:i,operator:e})[0]||acf.Condition)(t)}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.registerConditionForFieldType("contains","checkbox"),acf.registerConditionForFieldType("contains","radio"),acf.registerConditionForFieldType("equalTo","acfe_code_editor"),acf.registerConditionForFieldType("notEqualTo","acfe_code_editor"),acf.registerConditionForFieldType("patternMatch","acfe_code_editor"),acf.registerConditionForFieldType("contains","acfe_code_editor"),acf.registerConditionForFieldType("hasValue","acfe_code_editor"),acf.registerConditionForFieldType("hasNoValue","acfe_code_editor"),acf.registerConditionForFieldType("equalTo","date_picker"),acf.registerConditionForFieldType("notEqualTo","date_picker"),acf.registerConditionForFieldType("patternMatch","date_picker"),acf.registerConditionForFieldType("contains","date_picker"),acf.registerConditionForFieldType("greaterThan","date_picker"),acf.registerConditionForFieldType("lessThan","date_picker"),acf.registerConditionForFieldType("equalTo","date_time_picker"),acf.registerConditionForFieldType("notEqualTo","date_time_picker"),acf.registerConditionForFieldType("patternMatch","date_time_picker"),acf.registerConditionForFieldType("contains","date_time_picker"),acf.registerConditionForFieldType("equalTo","acfe_forms"),acf.registerConditionForFieldType("notEqualTo","acfe_forms"),acf.registerConditionForFieldType("patternMatch","acfe_forms"),acf.registerConditionForFieldType("contains","acfe_forms"),acf.registerConditionForFieldType("hasValue","acfe_forms"),acf.registerConditionForFieldType("hasNoValue","acfe_forms"),acf.registerConditionForFieldType("equalTo","acfe_hidden"),acf.registerConditionForFieldType("notEqualTo","acfe_hidden"),acf.registerConditionForFieldType("patternMatch","acfe_hidden"),acf.registerConditionForFieldType("contains","acfe_hidden"),acf.registerConditionForFieldType("hasValue","acfe_hidden"),acf.registerConditionForFieldType("hasNoValue","acfe_hidden"),acf.registerConditionForFieldType("equalTo","acfe_post_statuses"),acf.registerConditionForFieldType("notEqualTo","acfe_post_statuses"),acf.registerConditionForFieldType("patternMatch","acfe_post_statuses"),acf.registerConditionForFieldType("contains","acfe_post_statuses"),acf.registerConditionForFieldType("hasValue","acfe_post_statuses"),acf.registerConditionForFieldType("hasNoValue","acfe_post_statuses"),acf.registerConditionForFieldType("equalTo","acfe_post_types"),acf.registerConditionForFieldType("notEqualTo","acfe_post_types"),acf.registerConditionForFieldType("patternMatch","acfe_post_types"),acf.registerConditionForFieldType("contains","acfe_post_types"),acf.registerConditionForFieldType("hasValue","acfe_post_types"),acf.registerConditionForFieldType("hasNoValue","acfe_post_types"),acf.registerConditionForFieldType("equalTo","acfe_slug"),acf.registerConditionForFieldType("notEqualTo","acfe_slug"),acf.registerConditionForFieldType("patternMatch","acfe_slug"),acf.registerConditionForFieldType("contains","acfe_slug"),acf.registerConditionForFieldType("hasValue","acfe_slug"),acf.registerConditionForFieldType("hasNoValue","acfe_slug"),acf.registerConditionForFieldType("equalTo","acfe_taxonomies"),acf.registerConditionForFieldType("notEqualTo","acfe_taxonomies"),acf.registerConditionForFieldType("patternMatch","acfe_taxonomies"),acf.registerConditionForFieldType("contains","acfe_taxonomies"),acf.registerConditionForFieldType("hasValue","acfe_taxonomies"),acf.registerConditionForFieldType("hasNoValue","acfe_taxonomies"),acf.registerConditionForFieldType("equalTo","taxonomy"),acf.registerConditionForFieldType("notEqualTo","taxonomy"),acf.registerConditionForFieldType("patternMatch","taxonomy"),acf.registerConditionForFieldType("contains","taxonomy"),acf.registerConditionForFieldType("hasValue","taxonomy"),acf.registerConditionForFieldType("hasNoValue","taxonomy"),acf.registerConditionForFieldType("equalTo","acfe_taxonomy_terms"),acf.registerConditionForFieldType("notEqualTo","acfe_taxonomy_terms"),acf.registerConditionForFieldType("patternMatch","acfe_taxonomy_terms"),acf.registerConditionForFieldType("contains","acfe_taxonomy_terms"),acf.registerConditionForFieldType("hasValue","acfe_taxonomy_terms"),acf.registerConditionForFieldType("hasNoValue","acfe_taxonomy_terms"),acf.registerConditionForFieldType("equalTo","time_picker"),acf.registerConditionForFieldType("notEqualTo","time_picker"),acf.registerConditionForFieldType("patternMatch","time_picker"),acf.registerConditionForFieldType("contains","time_picker"),acf.registerConditionForFieldType("equalTo","acfe_user_roles"),acf.registerConditionForFieldType("notEqualTo","acfe_user_roles"),acf.registerConditionForFieldType("patternMatch","acfe_user_roles"),acf.registerConditionForFieldType("contains","acfe_user_roles"),acf.registerConditionForFieldType("hasValue","acfe_user_roles"),acf.registerConditionForFieldType("hasNoValue","acfe_user_roles")),function(c){var i;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(i=[],acfe.registerEventForFieldType=function(e,t,a){i.push({fieldType:e,events:t="string"==typeof t?[t]:t,callback:a||!1})},acfe.getEvents=function(t){t=acf.parseArgs(t,{fieldType:""});var a=[];return i.map(function(e){t.fieldType&&-1===e.fieldType.indexOf(t.fieldType)||a.push(e)}),a},new acf.Model({actions:{new_field:"newField"},priority:20,data:{},parseEvent:function(e){return e.match(/^(\S+)\s*(.*)$/)},newField:function(o){this.set(o.cid,o.val()),acfe.getEvents({fieldType:o.get("type")}).map(function(n){n.events.map(function(e){e=this.parseEvent(e);o.on(e[1],e[2],this.proxy(function(e){var t=o.val(),a=this.get(o.cid),i=c(e.currentTarget);(n.callback||this.proxy(function(e,t,a,i,n){var o=e,c=t;"object"==typeof o&&(o=JSON.stringify(o)),(c="object"==typeof c?JSON.stringify(c):c)!==o&&(this.set(a.cid,e),acf.doAction("acfe/change_field",e,t,a,i,n),acf.doAction("acfe/change_field/type="+a.get("type"),e,t,a,i,n),acf.doAction("acfe/change_field/name="+a.get("name"),e,t,a,i,n),acf.doAction("acfe/change_field/key="+a.get("key"),e,t,a,i,n))}))(t,a,o,e,i)}))},this)},this)}}),acfe.registerEventForFieldType("button_group","change"),acfe.registerEventForFieldType("checkbox","change"),acfe.registerEventForFieldType("color_picker","change"),acfe.registerEventForFieldType("date_picker","change"),acfe.registerEventForFieldType("date_time_picker","change"),acfe.registerEventForFieldType("email",["input","change"]),acfe.registerEventForFieldType("file","change"),acfe.registerEventForFieldType("flexible_content","change"),acfe.registerEventForFieldType("gallery","change"),acfe.registerEventForFieldType("google_map","change"),acfe.registerEventForFieldType("image","change"),acfe.registerEventForFieldType("link","change"),acfe.registerEventForFieldType("number",["input","change"]),acfe.registerEventForFieldType("oembed","change"),acfe.registerEventForFieldType("page_link","change"),acfe.registerEventForFieldType("post_object","change"),acfe.registerEventForFieldType("relationship","change"),acfe.registerEventForFieldType("password",["input","change"]),acfe.registerEventForFieldType("radio","change"),acfe.registerEventForFieldType("range",["input","change"]),acfe.registerEventForFieldType("repeater","change"),acfe.registerEventForFieldType("select","change"),acfe.registerEventForFieldType("taxonomy","change"),acfe.registerEventForFieldType("text",["input","change"]),acfe.registerEventForFieldType("textarea",["input","change"]),acfe.registerEventForFieldType("time_picker","change"),acfe.registerEventForFieldType("true_false","change"),acfe.registerEventForFieldType("url",["input","change"]),acfe.registerEventForFieldType("user","change"),acfe.registerEventForFieldType("wysiwyg","change"),acfe.registerEventForFieldType("acfe_advanced_link","change"),acfe.registerEventForFieldType("acfe_block_types","change"),acfe.registerEventForFieldType("acfe_countries","change"),acfe.registerEventForFieldType("acfe_currencies","change"),acfe.registerEventForFieldType("acfe_code_editor","change"),acfe.registerEventForFieldType("acfe_date_range_picker","change"),acfe.registerEventForFieldType("acfe_field_groups","change"),acfe.registerEventForFieldType("acfe_field_types","change"),acfe.registerEventForFieldType("acfe_fields","change"),acfe.registerEventForFieldType("acfe_forms","change"),acfe.registerEventForFieldType("acfe_hidden","change"),acfe.registerEventForFieldType("acfe_image_selector","change"),acfe.registerEventForFieldType("acfe_image_sizes","change"),acfe.registerEventForFieldType("acfe_languages","change"),acfe.registerEventForFieldType("acfe_menu_locations","change"),acfe.registerEventForFieldType("acfe_options_pages","change"),acfe.registerEventForFieldType("acfe_payment","change"),acfe.registerEventForFieldType("acfe_payment_cart","change"),acfe.registerEventForFieldType("acfe_payment_selector","change"),acfe.registerEventForFieldType("acfe_phone_number","change"),acfe.registerEventForFieldType("acfe_post_formats","change"),acfe.registerEventForFieldType("acfe_post_statuses","change"),acfe.registerEventForFieldType("acfe_post_types","change"),acfe.registerEventForFieldType("acfe_recaptcha","change"),acfe.registerEventForFieldType("acfe_taxonomies","change"),acfe.registerEventForFieldType("acfe_taxonomy_terms","change"),acfe.registerEventForFieldType("acfe_templates","change"),acfe.registerEventForFieldType("acfe_user_roles","change"),acfe.registerEventForFieldType("acfe_slug",["input","change"]))}(jQuery),function(r){var i,f,l,n,o,c;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(i=[],acfe.FieldExtender=function(e){var t=acfe.extractVar(e,"id",acf.uniqueId("extender"));return e.type=acfe.getArray(e.type),e.dependencies=acfe.getArray(e.dependencies),e.extender=t,i.push(e),t},f=acf.Field.prototype.setup,acf.Field.prototype.setup=function(e){f.apply(this,arguments);var t=l(this);if(t.length){var a,i=Object.getPrototypeOf(this);this.extenders=[];for(a of t){this.extenders.push(a.extender);var n=r.extend(!0,{},a),o=acfe.extractVar(n,"events");acfe.extractVars(n,"type","condition","dependencies"),n.hasOwnProperty("setup")&&n.setup.apply(this,arguments);function c(){}c.prototype=Object.create(i),r.extend(c.prototype,n),o&&(c.prototype.events=r.extend(!0,{},c.prototype.events,o)),c.prototype.__parent__=i,i=c.prototype}this.getParent=function(e){for(var t=Object.getPrototypeOf(this);t;){if(t.extender===e)return t.__parent__;if(!t.__parent__)return t;t=t.__parent__}return t},Object.setPrototypeOf(this,i)}},l=function(e){var t,a=[];for(t of o(e))a.push(n(t));return a},n=function(e){for(var t of i)if(t.extender===e)return t;return!1},o=function(e){var t,a={};for(t of i)acfe.inArray(e.get("type"),t.type)&&(t.hasOwnProperty("condition")&&!t.condition.apply(e,arguments)||(a[t.extender]=t.dependencies));return c(a)},c=function(e,i=e,t=[],a=0){const n=(e="object"==typeof e&&!Array.isArray(e)?Object.keys(e):e).reduce(function(e,t,a){return i[t].every(Array.prototype.includes,e)&&e.push(t),e},t);t=e.filter(function(e){return!n.includes(e)});return t.length&&a<=e.length?c(t,i,n,a+1):n})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{new_field:"newField"},priority:1,validateField:function(e){return!!e.has("ftype")&&!acf.getFieldType(e.get("type")).prototype.get("ftype")},newField:function(e){this.validateField(e)&&(e.set("rtype",e.get("type"),!0),e.set("type",e.get("ftype"),!0),e.$el.attr("data-type",e.get("ftype")),e.$el.removeAttr("data-ftype"),delete e.data.ftype)}}),function(i){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({field:!1,placement:!1,actions:{new_field:"newField"},newField:function(e){(this.field=e).has("instructionTooltip")&&this.setTooltip(),e.has("instructionAboveField")&&this.setAboveField(),e.has("instructionPlacement")&&this.overridePlacement(e.get("instructionPlacement"))},setTooltip:function(){var e=acfe.versionCompare(acf.get("wp_version"),">=","5.5")?"dashicons-info-outline":"dashicons-info";this.field.$labelWrap().prepend('<span class="acfe-field-tooltip acf-js-tooltip dashicons '+e+'" title="'+acf.escHtml(this.field.get("instructionTooltip"))+'"></span>'),this.field.$labelWrap().find(".description").remove()},setAboveField:function(){this.field.$inputWrap().prepend('<p class="description">'+this.field.get("instructionAboveField")+"</p>"),this.field.$labelWrap().find(".description").remove()},overridePlacement:function(e){var t=this.getPlacement();t&&t!==e&&this.setPlacement(e)},getPlacement:function(){var e=!1;return this.field.$labelWrap().find(">.description").length?e="label":this.field.$inputWrap().find(">.description:first-child").length?e="above_field":this.field.$inputWrap().find(">.description:last-child").length?e="field":this.field.$labelWrap().find(">.acfe-field-tooltip").length&&(e="tooltip"),this.placement=e,this.placement},$getInstruction:function(){var e=this.getPlacement();return"label"===e?this.field.$labelWrap().find(">.description"):"above_field"===e?this.field.$inputWrap().find(">.description:first-child"):"field"===e?this.field.$inputWrap().find(">.description:last-child"):"tooltip"===e&&this.field.$labelWrap().find(">.acfe-field-tooltip")},setPlacement:function(e){var t,a=this.$getInstruction();"tooltip"===this.placement&&(t=a.attr("title"),a.remove(),a=i('<p class="description">'+t+"</p>")),"label"===e?this.field.$labelWrap().append(a):"above_field"===e?this.field.$inputWrap().prepend(a):"field"===e?this.field.$inputWrap().append(a):"tooltip"===e&&(e=acfe.versionCompare(acf.get("wp_version"),">=","5.5")?"dashicons-info-outline":"dashicons-info",this.field.$labelWrap().prepend(i('<span class="acfe-field-tooltip acf-js-tooltip dashicons '+e+'" title="'+acf.escHtml(a.html())+'"></span>')),a.remove())}})}(jQuery),function(n){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{new_field:"newField"},getFieldType:function(e){return e.get("rtype",e.get("type"))},validateField:function(e){return!!e.has("acfeLabels")&&("checkbox"===this.getFieldType(e)||"radio"===this.getFieldType(e))},newField:function(e){if(this.validateField(e)){var t,a,i=e.get("acfeLabels");switch(this.getFieldType(e)){case"checkbox":for(t in i)a=i[t],e.$control().find('input[type=checkbox][value="'+a+'"]').closest("ul").before("<strong>"+t+"</strong>");break;case"radio":for(t in i)a=i[t],e.$control().find('input[type=radio][value="'+a+'"]').closest("li").addClass("parent").prepend("<strong>"+t+"</strong>");e.$control().hasClass("acf-hl")&&e.$control().find("li.parent").each(function(){n(this).nextUntil("li.parent").addBack().wrapAll("<li><ul></ul></li>")})}}}})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.Field.prototype.getModal=function(e){var t=acfe.findModal("",this.$inputWrap());return!!t.length&&acfe.getModal(t,e)}),function(r){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{select2_init:"init"},filters:{select2_args:"args",select2_ajax_data:"ajaxData"},init:function(e,t,a,i,n){i&&(acf.doAction("select2_init/type="+i.get("type"),e,t,a,i,n),acf.doAction("select2_init/name="+i.get("name"),e,t,a,i,n),acf.doAction("select2_init/key="+i.get("key"),e,t,a,i,n))},args:function(e,i,n,o,c){return o&&(e=acf.applyFilters("select2_args/type="+o.get("type"),e,i,n,o,c),e=acf.applyFilters("select2_args/name="+o.get("name"),e,i,n,o,c),e=acf.applyFilters("select2_args/key="+o.get("key"),e,i,n,o,c),acf.isset(window,"jQuery","fn","selectWoo")||(e.templateSelection=function(e){var t=e.text,t=acf.applyFilters("select2_template_selection",t,e,i,n,o,c);t=acf.applyFilters("select2_template_selection/type="+o.get("type"),t,e,i,n,o,c),t=acf.applyFilters("select2_template_selection/name="+o.get("name"),t,e,i,n,o,c),t=acf.applyFilters("select2_template_selection/key="+o.get("key"),t,e,i,n,o,c);var a=r('<span class="acf-selection"></span>');return a.html(acf.escHtml(t)),a.data("element",e.element),a},e.templateResult=function(e){var t=e.text,t=acf.applyFilters("select2_template_result",t,e,i,n,o,c);t=acf.applyFilters("select2_template_result/type="+o.get("type"),t,e,i,n,o,c),t=acf.applyFilters("select2_template_result/name="+o.get("name"),t,e,i,n,o,c),t=acf.applyFilters("select2_template_result/key="+o.get("key"),t,e,i,n,o,c);var a=r('<span class="acf-selection"></span>');return a.html(acf.escHtml(t)),a.data("element",e.element),a},acfe.versionCompare(acf.get("acf_version"),"<","5.10")&&(e.escapeMarkup=function(e){return"string"!=typeof e?e:acf.escHtml(e)}))),e},ajaxData:function(e,t,a,i,n){return i?(e=acf.applyFilters("select2_ajax_data/type="+i.get("type"),e,t,a,i,n),e=acf.applyFilters("select2_ajax_data/name="+i.get("name"),e,t,a,i,n),(e=acf.applyFilters("select2_ajax_data/key="+i.get("key"),e,t,a,i,n)).action?acf.applyFilters("select2_ajax_data/action="+e.action,e,t,a,i,n):e):e}})}(jQuery),function(c){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.Form=acf.Model.extend({notice:!1,validator:!1,events:{"click .button":"onClickSubmit",'click [type="submit"]':"onClickSubmit",invalidField:"onInvalidField","changed:status":"onChangeStatus",showErrors:"onShowErrors"},setup:function(e){e=(this.$el=e).data("cid");c.extend(this.data,acfe.getFormData(e)),this.$el.data("acfe_form",this)},initialize:function(){this.$el.removeData("acf"),this.get("hide_unload")&&acf.unload.disable()},onClickSubmit:function(e,t){t.hasClass("disabled")&&e.preventDefault()},onInvalidField:function(e,t){var a=acf.getField(c(e.target));if(a.notice&&"error"===a.notice.get("type"))switch(this.get("error_class")&&a.notice.$el.addClass(this.get("error_class")),this.get("error_position")){case"hide":a.notice.remove();break;case"below":a.$control().length?a.notice.$el.insertAfter(a.$control()):a.$inputWrap().length&&a.notice.$el.appendTo(a.$inputWrap()),a.notice.$el.addClass("-below");break;case"group":var i=acfe.getTextNode(a.$labelWrap().find("label")).trim(),n=a.$('.acf-input-wrap [placeholder!=""]').attr("placeholder"),o=a.notice.$el.text().trim();a.notice.remove(),o=i&&i.length&&"*"!==i?`${i}: ${o}`:n&&n.length&&""!==n?`${n}: ${o}`:`${acfe.ucFirst(a.get("name")).replace(/_/g," ")}: ${o}`,this.notice&&this.notice.$el.append(acf.escHtml(`<p>${o}</p>`))}},onChangeStatus:function(e,t,a,i){switch(a){case"validating":if(this.validator)return;var n=this.$el.data("acf"),o=Object.getPrototypeOf(n).showErrors;n.showErrors=function(){o.apply(this,arguments),n.trigger("showErrors")},this.validator=n;break;case"invalid":if("group"===this.get("error_position")){if(!this.validator.getFieldErrors().length){this.notice&&(this.notice.remove(),this.notice=!1);break}this.notice?this.notice.update({type:"error",html:""}):this.notice=acf.newNotice({type:"error",target:this.$el}),this.get("error_class")&&this.notice.$el.addClass(this.get("error_class")),this.notice.$el.find("p:empty").remove(),this.setTimeout(function(){acfe.scrollTo(this.notice.$el)},20)}break;case"valid":"group"===this.get("error_position")&&this.notice&&(this.notice.remove(),this.notice=!1),this.get("hide_revalidation")&&this.validator.has("notice")&&(this.validator.get("notice").remove(),this.validator.set("notice",null))}},onShowErrors:function(e,t){var a,i,n,o;this.get("hide_error")?this.validator.has("notice")&&(this.validator.get("notice").remove(),this.validator.set("notice",null)):this.validator.has("notice")&&(a=this.validator.getFieldErrors(),i=this.validator.getGlobalErrors(),n=0,a.map(function(e){var t=this.validator.$('[name="'+e.input+'"]').first();(t=!t.length?this.validator.$('[name^="'+e.input+'"]').first():t).length&&n++},this),o=this.get("messages.failure"),i.map(function(e){o+=o.length?". ":"",o+=e.message}),1===n&&this.get("messages.error")?(o+=o.length?". ":"",o+=this.get("messages.error")):1<n&&this.get("messages.errors")&&(o+=o.length?". ":"",o+=this.get("messages.errors").replace("%d",n)),this.validator.get("notice").update({text:o}))},set:function(e,t,a){var i=this.get(e);if(i===t)return this;var n=e.split("."),o=n.shift(),c=this.get(o);acfe.arraySet(this.data,e,t),acfe.setFormData(this.get("cid"),e,t);var r=this.get(o);return a||(this.changed=!0,1<n.length&&this.trigger(`changedData:${o}`,[r,c]),this.trigger("changedData:"+e,[t,i]),this.trigger("changedData",[e,t,i])),this}}),acf.addAction("acfe/form/validation_success",function(e,t,a){t.has("notice")&&t.get("notice").update({type:"success",text:a.get("messages.success"),timeout:1e3})}))}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({wait:"prepare",priority:1,initialize:function(){acfe.get("is_admin")||new e}}),e=acf.Model.extend({actions:{"new_field/type=date_picker":"datePicker","new_field/type=date_time_picker":"datePicker","new_field/type=time_picker":"datePicker","new_field/type=acfe_date_range_picker":"datePicker","new_field/type=google_map":"googleMap"},datePicker:function(e){var t=e.getForm();t&&t.get("field_class")&&e.$inputText().addClass(t.get("field_class"))},googleMap:function(e){var t=e.getForm();t&&t.get("field_class")&&e.$search().addClass(t.get("field_class"))}}))}(jQuery),function(c){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.Field.prototype.getForm=function(){return acfe.getForm(this.$el.closest(".acfe-form"))},acfe.findForms=function(t){acfe.isObject(t)||(t={name:t});var e=".acfe-form",a=!1;if((t=acf.parseArgs(t,{cid:[],name:[],parent:!1,limit:!1})).cid=acfe.getArray(t.cid),t.name=acfe.getArray(t.name),t.name.length){for(var i of t.name)acfe.getFormsData({name:i}).map(function(e){t.cid.push(e.cid)});if(!t.cid.length)return c()}if(t.cid.length){var n,o=[];for(n of t.cid)o.push(e+'[data-cid="'+n+'"]');e=o.join(",")}return a=t.parent?t.parent.find(e):c(e),a=t.limit?a.slice(0,t.limit):a},acfe.getForms=function(e){e instanceof jQuery||(e=acfe.findForms(e));var t=[];return e.each(function(){var e=acfe.getForm(c(this));e&&t.push(e)}),t},acfe.getForm=function(e){if(e instanceof jQuery){if(!e.hasClass("acfe-form"))return!1}else e=acfe.findForm(e);return!!e.length&&(e.data("acfe_form")||acfe.newForm(e))},acfe.findForm=function(e){return acfe.isObject(e)||(e={name:e}),e=acf.parseArgs(e,{limit:1}),acfe.findForms(e)},acfe.newForm=function(e){e=new acfe.Form(e);return acf.doAction("acfe/new_form",e),acf.doAction(`acfe/new_form/form=${e.get("name")}`,e),e},acfe.getFormsData=function(e){e instanceof acfe.Form?e={form:e}:acfe.isObject(e)||(e={cid:e}),(e=acf.parseArgs(e,{form:"",cid:"",name:"",success:""})).form&&e.form instanceof acfe.Form&&(e.cid=e.form.get("cid"));var t,a,i,n,o=[],c=acfe.get("forms",{});if(!Object.keys(c).length)return o;for(t in c)c[t]&&(n=i=a=!0,e.cid.length&&(a=e.cid===t),e.name.length&&(i=e.name===c[t].name),""!==e.success&&(n=e.success===c[t].success),a&&i&&n&&o.push(c[t]));return o},acfe.getFormData=function(e){e=acfe.getFormsData(e);return!!e.length&&e.shift()},acfe.setFormData=function(e,t,a){acfe.set(`forms.${e}.${t}`,a)},acfe.renderForm=function(e){e&&e.length&&e.hasClass("acfe-form")&&(e=e.parent()),acf.doAction("append",e)},acfe.renderFormAjax=function(t){var a;(t=acf.parseArgs(t,{url:acf.get("ajaxurl"),data:acf.prepareForAjax({action:"acfe/form/render_form_ajax"}),type:"post",dataType:"html",success:function(){},replace:"html",form:"",target:""})).form&&(t.data.form=t.form),t.target&&(a=t.success,t.success=function(e){acfe.isString(t.target)&&(t.target=c(t.target)),t.target[t.replace](c(e)),acfe.renderForm(t.target),a(e)}),c.ajax(t)})}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({wait:"prepare",priority:5,initialize:function(){acfe.get("is_admin")||new e}}),e=acf.Model.extend({actions:{validation_begin:"validationBegin",validation_failure:"validationFailure",validation_success:"validationSuccess",submit:"submit"},filters:{validation_complete:"validationComplete"},validationBegin:function(e){var t,a=acfe.getForm(e);a&&(t=e.data("acf"),acf.doAction("acfe/form/validation_begin",e,t,a),acf.doAction(`acfe/form/validation_begin/form=${a.get("name")}`,e,t,a))},validationFailure:function(e,t){var a=acfe.getForm(e);a&&(acf.doAction("acfe/form/validation_failure",e,t,a),acf.doAction(`acfe/form/validation_failure/form=${a.get("name")}`,e,t,a))},validationSuccess:function(e,t){var a=acfe.getForm(e);a&&(acf.doAction("acfe/form/validation_success",e,t,a),acf.doAction(`acfe/form/validation_success/form=${a.get("name")}`,e,t,a))},submit:function(e){var t,a=acfe.getForm(e);a&&(t=e.data("acf"),acf.doAction("acfe/form/submit",e,t,a),acf.doAction(`acfe/form/submit/form=${a.get("name")}`,e,t,a))},validationComplete:function(e,t,a){var i=acfe.getForm(t);return i&&(e=acf.applyFilters("acfe/form/validation_complete",e,t,a,i),e=acf.applyFilters(`acfe/form/validation_complete/form=${i.get("name")}`,e,t,a,i)),e}}))}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&["prepare","ready","load","append"].map(function(e){acf.addAction(e,function(e){acfe.get("is_admin")||acfe.getForms({parent:e})},1)}),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({wait:"ready",priority:15,initialize:function(){!acfe.get("is_admin")&&acf.isset(window,"wp","media","view","settings","post")&&new e}}),e=acf.Model.extend({defaultPostId:null,actions:{new_media_popup:"newMediaPopup","acfe/before_editor_media_popup":"beforeEditorPopup","acfe/open_editor_media_popup":"openEditorPopup","acfe/close_editor_media_popup":"closeEditorPopup"},filters:{"acfe/select_media_popup/args":"mediaPopupArgs","acfe/select_media_popup/frame_options":"mediaPopupFrameOptions"},initialize:function(){this.defaultPostId=wp.media.view.settings.post.id},resetPostId:function(){wp.media.view.settings.post.id=this.defaultPostId},newMediaPopup:function(e){null!==e.get("attachTo")&&(wp.media.view.settings.post.id=e.get("attachTo")),e.frame.on("close",this.proxy(this.resetPostId))},beforeEditorPopup:function(e){e=this.getFieldAttachTo(e);!1!==e&&(wp.media.view.settings.post.id=e)},openEditorPopup:function(e){e=this.getFieldAttachTo(e);!1!==e&&(wp.media.view.settings.post.id=e)},closeEditorPopup:function(e){this.setTimeout(this.resetPostId,100)},mediaPopupArgs:function(e,t){t=this.getFieldAttachTo(t);return!1!==t&&(e.attachTo=t),e},mediaPopupFrameOptions:function(e,t){return"uploadedTo"===t.get("library")&&null!==t.get("attachTo")&&(e.library.uploadedTo=t.get("attachTo")),e},getFieldAttachTo:function(t){var e=t.getForm();if(!e)return!1;var a=0;return e.get("media")&&e.get("media").some(function(e){if(acfe.inArray(t.get("key"),e.fields))return a=e.post_id,!0}),a}}))}(jQuery),function(n){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({wait:"prepare",priority:5,initialize:function(){acfe.get("is_admin")||new e}}),e=acf.Model.extend({initialize:function(){var e,t,a,i=this.getFormSuccess();i&&(e=i.data,t=!!i.form&&i.form.$el,a=i.form||!1,i=i.form?i.form.get("success_data"):i.data.success_data,acf.doAction("acfe/form/submit_success",t,a,i),acf.doAction(`acfe/form/submit_success/form=${e.name}`,t,a,i),acfe.doActionDeprecated("acfe/form/success",[t],"*******","acfe/form/submit_success"),acfe.doActionDeprecated(`acfe/form/success/id=${e.id}`,[t],"*******",`acfe/form/submit_success/form=${e.name}`),acfe.doActionDeprecated(`acfe/form/success/form=${e.name}`,[t],"*******",`acfe/form/submit_success/form=${e.name}`),acfe.doActionDeprecated(`acfe/form/success/name=${e.name}`,[t],"*******",`acfe/form/submit_success/form=${e.name}`),acfe.doActionDeprecated("acfe/form/submit/success",[t],"*******","acfe/form/submit_success"),acfe.doActionDeprecated(`acfe/form/submit/success/id=${e.id}`,[t],"*******",`acfe/form/submit_success/form=${e.name}`),acfe.doActionDeprecated(`acfe/form/submit/success/name=${e.name}`,[t],"*******",`acfe/form/submit_success/form=${e.name}`),e.scroll&&(e.selector?acfe.scrollTo(n(e.selector)):t&&acfe.scrollTo(t.prev())))},getFormSuccess:function(){var e=acfe.getFormData({success:!0});return!!e&&{form:acfe.getForm({cid:e.cid}),data:e}}}))}(jQuery),function(r){var e,t,a;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=function(){var a=this.parent(),e=a&&"repeater"===a.get("type"),t={mode:"select",field:this.get("key"),multiple:e,library:this.get("library"),allowedTypes:this.get("mime_types"),select:r.proxy(function(e,t){0<t?this.append(e,a):this.render(e)},this)};switch(this.get("type")){case"image":t.type="image",t.title=acf.__("Select Image");break;case"file":t.title=acf.__("Select File")}t=acf.applyFilters("acfe/select_media_popup/args",t,this),t=acf.applyFilters(`acfe/select_media_popup/args/type=${this.get("type")}`,t,this),t=acf.applyFilters(`acfe/select_media_popup/args/name=${this.get("name")}`,t,this);t=acf.applyFilters(`acfe/select_media_popup/args/key=${this.get("key")}`,t,this),acf.newMediaPopup(t)},acf.models.ImageField.prototype.selectAttachment=e,acf.models.FileField.prototype.selectAttachment=e,e=function(){var e=this.val();if(!e)return!1;var t={mode:"edit",attachment:e,field:this.get("key"),select:r.proxy(function(e,t){this.render(e)},this)};switch(this.get("type")){case"image":t.title=acf.__("Edit File"),t.button=acf.__("Update File");break;case"file":t.title=acf.__("Edit Image"),t.button=acf.__("Update Image")}t=acf.applyFilters("acfe/edit_media_popup/args",t,this),t=acf.applyFilters(`acfe/edit_media_popup/args/type=${this.get("type")}`,t,this),t=acf.applyFilters(`acfe/edit_media_popup/args/name=${this.get("name")}`,t,this);t=acf.applyFilters(`acfe/edit_media_popup/args/key=${this.get("key")}`,t,this),acf.newMediaPopup(t)},acf.models.ImageField.prototype.editAttachment=e,acf.models.FileField.prototype.editAttachment=e,acf.models.GalleryField.prototype.onClickAdd=function(e,t){var a;this.isFull()?this.showNotice({text:acf.__("Maximum selection reached"),type:"warning"}):(a={mode:"select",title:acf.__("Add Image to Gallery"),field:this.get("key"),multiple:"add",library:this.get("library"),allowedTypes:this.get("mime_types"),selected:this.val(),select:r.proxy(function(e,t){this.appendAttachment(e,t)},this)},a=acf.applyFilters("acfe/select_media_popup/args",a,this),a=acf.applyFilters(`acfe/select_media_popup/args/type=${this.get("type")}`,a,this),a=acf.applyFilters(`acfe/select_media_popup/args/name=${this.get("name")}`,a,this),a=acf.applyFilters(`acfe/select_media_popup/args/key=${this.get("key")}`,a,this),acf.newMediaPopup(a))},acf.models.GalleryField.prototype.editAttachment=function(e){e={mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:e,field:this.get("key"),select:r.proxy(function(e,t){this.renderAttachment(e)},this)},e=acf.applyFilters("acfe/edit_media_popup/args",e,this);e=acf.applyFilters(`acfe/edit_media_popup/args/type=${this.get("type")}`,e,this),e=acf.applyFilters(`acfe/edit_media_popup/args/name=${this.get("name")}`,e,this),e=acf.applyFilters(`acfe/edit_media_popup/args/key=${this.get("key")}`,e,this);acf.newMediaPopup(e)},t=acf.models.SelectMediaPopup.prototype.getFrameOptions,acf.models.SelectMediaPopup.prototype.getFrameOptions=function(){var e=t.apply(this,arguments);return e=acf.applyFilters("acfe/select_media_popup/frame_options",e,this)},a=acf.models.EditMediaPopup.prototype.getFrameOptions,acf.models.EditMediaPopup.prototype.getFrameOptions=function(){var e=a.apply(this,arguments);return e=acf.applyFilters("acfe/edit_media_popup/frame_options",e,this)},new acf.Model({wait:"prepare",done:[],initialize:function(){acf.isset(window,"wp","media","editor")&&this.customizeEditor()},customizeEditor:function(){var o=this,c=wp.media.editor.add;wp.media.editor.add=function(e,t){if("string"!=typeof e||!e.startsWith("acf-editor")||o.done.includes(e))return c.apply(this,arguments);o.done.push(e);var a=r("#"+e).closest(".acf-field");if(!a.length)return c.apply(this,arguments);var i=acf.getInstance(a);if(!i)return c.apply(this,arguments);acf.doAction("acfe/before_editor_media_popup",i);var n=c.apply(this,arguments);return acf.doAction("acfe/new_editor_media_popup",i,n),n.on("open",function(){acf.doAction("acfe/open_editor_media_popup",i,n)}),n.on("close",function(){acf.doAction("acfe/close_editor_media_popup",i,n)}),n}}}))}(jQuery),function(a){var i,n;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.validation.onClickSubmit=function(e,t){i(t),this.set("originalEvent",e)},i=function(e){var t=a(".acf-field input"),e=e.closest("form");(t=e.length?e.find(".acf-field input"):t).each(function(){this.checkValidity()||n(a(this))})},n=function(e){e=e.parents(".acf-postbox");!e.length||(e=acf.getPostbox(e))&&(e.$el.hasClass("hide-if-js")||"none"==e.$el.css("display"))&&(e.$el.removeClass("hide-if-js"),e.$el.css("display",""))})}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({type:"acfe_advanced_link",events:{'click a[data-name="add"]':"onClickEdit",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove"},$control:function(){return this.$(".acf-link")},getSubField:function(e){return acf.getFields({key:e,parent:this.$el}).shift()},getSubFields:function(){return acf.getFields({parent:this.$el})},getValue:function(){var e={type:this.getSubField("type").val(),title:this.getSubField("title").val(),value:"",name:"",target:Boolean(this.getSubField("target").val())};return e.value=this.getSubField(e.type).val(),e.name=e.value,"post"!==e.type&&"term"!==e.type||(e.name=this.getSubField(e.type).$input().find(":selected").text()),e},setValue:function(e){if(!e)return this.clearValue();acfe.isString(e)&&(e={type:"url",title:"",value:e,target:!1}),e=acf.parseArgs(e,{type:"url",value:"",title:"",target:!1}),this.getSubField("type").val(e.type),this.getSubField(e.type).val(e.value),this.getSubField("title").val(e.title),this.getSubField("target").val(e.target),this.renderValue()},clearValue:function(){this.getSubFields().map(function(e){e.val("")})},renderValue:function(){var e=this.val(),t=this.$control();t.removeClass("-value -external"),(e.value||e.title)&&t.addClass("-value"),e.target&&t.addClass("-external");t="url"===e.type?e.value:"#";this.$(".link-title").html(e.title),this.$(".link-url").attr("href",t).html(e.name)},onClickEdit:function(e,t){this.getModal({open:!0,onClose:this.proxy(function(){this.renderValue()})})},onClickRemove:function(e,t){this.clearValue(),this.renderValue()}}),acf.registerFieldType(e),new acf.Model({filters:{"select2_ajax_data/action=acfe/fields/advanced_link/post_query":"ajaxData"},ajaxData:function(e,t,a,i,n){i=i.parent();return i&&(e.field_key=i.get("key")),e}}))}(jQuery),function(i){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({type:"acfe_button",events:{"click input":"onClick","click button":"onClick"},$input:function(){return this.$("input").length?this.$("input"):this.$("button").length?this.$("button"):void 0},initialize:function(){var e=this.$input();this.inherit(e)},onClick:function(e,t){var a;this.get("ajax")&&(e.preventDefault(),a={action:"acfe/fields/button",field_key:this.get("key"),acf:acf.serialize(this.$el.closest("form"),"acf")},a=acf.applyFilters("acfe/fields/button/data",a,this.$el),a=acf.applyFilters("acfe/fields/button/data/name="+this.get("name"),a,this.$el),a=acf.applyFilters("acfe/fields/button/data/key="+this.get("key"),a,this.$el),acf.doAction("acfe/fields/button/before_ajax",this.$el,a),acf.doAction("acfe/fields/button/before",this.$el,a),acf.doAction("acfe/fields/button/before/name="+this.get("name"),this.$el,a),acf.doAction("acfe/fields/button/before/key="+this.get("key"),this.$el,a),i.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"json",context:this,success:function(e){acf.doAction("acfe/fields/button/ajax_success",e,this.$el,a),acf.doAction("acfe/fields/button/success",e,this.$el,a),acf.doAction("acfe/fields/button/success/name="+this.get("name"),e,this.$el,a),acf.doAction("acfe/fields/button/success/key="+this.get("key"),e,this.$el,a)},complete:function(e){e=e.responseText;acf.doAction("acfe/fields/button/complete",e,this.$el,a),acf.doAction("acfe/fields/button/complete/name="+this.get("name"),e,this.$el,a),acf.doAction("acfe/fields/button/complete/key="+this.get("key"),e,this.$el,a)}}))}}),acf.registerFieldType(e))}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.models.CheckboxField,acf.models.CheckboxField=e.extend({setValue:function(e){if(!e)return this.clearValue();acfe.getArray(e).map(function(e){e=this.$(':checkbox[value="'+e+'"]');e.length&&!e.is(":checked")&&e.prop("checked",!0).trigger("change")},this)},clearValue:function(){var e=this.$inputs(),t=this.$("label");e.prop("checked",!1),t.removeClass("selected")}}))}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({wait:!1,type:"clone",events:{duplicateField:"onDuplicate"},initialize:function(){this.has("acfeCloneModal")&&(this.$el.find("> .acf-input > .acf-fields, > .acf-input > .acf-table").wrapAll('<div class="acfe-modal"><div class="acfe-modal-wrapper"><div class="acfe-modal-content"></div></div></div>'),this.$el.find("> .acf-input").append('<a href="#" class="acf-button button" data-modal>'+this.get("acfeCloneModalButton")+"</a>"),this.initializeModal())},initializeModal:function(){var e,t=this.$labelWrap().find("label").text().trim();this.$el.is("td")&&(t=this.get("acfeCloneModalButton"),(e=this.$el.closest("table").find(' > thead th[data-key="'+this.get("key")+'"]')).length&&(t=acfe.getTextNode(e))),t.length||(t=this.get("acfeCloneModalButton")),this.getModal({title:t,size:this.has("acfeCloneModalSize")?this.get("acfeCloneModalSize"):"large",footer:!!this.has("acfeCloneModalClose")&&acf.__("Close"),class:"acfe-modal-edit-"+this.get("name")+" acfe-modal-edit-"+this.get("key")})},onDuplicate:function(e,t,a){a.find(".acf-input:first > a[data-modal]").remove()}}),acf.registerFieldType(e))}(jQuery),function(t){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({wait:!1,type:"acfe_code_editor",editor:{},events:{showField:"onShow",duplicateField:"onDuplicate"},$control:function(){return this.$("> .acf-input > .acf-input-wrap")},$input:function(){return this.$control().find("> textarea")},initialize:function(){var e;acf.isset(wp,"codeEditor")&&(e={lineNumbers:this.get("lines"),lineWrapping:!0,styleActiveLine:!1,continueComments:!0,indentUnit:this.get("indentUnit"),tabSize:1,indentWithTabs:!1,autoRefresh:!0,mode:this.get("mode"),extraKeys:{Tab:function(e){e.execCommand("indentMore")},"Shift-Tab":function(e){e.execCommand("indentLess")}}},e=acf.applyFilters("acfe/fields/code_editor/args",e,this),e=acf.applyFilters("acfe/fields/code_editor/args/name="+this.get("name"),e,this),e=acf.applyFilters("acfe/fields/code_editor/args/key="+this.get("key"),e,this),this.editor=wp.codeEditor.initialize(this.$input().get(0),{codemirror:t.extend(wp.codeEditor.defaultSettings.codemirror,e)}),this.get("rows")&&(this.editor.codemirror.getScrollerElement().style.minHeight=18.5*this.get("rows")+"px"),this.get("maxRows")&&(this.editor.codemirror.getScrollerElement().style.maxHeight=18.5*this.get("maxRows")+"px"),this.editor.codemirror.on("change",this.proxy(this.onEditorChange)),acf.doAction("acfe/fields/code_editor/init",this.editor,this),acf.doAction("acfe/fields/code_editor/init/name="+this.get("name"),this.editor,this),acf.doAction("acfe/fields/code_editor/init/key="+this.get("key"),this.editor,this))},onEditorChange:function(e,t){this.editor.codemirror.save(),this.$input().change()},onShow:function(){this.editor.codemirror&&this.editor.codemirror.refresh()},onDuplicate:function(e,t,a){a.find(".CodeMirror:last").remove()}}),acf.registerFieldType(e))}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({wait:"new_field",type:"acfe_column",$control:function(){return this.$(".acf-fields:first")},initialize:function(){if(this.$el.is("td")&&(this.$el.closest(".acf-table").find('th[data-type="acfe_column"]').remove(),this.remove()),this.get("endpoint"))return this.$el.find("> .acf-label").remove(),void this.$el.find("> .acf-input").remove();var e=this.$el,t=this.$el.find("> .acf-label"),a=(this.$inputWrap(),this.$control());t.remove();t=e.parent();t.addClass("acfe-column-wrapper"),a.addClass(t.hasClass("-left")?"-left":""),a.addClass(t.hasClass("-clear")?"-clear":""),a.append(e.nextUntil(".acf-field-acfe-column",".acf-field"))}}),acf.registerFieldType(e))}(jQuery),function(t){var a;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(a=acf.models.FlexibleContentField,acf.models.FlexibleContentField=a.extend({addSortable:function(a){1!=this.get("max")&&this.$layoutsWrap().sortable({items:" > .layout",handle:"> .acf-fc-layout-handle",forceHelperSize:!1,forcePlaceholderSize:!0,revert:50,tolerance:"pointer",scroll:!0,stop:function(e,t){a.render()},update:function(e,t){a.$input().trigger("change")}})},add:function(e){var t=a.prototype.add.apply(this,arguments);t.length&&t.data("added",!0)}}),new acf.Model({actions:{invalid_field:"onInvalidField",valid_field:"onValidField"},onInvalidField:function(e){e.$el.parents(".layout").addClass("acfe-flexible-modal-edit-error")},onValidField:function(e){e.$el.parents(".layout").each(function(){var e=t(this);e.find(".acf-error").length||e.removeClass("acfe-flexible-modal-edit-error")})}}))}(jQuery),function(i){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acfe.FieldExtender({id:"fc_append",type:"flexible_content",initialize:function(){this.getParent(e).initialize.apply(this,arguments),this.addEvents({appendLayout:"acfeAppendLayout"})},acfeAppendLayout:function(e,t,a){a.is(".acfe-layout-duplicated")||(this.has("acfeFlexibleModalEdition")?this.acfeModalEdit(null,a):this.openLayout(a));var i=acfe.getModal(a.closest(".acfe-modal.-open"));i?this.acfeScrollToLayout(a,i.$content()):a.is(".acfe-layout-duplicated")?this.acfeScrollToLayout(a):this.setTimeout(function(){a.data("added")&&this.acfeScrollToLayout(a)},10)},acfeScrollToLayout:function(e,t){var a=t||!1;t=t||i("body, html"),acf.isInView(e)||(e=a?e.position().top:e.offset().top-i(window).height()/2,t.animate({scrollTop:e},500))}}))}(jQuery),function(o){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_async",type:"flexible_content",condition:function(){return this.has("acfeFlexibleAjax")},add:function(n){if(n=acf.parseArgs(n,{layout:"",before:!1}),!this.allowAdd())return!1;var e={action:"acfe/flexible/models",field_key:this.get("key"),layout:n.layout},t=this.proxy(function(e){if(e){var t=o(e),a=acf.uniqid(),i="acf["+this.get("key")+"][acfcloneindex]",e=this.$control().find("> input[type=hidden]").attr("name")+"["+a+"]",e=acf.duplicate({target:t,search:i,replace:e,append:this.proxy(function(e,t){n.before?n.before.before(t):this.$layoutsWrap().append(t),acf.enable(t,this.cid),this.render()})});return e.attr("data-id",a),this.$input().trigger("change"),e}});o.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(e),dataType:"html",type:"post",beforeSend:function(){o("body").addClass("-loading")},success:t,complete:function(){o("body").removeClass("-loading")}})}})}(jQuery),function(f){var n;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&((n=acf.getFieldType("flexible_content").prototype).events["click [data-acfe-flexible-control-clone]"]="acfeCloneLayout",n.acfeCloneLayout=function(e,t){var a,i=this,n=t.closest(".layout"),o=n.data("layout"),c=f(i.$popup().html()),r=i.$layouts(),c=c.find('[data-layout="'+o+'"]'),c=(c.data("min"),c.data("max")||0),o=(a=o,r.filter(function(){return f(this).data("layout")===a}).length);if(c&&c<=o)return t.addClass("disabled"),!1;t.removeClass("disabled"),i.acfeFixInputs(n);o=n.clone();i.acfeCleanLayouts(o);t=t.closest(".acf-flexible-content").find("> input[type=hidden]").attr("name"),i.acfeDuplicate({layout:o,before:n,parent:t})},n.events["click [data-acfe-flexible-control-copy]"]="acfeCopyLayout",n.acfeCopyLayout=function(e,t){var a=t.closest(".layout").clone(),t=this.$control().find("> input[type=hidden]").attr("name");this.acfeFixInputs(a),this.acfeCleanLayouts(a);a=JSON.stringify({source:t,layouts:a[0].outerHTML});acfe.copyClipboard(a,{auto:acf.__("Layout data has been copied to your clipboard.")+"\n"+acf.__('You can now paste it on another page, using the "Paste" button action.'),manual:acf.__("Please copy the following data to your clipboard.")+"\n"+acf.__('You can then paste it on another page, using the "Paste" button action.')})},n.acfeCopyLayouts=function(){var e=this.$layoutsWrap().clone(),t=this.$control().find("> input[type=hidden]").attr("name");this.acfeFixInputs(e),this.acfeCleanLayouts(e);e=JSON.stringify({source:t,layouts:e.html()});acfe.copyClipboard(e,{auto:acf.__("Layouts data have been copied to your clipboard.")+"\n"+acf.__('You can now paste it on another page, using the "Paste" button action.'),manual:acf.__("Please copy the following data to your clipboard.")+"\n"+acf.__('You can then paste it on another page, using the "Paste" button action.')})},n.acfePasteLayouts=function(){var n=this,e=prompt(acf.__("Please paste previously copied layout data in the following field:"));if(null!=e&&""!==e)try{var t=JSON.parse(e),i=t.source,a=f(t.layouts).closest("[data-layout]");if(!a.length)return alert("No layouts data available");var o=f(n.$popup().html()),c=n.$layouts(),r=[];if(a.each(function(){var e,t=f(this),a=t.data("layout"),i=o.find('[data-layout="'+a+'"]'),i=(i.data("min"),i.data("max")||0),a=(e=a,c.filter(function(){return f(this).data("layout")===e}).length);i&&i<=a||n.$clone(t.attr("data-layout")).length&&r.push(t)}),!r.length)return alert("No layouts could be pasted");f.each(r,function(){var e=f(this),t=i+"["+e.attr("data-id")+"]",a=n.$control().find("> input[type=hidden]").attr("name");n.acfeDuplicate({layout:e,before:!1,search:t,parent:a})})}catch(e){console.log(e),alert("Invalid data")}},n.events['click [data-name="acfe-flexible-control-button"]']="acfeControl",n.acfeControl=function(e,t){var a=this,i=t.next(".tmpl-acfe-flexible-control-popup").html();new(acf.models.TooltipConfirm.extend({render:function(){this.html(this.get("text")),this.$el.addClass("acf-fc-popup")}}))({target:t,targetConfirm:!1,text:i,context:a,confirm:function(e,t){"paste"===t.attr("data-acfe-flexible-control-action")?a.acfePasteLayouts():"copy"===t.attr("data-acfe-flexible-control-action")&&a.acfeCopyLayouts()}}).on("click","a","onConfirm")},n.acfeDuplicate=function(a){if(a=acf.parseArgs(a,{layout:"",before:!1,parent:!1,search:"",replace:""}),!this.allowAdd())return!1;var i=acf.uniqid();a.parent&&(a.search||(a.search=a.parent+"["+a.layout.attr("data-id")+"]"),a.replace=a.parent+"["+i+"]");var e,t={target:a.layout,search:a.search,replace:a.replace,append:this.proxy(function(e,t){t.addClass("acfe-layout-duplicated"),t.attr("data-id",i),a.before?a.before.after(t):this.$layoutsWrap().append(t),acf.enable(t,this.cid),this.render()})};e=acfe.versionCompare(acf.get("acf_version"),"<","5.9")?acf.duplicate(t):n.acfeNewAcfDuplicate(t),this.$input().trigger("change");t=acf.getFields({type:"tab",parent:e});return t.length&&f.each(t,function(){this.$el.hasClass("acf-hidden")&&this.tab.$el.addClass("acf-hidden")}),e},n.acfeNewAcfDuplicate=function(e){e instanceof jQuery&&(e={target:e}),(e=acf.parseArgs(e,{target:!1,search:"",replace:"",rename:!0,before:function(e){},after:function(e,t){},append:function(e,t){e.after(t)}})).target=e.target||e.$el;var t=e.target;e.search=e.search||t.attr("data-id"),e.replace=e.replace||acf.uniqid(),e.before(t),acf.doAction("before_duplicate",t);var a=t.clone();return e.rename&&acf.rename({target:a,search:e.search,replace:e.replace,replacer:"function"==typeof e.rename?e.rename:null}),a.removeClass("acf-clone"),a.find(".ui-sortable").removeClass("ui-sortable"),e.after(t,a),acf.doAction("after_duplicate",t,a),e.append(t,a),acf.doAction("append",a),a},n.acfeFixInputs=function(e){e.find("input").each(function(){f(this).attr("value",this.value)}),e.find("textarea").each(function(){f(this).html(this.value)}),e.find("input:radio,input:checkbox").each(function(){this.checked?f(this).attr("checked","checked"):f(this).attr("checked",!1)}),e.find("option").each(function(){this.selected?f(this).attr("selected","selected"):f(this).attr("selected",!1)})},n.acfeCleanLayouts=function(e){e.find(".acf-editor-wrap").each(function(){var e=f(this);e.find(".wp-editor-container div").remove(),e.find(".wp-editor-container textarea").css("display","")}),e.find(".acfe-block-editor-wrapper").each(function(){f(this).find(".editor").remove()}),e.find(".acf-date-picker").each(function(){f(this).find("input.input").removeClass("hasDatepicker").removeAttr("id")}),e.find(".acf-time-picker").each(function(){f(this).find("input.input").removeClass("hasDatepicker").removeAttr("id")}),e.find(".acf-date-time-picker").each(function(){f(this).find("input.input").removeClass("hasDatepicker").removeAttr("id")}),e.find(".acfe-field-code-editor").each(function(){f(this).find(".CodeMirror").remove()}),e.find(".acf-color-picker").each(function(){var e=f(this),t=e.find("> input"),a=e.find(".wp-picker-container input.wp-color-picker").clone();t.after(a),e.find(".wp-picker-container").remove()}),e.find(".acf-field-post-object").each(function(){var e=f(this);e.find("> .acf-input span").remove(),e.find("> .acf-input select").removeAttr("tabindex aria-hidden").removeClass()}),e.find(".acf-field-page-link").each(function(){var e=f(this);e.find("> .acf-input span").remove(),e.find("> .acf-input select").removeAttr("tabindex aria-hidden").removeClass()}),e.find(".acf-field-select").each(function(){var e=f(this);e.find("> .acf-input span").remove(),e.find("> .acf-input select").removeAttr("tabindex aria-hidden").removeClass()}),e.find(".acf-field-font-awesome").each(function(){var e=f(this);e.find("> .acf-input span").remove(),e.find("> .acf-input select").removeAttr("tabindex aria-hidden")}),e.find(".acf-tab-wrap").each(function(){var e=f(this),t=e.closest(".acf-fields"),a=[];f.each(e.find("li a"),function(){a.push(f(this))}),t.find("> .acf-field-tab").each(function(){$current_tab=f(this),f.each(a,function(){var e=f(this);e.attr("data-key")===$current_tab.attr("data-key")&&$current_tab.find("> .acf-input").append(e)})}),e.remove()}),e.find(".acf-field-accordion").each(function(){var e=f(this);e.find("> .acf-accordion-title > .acf-accordion-icon").remove(),e.after('<div class="acf-field acf-field-accordion" data-type="accordion"><div class="acf-input"><div class="acf-fields" data-endpoint="1"></div></div></div>')})})}(jQuery),function(t){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{"new_field/type=flexible_content":"newField",show:"onShow",hide:"onHide",append:"onAppend"},newField:function(e){e.addEvents({"click .acfe-fc-placeholder":"onClickCollapse"}),e.addEvents({"click .acfe-flexible-opened-actions > a":"onClickCollapse"}),e.has("acfeFlexibleModalEdition")&&(e.has("acfeFlexiblePlaceholder")||e.has("acfeFlexiblePreview"))&&(e.removeEvents({'click [data-name="collapse-layout"]':"onClickCollapse"}),e.removeEvents({"click .acfe-fc-placeholder":"onClickCollapse"})),e.has("acfeFlexibleLock")&&e.removeEvents({mouseover:"onHover"}),e.$layouts().each(function(){e.trigger("newLayout",[t(this)])})},onShow:function(e,t){"collapse"===t&&e.is(".layout")&&acf.getClosestField(e).trigger("showLayout",[e])},onHide:function(e,t){"collapse"===t&&e.is(".layout")&&!e.is(".acf-clone")&&acf.getClosestField(e).trigger("hideLayout",[e])},onAppend:function(e){var t;e?.[0]?.classList?.contains("layout")&&((t=acf.getClosestField(e)).trigger("newLayout",[e]),t.trigger("appendLayout",[e]))}})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_modal_edit",type:"flexible_content",events:{'click [data-action="acfe-flexible-modal-edit"]':"acfeModalEdit"},acfeModalEdit:function(e,t){var a=t.closest(".layout"),i=a.find("> .acfe-modal.-fields"),n=a.find("> .acf-fc-layout-handle"),t=n.find("> .acf-fc-layout-order").outerHTML(),n=acfe.getTextNode(n.find(".acfe-layout-title"));acfe.getModal(i,{open:!0,title:t+n,onOpen:this.proxy(function(){this.openLayout(a)}),onClose:this.proxy(function(){this.closeLayout(a)})})}}),function(o){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_modal_select",type:"flexible_content",condition:function(){return this.has("acfeFlexibleModal")&&1<this.$clones().length},onClickAdd:function(e,t){var a=this;if(!a.validateAdd())return!1;var i=this.getLayoutsCategories(),n=t.hasClass("acf-icon")?t.closest(".layout"):null;acfe.newModal({title:a.get("acfeFlexibleModalTitle",acf.__("Add Row")),class:"acfe-modal-select-"+a.get("name")+" acfe-modal-select-"+a.get("key"),size:a.get("acfeFlexibleModalSize"),destroy:!0,events:{"click .acfe-flexible-categories a":"onClickCategory","click a[data-layout]":"onClickLayout"},content:function(){return'<div class="acfe-flex-container">'+a.getPopupHTML()+"</div>"},onOpen:function(){i&&this.$content().prepend(i),a.has("acfeFlexibleModalCol")&&this.$(".acfe-modal-content .acfe-flex-container").addClass("acfe-col-"+a.get("acfeFlexibleModalCol")),a.has("acfeFlexibleThumbnails")&&this.$(".acfe-modal-content .acfe-flex-container").addClass("acfe-flex-thumbnails");var e=acfe.versionCompare(acf.get("wp_version"),">=","5.5")?"dashicons-info-outline":"dashicons-info";this.$("li a span.badge").addClass("acf-js-tooltip dashicons "+e),this.$("li:first-of-type a").blur()},onClickCategory:function(e,t){e.preventDefault();var t=t,i=t.data("acfe-flexible-category");t.closest(".acfe-flexible-categories").find("a").removeClass("nav-tab-active"),t.addClass("nav-tab-active"),this.$("a[data-layout] span[data-acfe-flexible-category]").closest("li").show(),"acfe-all"!==i&&this.$("a[data-layout] span[data-acfe-flexible-category]").each(function(){var e,t=o(this),a=t.data("acfe-flexible-category");t.closest("li").hide();for(e of a)if(acfe.slugify(i)===acfe.slugify(e)){t.closest("li").show();break}})},onClickLayout:function(e,t){e.preventDefault(),this.close(),a.add({layout:t.data("layout"),before:n})}})},getLayoutsCategories:function(){var e=o(this.getPopupHTML()),t="",i=[];return e.find("li a span[data-acfe-flexible-category]").exists()&&(e.find("li a span[data-acfe-flexible-category]").each(function(){o(this).data("acfe-flexible-category").map(function(e){var t=acfe.slugify(e),a=i.filter(function(e){return e.slug===t});acfe.isEmpty(a)&&i.push({slug:t,label:e})})}),i.length&&(t+='<h2 class="acfe-flexible-categories nav-tab-wrapper">',t+='<a href="#" data-acfe-flexible-category="acfe-all" class="nav-tab nav-tab-active"><span class="dashicons dashicons-menu"></span></a>',i.sort(function(e,t){return e.slug>t.slug?1:-1}),i.map(function(e){t+='<a href="#" data-acfe-flexible-category="'+e.label+'" class="nav-tab">'+e.label+"</a>"}),t+="</h2>")),t}})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_modal_settings",type:"flexible_content",events:{"click [data-acfe-flexible-settings]":"acfeLayoutSettings"},acfeLayoutSettings:function(e,t){var a=t.closest(".layout"),i=a.find("> .acfe-modal.-settings"),n=a.find("> .acf-fc-layout-handle"),t=n.find("> .acf-fc-layout-order").outerHTML(),n=acfe.getTextNode(n.find(".acfe-layout-title"));acfe.getModal(i,{open:!0,title:t+n,onClose:this.proxy(function(){this.has("acfeFlexiblePreview")&&this.closeLayout(a)})})}}),function(n){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_one_click",type:"flexible_content",condition:function(){return 1===this.$clones().length},onClickAdd:function(e,t){if(!this.validateAdd())return!1;var a=this.$clones(),i=n(a[0]).data("layout"),a=null;t.hasClass("acf-icon")&&(a=t.closest(".layout")),this.add({layout:i,before:a});a=n(".acf-fc-popup");a.length&&a.hide()}})}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acfe.FieldExtender({id:"fc_placeholder",type:"flexible_content",condition:function(){return this.has("acfeFlexiblePlaceholder")},initialize:function(){this.getParent(e).initialize.apply(this,arguments),this.addEvents({showLayout:"acfePlaceholderShowLayout",hideLayout:"acfePlaceholderHideLayout",newLayout:"acfePlaceholderNewLayout"})},acfePlaceholderShowLayout:function(e,t,a){this.has("acfeFlexibleModalEdition")||acf.hide(a.find("> .acfe-fc-placeholder"))},acfePlaceholderHideLayout:function(e,t,a){acf.show(a.find("> .acfe-fc-placeholder"))},acfePlaceholderNewLayout:function(e,t,a){this.isLayoutClosed(a)&&acf.show(a.find("> .acfe-fc-placeholder"))}}))}(jQuery),function(l){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_preview",type:"flexible_content",condition:function(){return this.has("acfeFlexiblePreview")},events:{hideLayout:"acfePreviewHideLayout",appendLayout:"acfePreviewAppendLayout"},acfePreviewHideLayout:function(e,t,a){this.acfeLayoutPreview(a)},acfePreviewAppendLayout:function(e,t,a){this.acfeLayoutPreview(a)},acfeLayoutPreview:function(t){var a,i,n,o,e,c,r,f;this.isLayoutClosed(t)&&!t.find("> .acfe-fc-placeholder").hasClass("-loading")&&(a=this.get("key"),i=this.get("name"),n=this.$el,o=t.data("layout"),e=t.index(),(c=t.find("> .acfe-fc-placeholder")).addClass("acfe-fc-preview -loading").find("> .acfe-flexible-placeholder").prepend('<span class="spinner"></span>'),c.find("> .acfe-fc-overlay").addClass("-hover"),r=t.children("input").attr("name").replace("[acf_fc_layout]",""),f={action:"acfe/flexible/layout_preview",field_key:a,i:e,layout:o,value:acf.serialize(t,r)},f=acf.applyFilters("acfe/fields/flexible_content/preview_data",f,n,t),f=acf.applyFilters("acfe/fields/flexible_content/preview_data/name="+i,f,n,t),f=acf.applyFilters("acfe/fields/flexible_content/preview_data/key="+a,f,n,t),f=acf.applyFilters("acfe/fields/flexible_content/preview_data/layout="+o,f,n,t),f=acf.applyFilters("acfe/fields/flexible_content/preview_data/name="+i+"&layout="+o,f,n,t),f=acf.applyFilters("acfe/fields/flexible_content/preview_data/key="+a+"&layout="+o,f,n,t),acf.doAction("acfe/fields/flexible_content/before_preview",n,t,f),acf.doAction("acfe/fields/flexible_content/before_preview/name="+i,n,t,f),acf.doAction("acfe/fields/flexible_content/before_preview/key="+a,n,t,f),acf.doAction("acfe/fields/flexible_content/before_preview/layout="+o,n,t,f),acf.doAction("acfe/fields/flexible_content/before_preview/name="+i+"&layout="+o,n,t,f),acf.doAction("acfe/fields/flexible_content/before_preview/key="+a+"&layout="+o,n,t,f),l.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(f),dataType:"html",type:"post",context:this,success:function(e){e?c.find("> .acfe-flexible-placeholder").html(e):c.removeClass("acfe-fc-preview"),acf.doAction("acfe/fields/flexible_content/preview",e,n,t,f),acf.doAction("acfe/fields/flexible_content/preview/name="+i,e,n,t,f),acf.doAction("acfe/fields/flexible_content/preview/key="+a,e,n,t,f),acf.doAction("acfe/fields/flexible_content/preview/layout="+o,e,n,t,f),acf.doAction("acfe/fields/flexible_content/preview/name="+i+"&layout="+o,e,n,t,f),acf.doAction("acfe/fields/flexible_content/preview/key="+a+"&layout="+o,e,n,t,f)},complete:function(){c.find("> .acfe-fc-overlay").removeClass("-hover"),c.removeClass("-loading").find("> .acfe-flexible-placeholder > .spinner").remove()}}))}})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_state",type:"flexible_content",condition:function(){return this.has("acfeFlexibleOpen")},addCollapsed:function(){}}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_title_ajax",type:"flexible_content",condition:function(){return this.has("acfeFlexibleRemoveAjaxTitle")},renderLayout:function(){}}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_title_inline",type:"flexible_content",condition:function(){return this.has("acfeFlexibleTitleEdition")},events:{"click .acf-fc-layout-handle":"acfeEditLayoutTitleToggleHandle","click .acfe-layout-title-text":"acfeEditLayoutTitle","blur input.acfe-flexible-control-title":"acfeEditLayoutTitleToggle","click input.acfe-flexible-control-title":"acfeEditLayoutTitlePropagation","input [data-acfe-flexible-control-title-input]":"acfeEditLayoutTitleInput","keypress [data-acfe-flexible-control-title-input]":"acfeEditLayoutTitleInputEnter"},acfeEditLayoutTitleToggleHandle:function(e,t){t=t.closest(".layout");t.hasClass("acfe-flexible-title-edition")&&t.find("> .acf-fc-layout-handle > .acfe-layout-title > input.acfe-flexible-control-title").trigger("blur")},acfeEditLayoutTitle:function(e,t){e.stopPropagation(),this.acfeEditLayoutTitleToggle(e,t)},acfeEditLayoutTitleToggle:function(e,t){var a,i=t.closest(".layout"),n=i.find("> .acf-fc-layout-handle"),t=n.find(".acfe-layout-title");i.hasClass("acfe-flexible-title-edition")?(""===(a=t.find("> input[data-acfe-flexible-control-title-input]")).val()&&a.val(a.attr("placeholder")).trigger("input"),i.removeClass("acfe-flexible-title-edition"),a.insertAfter(n)):(a=(a=i.find("> input[data-acfe-flexible-control-title-input]")).appendTo(t),i.addClass("acfe-flexible-title-edition"),a.focus().attr("size",a.val().length))},acfeEditLayoutTitlePropagation:function(e,t){e.stopPropagation()},acfeEditLayoutTitleInput:function(e,t){var a=t.closest(".layout").find("> .acf-fc-layout-handle .acfe-layout-title .acfe-layout-title-text"),i=t.val();t.attr("size",i.length),a.html(i)},acfeEditLayoutTitleInputEnter:function(e,t){13===e.keyCode&&(e.preventDefault(),t.blur())}}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&acfe.FieldExtender({id:"fc_toggle",type:"flexible_content",condition:function(){return this.has("acfeFlexibleToggle")},events:{"click [data-acfe-flexible-control-toggle]":"acfeLayoutToggle"},acfeLayoutToggle:function(e,t){var a=t.closest(".layout"),t=a.find("> .acfe-flexible-layout-toggle");t.length&&("1"===t.val()?(a.removeClass("acfe-flexible-layout-hidden"),t.val("")):(a.addClass("acfe-flexible-layout-hidden"),t.val("1")))}}),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({wait:!1,type:"group",events:{duplicateField:"onDuplicate"},initialize:function(){this.has("acfeGroupModal")&&(this.$inputWrap().find("> .acf-fields, > .acf-table").wrapAll('<div class="acfe-modal"><div class="acfe-modal-wrapper"><div class="acfe-modal-content"></div></div></div>'),this.$inputWrap().append('<a href="#" class="acf-button button" data-modal>'+this.get("acfeGroupModalButton")+"</a>"),this.initializeModal())},initializeModal:function(){var e,t=this.$labelWrap().find("label").text().trim();this.$el.is("td")&&(t=this.get("acfeGroupModalButton"),(e=this.$el.closest("table").find(' > thead th[data-key="'+this.get("key")+'"]')).length&&(t=acfe.getTextNode(e))),t.length||(t=this.get("acfeGroupModalButton")),this.getModal({title:t,size:this.has("acfeGroupModalSize")?this.get("acfeGroupModalSize"):"large",footer:!!this.has("acfeGroupModalClose")&&acf.__("Close"),class:"acfe-modal-edit-"+this.get("name")+" acfe-modal-edit-"+this.get("key")})},onDuplicate:function(e,t,a){a.find(".acf-input:first > a[data-modal]").remove()}}),acf.registerFieldType(e))}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&void 0!==acf.models.IconPickerField&&(acf.models.IconPickerField.prototype.get=function(e){return this.data[e]}),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.models.RadioField,acf.models.RadioField=e.extend({setValue:function(e){if(!e)return this.clearValue();e=this.$(':radio[value="'+e+'"]');e.length&&!e.is(":checked")&&(e.prop("checked",!0).trigger("change"),this.onClick(null,e))},clearValue:function(){this.get("allow_null")?this.$input().length&&this.onClick(null,this.$input()):this.val(this.$(":radio").first().val())}}))}(jQuery),function(i){var e,t;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({type:"acfe_recaptcha",wait:"load",widgetID:0,events:{invalidField:"onInvalidField"},$control:function(){return this.$(".acf-input-wrap")},$input:function(){return this.$('input[type="hidden"]')},initialize:function(){t.load(this,this.render)},render:function(){"v2"===this.get("version")?this.renderV2():"v3"===this.get("version")&&this.renderV3()},renderV2:function(){this.widgetID=grecaptcha.render(this.$control().find("> div")[0],{sitekey:this.get("siteKey"),theme:this.get("theme"),size:this.get("size"),callback:this.proxy(function(e){acf.val(this.$input(),e,!0),this.removeError()}),"error-callback":this.proxy(function(){this.$el.addClass("acf-error"),this.showNotice({text:"An error has occured",type:"error",dismiss:!1})}),"expired-callback":this.proxy(function(){this.showError("reCaptcha has expired")})})},renderV3:function(){var t=this.$input(),e=this.get("siteKey"),a=function(){grecaptcha.execute(e,{action:"homepage"}).then(function(e){acf.val(t,e,!0)}),setTimeout(a,8e4)};a()},reset:function(){"v2"===this.get("version")?(grecaptcha.reset(this.widgetID),acf.val(this.$input(),"",!0)):"v3"===this.get("version")&&this.renderV3()},onInvalidField:function(e,t){this.reset()}}),acf.registerFieldType(e),t=new acf.Model({busy:!1,load:function(e,t){t=e.proxy(t);var a="https://www.google.com/recaptcha/api.js?render="+e.get("siteKey"),a="v2"===e.get("version")?"https://www.google.com/recaptcha/api.js?render=explicit":a;if("undefined"!=typeof grecaptcha||acf.isset(window,"grecaptcha"))return t();acf.addAction("acfe/recpatcha_loaded",t),this.busy||(this.busy=!0,i.ajax({url:a,dataType:"script",cache:!0,context:this,success:function(){grecaptcha.ready(this.proxy(function(){acf.doAction("acfe/recpatcha_loaded"),this.busy=!1}))}}))}}))}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.getFieldType("repeater").prototype.acfeOnHover=function(){this.off("mouseover")},acf.addAction("new_field/type=repeater",function(e){e.has("acfeRepeaterLock")&&(e.removeEvents({mouseover:"onHover"}),e.addEvents({mouseover:"acfeOnHover"})),e.has("acfeRepeaterRemoveActions")&&(e.$actions().remove(),e.$el.find("thead:first > tr > th.acf-row-handle:last").remove(),e.$rows().find("> .acf-row-handle:last").remove(),e.$control().find("> .acfe-repeater-stylised-button").remove()),e.has("acfeRepeaterStylisedButton")&&(e.$button().removeClass("button-primary"),e.$actions().wrap('<div class="acfe-repeater-stylised-button" />'))})),function(r){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{"new_field/type=select":"newSelect",select2_init:"init"},filters:{select2_args:"args"},newSelect:function(e){e.inherit(e.$input()),!e.get("ui")&&e.get("allow_null")&&e.$input().find("option").each(function(e,t){!t.value&&t.text.startsWith("- ")&&t.text.endsWith(" -")&&(t.text=t.text.substring(2),t.text=t.text.substring(0,t.text.length-2))}),(e.has("acfePrepend")||e.has("acfeAppend"))&&(e.$input().parent(".acf-input-wrap").length||(e.$input().wrapAll('<div class="acf-input-wrap"></div>'),e.get("ui")&&e.$(".acf-input-wrap:first").append(e.$(".select2")),e.has("acfePrepend")&&(e.$(".acf-input-wrap:first").before('<div class="acf-input-prepend">'+e.get("acfePrepend")+"</div>"),e.$input().addClass("acf-is-prepended")),e.has("acfeAppend")&&(e.$(".acf-input-wrap:first").before('<div class="acf-input-append">'+e.get("acfeAppend")+"</div>"),e.$input().addClass("acf-is-appended"))))},init:function(e,t,a,i,n){e.on("select2:clear",function(e){r(this).on("select2:opening.cancelOpen",function(e){e.preventDefault(),r(this).off("select2:opening.cancelOpen")})}),i&&(e.data("select2")&&e.data("select2").$dropdown.addClass("select2-dropdown-acf").addClass("select2-dropdown-acf-field-"+i.get("name")).addClass("select2-dropdown-acf-field-"+i.get("key")),!i.get("multiple")&&i.get("acfeSearchPlaceholder")&&e.on("select2:open",function(e){r(".select2-search.select2-search--dropdown > .select2-search__field").attr("placeholder",i.get("acfeSearchPlaceholder"))}))},args:function(e,t,a,i,n){return i&&i.get("acfeAllowCustom")&&(e.tags=!0,e.createTag=function(e){var t,a=r.trim(e.term);if(""===a)return null;var i=acf.isget(this,"_request","responseJSON","results");if(i){e:for(var n of i)if(n.children)for(var o of n.children)if("string"==typeof o.id&&o.id.toLowerCase()===a.toLowerCase()){t=!0;break e}}else for(var c of this.$element.find("option"))if(c.value.toLowerCase()===a.toLowerCase()){t=!0;break}return t?null:{id:a,text:a}},e.insertTag=function(e,t){var a,i;for(i of e)if(r.trim(t.text).toUpperCase()===r.trim(i.text).toUpperCase()){a=!0;break}a||e.unshift(t)}),e}})}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({type:"acfe_slug",events:{"input input":"onInput","focusout input":"onFocusOut"},onInput:function(e,t){t.val(t.val().toLowerCase().replace(/\s+/g,"-").replace(/[^\w\-]+/g,"").replace(/\-\-+/g,"-").replace(/\_\_+/g,"_").replace(/^-+/,""))},onFocusOut:function(e,t){t.val(t.val().toLowerCase().replace(/-+$/,"").replace(/_+$/,""))}}),acf.registerFieldType(e))}(jQuery),function(a){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&new acf.Model({actions:{"prepare_field/type=tab":"prepareField"},prepareField:function(e){var t,i,n;e.has("noPreference")&&(t=e.findTabs(),t=acf.getInstances(t),i=e.get("key"),!t.length||(n=acf.getPreference("this.tabs"))&&(a.each(t,function(e,t){var a=t.get("index");t.data.key===i&&(n[a]=0)}),acf.setPreference("this.tabs",n)))}})}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.Field.extend({type:"textarea",events:{"keydown textarea":"onInput"},onInput:function(e,t){var a,i;this.has("acfeTextareaCode")&&9===e.keyCode&&(e.preventDefault(),a=this.$el.find("textarea")[0],i=a.selectionStart,this.$el.find("textarea").val(function(e,t){return t.substring(0,i)+"    "+t.substring(a.selectionEnd)}),a.selectionEnd=i+4)}}),acf.registerFieldType(e))}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.models.TrueFalseField,acf.models.TrueFalseField=e.extend({setValue:function(e){if(!e)return this.clearValue();this.switchOn(),this.trigger("change")},clearValue:function(){this.switchOff(),this.trigger("change")}}))}(jQuery),function(){var e;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(e=acf.models.WysiwygField,acf.models.WysiwygField=e.extend({initialize:function(){this.has("id")||this.$control().hasClass("delay")||this.initializeEditor()}}),new acf.Model({actions:{"append_field/type=wysiwyg":"appendField","show_field/type=wysiwyg":"showField","ready_field/type=wysiwyg":"showField"},appendField:function(e){this.setTimeout(function(){this.showField(e)},1)},showField:function(e){e.has("acfeWysiwygAutoInit")&&e.$el.is(":visible")&&!e.has("id")&&!acfe.isFilterEnabled("acfeFlexibleOpen")&&this.initializeEditor(e)},initializeEditor:function(e){e.$control().hasClass("delay")&&(e.$control().removeClass("delay"),e.$control().find(".acf-editor-toolbar").remove(),e.initializeEditor())}}))}(jQuery);