!function(a){var e,i,s;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.enhancedEditUI=function(t){return new e(t)},e=acf.Model.extend({data:{screen:!1,submit:"> p.submit",pageTitle:!1},title:!1,$form:!1,$main:!1,$sidebar:!1,setup:function(t){a.extend(this.data,t),this.$el=a(".wrap"),this.$el.attr("id","poststuff"),this.$("> form").wrapInner('<div class="acf-columns-2"><div class="acf-column-1"></div></div>'),a("#side-sortables").appendTo(".acf-columns-2").wrapAll('<div class="acf-column-2"></div>'),this.title=this.$("> h1").text(),this.$form=this.$("> form"),this.$main=this.$(".acf-column-1"),this.$sidebar=this.$(".acf-column-2"),this.get("pageTitle")&&this.$main.find("> table:first").before("<h2>"+this.title+"</h2>"),a("#submitdiv > .postbox-header > h2.hndle").text(this.title),this.$main.find(this.get("submit")).contents().appendTo("#publishing-action"),this.$main.find(this.get("submit")).remove(),a("#submitdiv #publishing-action .button").addClass("button-large").after('<span class="spinner"></span>')},events:{"submit form":"onSubmit"},initialize:function(){this.addActions({"acfe/ui/user-edit":"userEdit","acfe/ui/user-new":"userNew","acfe/ui/term-edit":"termEdit","acfe/ui/settings":"settings"}),acf.doAction("acfe/ui/"+this.get("screen"),this)},onSubmit:function(t,e){acf.lockForm(e)},userEdit:function(){var t=this.$main;t.prepend(a("#acf_after_title-sortables"));var e=this.$("> form .yoast.yoast-settings");e.length&&(e.addClass("postbox"),e.find("> h2").wrapAll('<div class="postbox-header"></div>'),e.find("> .postbox-header ~ *").wrapAll('<div class="acf-fields -left"></div>'),e.find(".acf-fields > label:nth-of-type(1), .acf-fields > input:nth-of-type(1)").wrapAll('<div class="acf-field"></div>'),e.find(".acf-fields > label:nth-of-type(1), .acf-fields > label:nth-of-type(1) ~ *").wrapAll('<div class="acf-field"></div>'),e.find(".acf-fields > br").remove(),e.find(".acf-field").each(function(){var t=a(this);t.find("label:nth-of-type(1)").wrapAll('<div class="acf-label"></div>'),t.find(".acf-label ~ *").wrapAll('<div class="acf-input"></div>')}));var i=this.$("#ure_select_other_roles");i.length&&i.closest("table").find("tr:eq(1) > td > br").remove();e=a("#application-passwords-section");e.length&&(i=e.find("> h2").text(),e.addClass("postbox"),e.wrapInner('<div class="acf-fields -left"><div class="acf-field"><div class="acf-input"></div></div></div>'),e.find(".acf-input > h2").insertBefore(e.find(".acf-fields")).wrapAll('<div class="postbox-header"></div>'),e.find(".acf-input").before('<div class="acf-label"><label>'+i+"</label></div>"),e.find(".acf-input > p:first").css("margin-top",15).insertBefore(e.find("p.submit")),e.find(".acf-input > .create-application-password > .form-field").removeClass("form-field"),e.find(".acf-input > .create-application-password > div > label").remove());e=a("input#nickname");e.length&&(e.wrapAll('<div id="titlediv"><div id="titlewrap"></div></div>'),a("#titlediv").append(a("#edit-slug-box")).prependTo(t),t.find("tr.user-nickname-wrap").remove()),t.find("> h2:eq(1), > h2:eq(1) + table").insertBefore(t.find("> h2:first"))},userNew:function(){var t=this.$main;this.$("> p:first").insertAfter(t.find(">h2:first")),t.prepend(a("#acf_after_title-sortables"))},termEdit:function(){var t=this.$main,e=a("input#name");e.length&&(e.wrapAll('<div id="titlediv"><div id="titlewrap"></div></div>'),a("#titlediv").append(a(".permalink")).prependTo(t),t.find("tr.term-name-wrap").remove());t=a("#icl_tax_menu");t.length&&(i=t.find("h3.hndle").text(),t.find(".inside").addClass("icl-tax-postbox-content").attr("style","").insertAfter("#submitdiv"),this.$sidebar.find(".icl-tax-postbox-content").wrapAll('<div id="icl-tax-postbox" class="postbox"></div>').parent().prepend('<div class="postbox-header"><h2 class="hdnle">'+i+"</h2></div>"));var i,t=a(".wpseo-taxonomy-metabox-postbox");t.length&&(i=t.find("> h2").text(),t.find("> .inside").removeClass("inside").wrapAll('<div class="acf-fields -left"><div class="acf-field"><div class="acf-input"></div></div></div>'),a('<div class="acf-label"><label>'+i+"</label></div>").insertBefore(t.find(".acf-input"))),a("#submitdiv #publishing-action").addClass("edit-tag-actions")},settings:function(){acf.get("locale")||acf.set("locale","en_US"),a("#acf-form-data").prependTo(this.$form),a("#acf_after_title-sortables").prependTo(this.$main),a("#normal-sortables").appendTo(this.$main);var t=this.$("#ping_sites");t.length&&(t.wrap('<table class="form-table"><tbody><td class="td-full"></td></tbody></table>'),t.css("width","100%"));t=this.$(".permalink-structure");t.length&&t.prev().prev("p").insertBefore(t)}}),acfe.enhancedListUI=function(t){return new i(t)},i=acf.Model.extend({data:{taxonomy:!1},setup:function(t){a.extend(this.data,t)},initialize:function(){a(".wrap .wp-heading-inline").after(a("#tmpl-button-add-term").html()),a("#ajax-response").after(a("#col-container #col-left").addClass("acfe-bt")),a(".acfe-bt").hide(),a(".acfe-bt .form-wrap").append('<div id="poststuff"></div>');var t=a(".acfe-bt .form-wrap form");a(".acfe-bt #poststuff").append(t),t.wrapInner('<div class="postbox" id="acfe-bt-form"><div class="inside"></div></div>');var e=a(".acfe-bt .form-wrap > h2");a(".acfe-bt .postbox").prepend('<h2 class="hndle">'+e.text()+"</h2>"),e.remove(),a(".acfe-bt .inside .form-field").addClass("acf-field"),a(".acfe-bt .inside .submit").addClass("form-field"),a(".acfe-bt .inside .form-field").each(function(){var t,e=a(this);e.is("#term-translations")||(e.append('<div class="acf-input"></div>'),e.find(".acf-input").append(e.find('> :not("label")')),(t=e.find("> label")).length?t.wrap('<div class="acf-label"></div>'):e.addClass("acfe-bt-no-label"))}),a("#acf-term-fields").contents().unwrap(),a(".acfe-bt-admin-button-add").click(function(t){t.preventDefault();t=a(".acfe-bt");t.is(":visible")?t.hide():t.show()}),"undefined"!=typeof acf&&acf.postbox.render({id:"acfe-bt-form",label:"left"}),a("#acfe-bt-form .acf-tab-wrap.-left").removeClass("-left").addClass("-top");t=a("#icl_tax_menu");t.length&&((e=t.find(".postbox").removeClass("postbox")).find(".inside").removeClass("inside").css("padding",0),e.insertBefore(".acfe-bt .inside .submit"),t=e.find("h3.hndle").text(),e.find(".hndle").remove(),e.wrapAll('<div class="form-field acf-field"><div class="acf-input"></div></div>').parent().parent().prepend('<div class="acf-label"><label>'+t+"</label></div>")),this.addAction("ready","ready")},ready:function(){a(".global-new-entity-button").click(function(t){t.preventDefault();t=a(".acfe-bt");t.is(":visible")?t.hide():t.show()})}}),acfe.enhancedAttachmentUI=function(t){return new s(t)},s=acf.Model.extend({data:{title:"Edit Media"},setup:function(t){a.extend(this.data,t),this.$el=a(".wrap")},initialize:function(){this.$(".wp_attachment_details").addClass("postbox acf-postbox"),this.$(".wp_attachment_details").wrapInner('<div class="inside acf-fields -left" />'),this.$(".wp_attachment_details").prepend('<div class="postbox-header"><h2 class="hndle">'+this.get("title")+"</h2></div>"),this.$(".wp_attachment_details").find(".attachment-alt-text-description ~ p").wrapAll('<div class="acf-field acfe-caption" />'),this.$(".wp_attachment_details").find(".attachment-alt-text, .attachment-alt-text-description").wrapAll('<div class="acf-field acfe-alt-text" />'),this.$(".wp_attachment_details").find("label.attachment-content-description, #wp-attachment_content-wrap").wrapAll('<div class="acf-field acfe-description" />'),this.$(".acf-field.acfe-alt-text").find("label").wrapAll('<div class="acf-label" />'),this.$(".acf-field.acfe-alt-text").find(".acf-label").prependTo(".acf-field.acfe-alt-text"),this.$(".acf-field.acfe-alt-text").find("input, textarea, p.attachment-alt-text-description").wrapAll('<div class="acf-input" />'),this.$(".acf-field.acfe-alt-text").find(".acf-input").appendTo(".acf-field.acfe-alt-text"),this.$(".acf-field.acfe-alt-text").find(".attachment-alt-text").remove(),this.$(".acf-field.acfe-caption").find("label").wrapAll('<div class="acf-label" />'),this.$(".acf-field.acfe-caption").find(".acf-label").prependTo(".acf-field.acfe-caption"),this.$(".acf-field.acfe-caption").find("textarea").wrapAll('<div class="acf-input" />'),this.$(".acf-field.acfe-caption").find(".acf-input").appendTo(".acf-field.acfe-caption"),this.$(".acf-field.acfe-caption").find("> p").remove(),this.$(".acf-field.acfe-description").find("label").wrapAll('<div class="acf-label" />'),this.$(".acf-field.acfe-description").find("#wp-attachment_content-wrap").wrapAll('<div class="acf-input" />')}}))}(jQuery);