jQuery,"undefined"!=typeof acf&&(window.acfe={}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.getArray=function(e){return[].concat((e=0===e?"0":e)||[])},acfe.getObject=function(e){return acf.parseArgs(e)},acfe.extractVar=function(e,t,n=null){var a=e[t];return delete e[t],acfe.isUndefined(a)?n:a},acfe.extractVars=function(e,...t){var n,a={};for(n of t){var i=acfe.extractVar(e,n,"!!undefined!!");"!!undefined!!"!==i&&(a[n]=i)}return a},acfe.getEntries=function(e){return Object.entries(e)},acfe.inArray=function(e,t,n=!1){n||(e=acfe.getString(e),t=t.map(function(e){return acfe.getString(e)}));for(var a=0;a<t.length;a++)if(t[a]===e)return!0;return!1},acfe.arrayGet=function(e,t,n=null){for(var a,i=(t=acfe.normalizePath(t)).length,o=e,c=0;c<i;c++){if(a=t[c],acfe.isUndefined(o[a]))return n;o=o[a]}return o},acfe.arrayHas=function(e,t){return"!!undefined!!"!==acfe.arrayGet(e,t,"!!undefined!!")},acfe.arraySet=function(e,t,n){for(var a,i=(t=acfe.normalizePath(t)).length,o=e,c=0;c<i;c++)a=t[c],c===i-1?o[a]=n:o=(o[a]&&acfe.isObject(o[a])||(o[a]={}),o[a]);return e=acfe.arrayReindex(e)},acfe.arrayDelete=function(e,t){for(var n,a=(t=acfe.normalizePath(t)).length,i=e,o=0;o<a;o++)n=t[o],o===a-1?delete i[n]:i=i[n]||{};return e=acfe.arrayReindex(e)},acfe.arrayPluck=function(e,t,n=!1){var a,i,o={},c=[];n&&((a=t.split(".")).pop(),a.push(n),a=a.join("."));for(i of e){var r,f=acfe.arrayGet(i,t,"!!undefined!!");"!!undefined!!"!==f&&(c.push(f),!n||"!!undefined!!"!==(r=acfe.arrayGet(i,a,"!!undefined!!"))&&(o[r]=f))}return c=n?o:c},acfe.normalizePath=function(e){return e=(e=acfe.getString(e)).split(".")},acfe.arrayReindex=function(e){return e=acfe.isArray(e)?e.filter(function(e){return!acfe.isUndefined(e)}):e}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.data.acfe={},acfe.get=function(e=null,t=null){return null===e?acf.data.acfe:acfe.arrayGet(acf.data.acfe,e,t)},acfe.has=function(e){return null!==acfe.arrayGet(acf.data.acfe,e)},acfe.set=function(e,t){return acfe.arraySet(acf.data.acfe,e,t),this}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.deprecatedFunction=function(e,t,n=""){n=n?"! Use "+n+" instead.":" with no alternative available.",console.log("ACF Extended: "+e+" is deprecated since version "+t+n)},acfe.deprecatedHook=function(e,t,n=""){n=n?"! Use "+n+" instead.":" with no alternative available.",console.log("ACF Extended: Hook "+e+" is deprecated since version "+t+n)},acfe.applyFiltersDeprecated=function(e,t,n,a=""){return acfe.hasFilter(e)?(acfe.deprecatedHook(e,n,a),acf.applyFilters(e,...t)):t[0]},acfe.doActionDeprecated=function(e,t,n,a=""){if(acfe.hasAction(e))return acfe.deprecatedHook(e,n,a),acf.doAction(e,...t)}),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.isFieldKey=function(e){return acfe.isString(e)&&e.startsWith("field_")},acfe.isFieldName=function(e){return acfe.isString(e)&&!acfe.isFieldKey(e)},acfe.isGroupKey=function(e){return acfe.isString(e)&&e.startsWith("group_")},acfe.getField=function(e){return acfe.isFieldName(e)?acf.getFields({name:e,limit:1,suppressFilters:!0}).shift():acfe.isObject(e)?(e=acf.parseArgs(e,{limit:1,suppressFilters:!0}),acf.getFields(e).shift()):acf.getField(e)},acf.addFilter("find_fields_selector",function(e,t){if(t.types&&t.types.length&&acfe.isArray(t.types)){var n,a=[];for(n of t.types)a.push(e+'[data-type="'+n+'"]');e=a.join(",")}return e})),function(){var n;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(n=[],acfe.disableFilters=function(){n=[]},acfe.getFilters=function(){return n},acfe.isFilterEnabled=function(e){return-1<n.indexOf(e)},acfe.enableFilter=function(e){-1===n.indexOf(e)&&n.push(e)},acfe.disableFilter=function(e){for(var t=n.length;t--;)n[t]===e&&n.splice(t,1)})}(jQuery),function(n){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.findSubmitWrap=function(e){var t;return(t=(e=e||n("form")).find("#submitdiv")).length||(t=e.find("#submitpost")).length||(t=e.find("p.submit").last()).length||(t=e.find(".acf-form-submit")).length?t:e},acfe.findSubmit=function(e){return e=e||n("form"),this.findSubmitWrap(e).find('.button, [type="submit"]')},acfe.findSpinner=function(e){return e=e||n("form"),this.findSubmitWrap(e).find(".spinner, .acf-spinner")})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.hasFilter=function(e,t=!1){return acfe.hasHook("filters",e,t)},acfe.hasAction=function(e,t=!1){return acfe.hasHook("actions",e,t)},acfe.hasHook=function(e,t,n=!1){var a,i=acf.hooks.storage();if(!acfe.arrayGet(i[e],t))return!1;if(!n)return!0;for(a of i[e][t])if(acfe.arrayGet(a,"callback.name")===n)return!0;return!1}),function(a){var t;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(["prepare","ready","load","append","remove","unmount","remount","sortstart","sortstop","show","hide","unload"].map(function(e){acf.addAction(e,function(e){acfe.getModals({parent:e})})}),acfe.getModals=function(e){acfe.isJquery(e)||(e=acfe.findModals(e));var t=[];return e.each(function(){var e=acfe.getModal(a(this));t.push(e)}),t},acfe.getModal=function(e,t){return!(!(e=!acfe.isJquery(e)?acfe.findModal(e):e).length||!e.hasClass("acfe-modal"))&&acfe.newModal(e,t)},acfe.findModals=function(e){var t=".acfe-modal",n=!1;return(e=acf.parseArgs(e,{modal:"",is:"",parent:!1,sibling:!1,limit:!1,open:!1,close:!1,suppressFilters:!1})).modal&&(t+='[data-modal="'+e.modal+'"]'),e.is&&(t+=e.is),e.open&&(t+=":visible"),e.close&&(t+=":hidden"),n=e.parent?e.parent.find(t):e.sibling?e.sibling.siblings(t):a(t),e.suppressFilters||(n=n.not(".acf-clone .acfe-modal")),n=e.limit?n.slice(0,e.limit):n},acfe.findModal=function(e,t){return acfe.findModals({modal:e,limit:1,parent:t})},acfe.findClosestModal=function(e){for(var t of e.parents()){t=a(t).find(">.acfe-modal");if(t.length)return t}return!1},acfe.getClosestModal=function(e,t){e=acfe.findClosestModal(e);return!!e.length&&this.getModal(e,t)},acfe.newModal=function(e,t){if(acfe.isJquery(e)){var n=e.data("acf");return n?n.update(t):(n=new acfe.Modal(e,t),acf.doAction("acfe/new_modal",n),acfe.doActionDeprecated("new_modal",[n],"0.9.0.5","acfe/new_modal"),n)}return t=acf.parseArgs(e,{open:!0}),n=new acfe.Modal(t),acf.doAction("acfe/new_modal",n),acfe.doActionDeprecated("new_modal",[n],"0.9.0.5","acfe/new_modal"),n},acfe.Modal=acf.Model.extend({data:{modal:"",title:"",content:"",footer:"",class:"",size:"medium",open:!1,destroy:!1,events:{},onOpen:function(){},onClose:function(){}},eventScope:".acfe-modal",events:{changed:"onChanged","click .close":"onClickClose"},onChanged:function(e,t,n,a,i){acfe.inArray(n,["title","content","footer","class","size"])?this.render():"open"===n&&this.checkOpen()},setup:function(e,t){acfe.isJquery(e)||(t=e,e=a('<div class="acfe-modal"></div>').appendTo("body")),this.$el=e,t=acfe.getObject(t),this.inherit(e),this.inherit(t),this.renderTemplates(),this.render()},initialize:function(){this.addDataEvents(),this.checkOpen()},addDataEvents:function(){if(this.get("events")){var e,t=this.get("events"),n=/^(\S+)\s*(.*)$/;for(e in t){var a,i=e.match(n),o=(a=i[2]?(c=i[1],i[2]):(c=i[1],""),t[e]),c=c+"."+this.cid;o=this.proxyEvent(this.get(o)),(i=this.$el).on.apply(i,a?[c,a,o]:[c,o])}}},update:function(e){e=acfe.getObject(e);var t,n,a=acfe.extractVar(e,"open");for([t,n]of Object.entries(e))this.set(t,n);return a&&this.set("open",a),this},on:function(){return acf.Model.prototype.on.apply(this,arguments),this},checkOpen:function(){this.get("open")&&this.open()},$wrapper:function(){return this.$("> .acfe-modal-wrapper")},$title:function(e){return acfe.isUndefined(e)?this.$("> .acfe-modal-wrapper > .acfe-modal-title"):this.$("> .acfe-modal-wrapper > .acfe-modal-title > .title").html(e)},$content:function(e){return acfe.isUndefined(e)?this.$("> .acfe-modal-wrapper > .acfe-modal-content"):this.$("> .acfe-modal-wrapper > .acfe-modal-content").html(e)},$footer:function(e){return acfe.isUndefined(e)?this.$("> .acfe-modal-wrapper > .acfe-modal-footer"):this.$("> .acfe-modal-wrapper > .acfe-modal-footer").html(e)},renderTemplates:function(){this.$wrapper().length||this.$el.wrapInner('<div class="acfe-modal-wrapper"></div>'),this.$content().length||this.$wrapper().wrapInner('<div class="acfe-modal-content"></div>')},renderContent:function(){!this.$title().length&&this.get("title")?this.$wrapper().prepend('<div class="acfe-modal-title"><span class="title"></span><button class="close"></button></div>'):this.get("title")||this.$title().remove(),!this.$footer().length&&this.get("footer")?this.$wrapper().append('<div class="acfe-modal-footer"></div>'):this.get("footer")||this.$footer().remove();var e=acfe.isFunction(this.get("title"))?this.get("title").apply(this):this.get("title");this.$title(e),this.get("content")&&(t=acfe.isFunction(this.get("content"))?this.get("content").apply(this):this.get("content"),this.$content(t));var t=acfe.isFunction(this.get("footer"))?this.get("footer").apply(this):'<button class="button button-primary close">'+this.get("footer")+"</button>";this.$footer(t)},render:function(){this.renderContent(),this.$el.removeClass("-medium -large -full"),this.get("size")&&this.$el.addClass("-"+this.get("size")),this.get("class")&&this.$el.addClass(this.get("class")),"undefined"!=typeof tinymce&&acf.isset(tinymce,"ui","FloatPanel")&&this.$content().off("scroll.tinymcePanel").on("scroll.tinymcePanel",function(e){tinymce.ui.FloatPanel.hideAll()})},open:function(){this.$el.addClass("-open"),acf.doAction("acfe/modal/open",this),this.get("onOpen").apply(this),this.trigger("open")},close:function(){this.$el.removeAttr("style"),this.$el.removeClass("-open"),acf.doAction("acfe/modal/close",this),this.get("onClose").apply(this),this.trigger("close"),this.set("open",!1),this.get("destroy")&&this.remove()},onClickClose:function(e,t){e.preventDefault(),this.close()}}),acfe.closeModal=function(e){if(acfe.isUndefined(e))return t.closeLastModal();!acfe.isJquery(e)||(e=acfe.getModal(e))&&e.close()},t=new acf.Model({actions:{"acfe/modal/open":"onOpen","acfe/modal/close":"onClose"},events:{"click .acfe-modal-overlay":"onClick",keyup:"onKeyUp"},getModals:function(){return acfe.getModals({open:!0})},onOpen:function(e){this.syncModals();var t=a("body");t.hasClass("acfe-modal-opened")||t.addClass("acfe-modal-opened").append(a('<div class="acfe-modal-overlay" />')),acf.getFields({parent:e.$el,visible:!0}).map(function(e){acf.doAction("show_field",e,"group")})},onClose:function(e){this.syncModals(),this.getModals().length||(a(".acfe-modal-overlay").remove(),a("body").removeClass("acfe-modal-opened"))},onClick:function(e){e.preventDefault(),this.closeLastModal()},onKeyUp:function(e){27===e.keyCode&&a("body").hasClass("acfe-modal-opened")&&(e.preventDefault(),this.closeLastModal())},closeLastModal:function(){var e=this.getModals();e.length&&e[e.length-1].close()},syncModals:function(){this.getModals().map(function(e,t){return t===this.getModals().length-1?e.$el.removeClass("acfe-modal-sub").css("margin-left",""):void e.$el.addClass("acfe-modal-sub").css("margin-left",-500/(t+1))},this)}}),new acf.Model({events:{"click a[data-modal]":"onClick","click button[data-modal]":"onClick","click input[data-modal]":"onClick"},onClick:function(e,t){e.preventDefault();e=t.data(),t=e.modal?acfe.getModal(e.modal):acfe.getClosestModal(t);t&&(t.update(e),t.open())}}),acfe.Popup=function(e,t){return acfe.deprecatedFunction("acfe.Popup","0.8.8.11","acfe.newModal"),acfe.isJquery(e)?(t=acf.parseArgs(t,{open:!0}),acfe.newModal(e,t)):(t=acf.parseArgs(e,{open:!0}),acfe.newModal(t))})}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acf.Model.prototype.get=function(e=null,t=null){return null===e?this.data:acfe.arrayGet(this.data,e,t)},acf.Model.prototype.has=function(e){return null!==acfe.arrayGet(this.data,e)},acf.get=function(e=null,t=null){return null===e?this.data:acfe.arrayGet(this.data,e,t)},acf.has=function(e){return null!==acfe.arrayGet(this.data,e)},acf.set=function(e,t){return acfe.arraySet(this.data,e,t),this}),function(n){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.getString=function(e){return acfe.isObject(e)?JSON.stringify(e):acfe.isEmpty(e)?"":""+e},acfe.getTextNode=function(e){if(e.exists())for(row of e.contents()){var t=n.trim(n(row).text());if(t)return t}return""},acfe.ucFirst=function(e){return e.charAt(0).toUpperCase()+e.slice(1)})}(jQuery),function(){var t;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(new acf.Model({events:{"mouseenter .acfe-js-tooltip":"showTitle","mouseup .acfe-js-tooltip":"hideTitle","mouseleave .acfe-js-tooltip":"hideTitle","focus .acfe-js-tooltip":"showTitle","blur .acfe-js-tooltip":"hideTitle","keyup .acfe-js-tooltip":"onKeyUp"},showTitle:function(e,t){var n=t.attr("title");n&&(t.attr("title",""),t.data("acfe-tooltip")?t.data("acfe-tooltip").update({text:n,target:t}):(n=acf.newTooltip({text:n,target:t}),t.data("acfe-tooltip",n)))},hideTitle:function(e,t){t.data("acfe-tooltip").hide(),t.attr("title",t.data("acfe-tooltip").get("text"))},onKeyUp:function(e,t){"Escape"===e.key&&this.hideTitle(e,t)}}),t=new acf.Model({tooltips:{},events:{"click .acfe-field-tooltip":"clickTooltip"},clickTooltip:function(e,t){var n,a=t.attr("title");!a||(n=acf.getClosestField(t))&&this.toggle(n,t,a)},toggle:function(e,t,n){t.attr("title",""),this.tooltips[e.cid]?this.close(e,t,n):this.open(e,t,n)},open:function(e,t,n){this.tooltips[e.cid]=acf.newTooltip({text:n,target:t}),acfe.versionCompare(acf.get("wp_version"),">=","5.5")&&t.removeClass("dashicons-info-outline").addClass("dashicons-remove")},close:function(e,t,n){this.tooltips[e.cid].hide(),t.attr("title",this.tooltips[e.cid].get("text")),this.tooltips[e.cid]=!1,acfe.versionCompare(acf.get("wp_version"),">=","5.5")&&t.removeClass("dashicons-remove").addClass("dashicons-info-outline")}}),new acf.Model({actions:{hide_field:"onHideField"},onHideField:function(e){t.tooltips[e.cid]&&t.close(e,e.$el.find(".acfe-field-tooltip:first"))}}))}(jQuery),jQuery,"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.isArray=function(e){return Array.isArray(e)},acfe.isObject=function(e){return"object"==typeof e&&!acfe.isArray(e)},acfe.isNumeric=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},acfe.isString=function(e){return"string"==typeof e},acfe.isUndefined=function(e){return void 0===e},acfe.isFunction=function(e){return"function"==typeof e},acfe.isBool=function(e){return"boolean"==typeof e},acfe.isInt=function(e){return Number.isInteger(e)},acfe.isJquery=function(e){return e instanceof jQuery},acfe.isEmpty=function(e){return acfe.isArray(e)?!e.length:acfe.isObject(e)?e&&0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype:acfe.isString(e)?!e.length:!e&&!acfe.isNumeric(e)}),function(){var i,t,o;"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.slugify=function(e){return e.replace(/[\s\./]+/g,"-").replace(/[^\p{L}\p{N}_-]+/gu,"").replace(/-+$/,"").toLowerCase()},acfe.addQueryArgs=function(){let e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",t=1<arguments.length?arguments[1]:void 0;if(!t||!Object.keys(t).length)return e;let n=e;var a=e.indexOf("?");return-1!==a&&(t=Object.assign(acfe.getQueryArgs(e),t),n=n.substr(0,a)),n+"?"+i(t)},acfe.getQueryArgs=function(e){return(t(e)||"").replace(/\+/g,"%20").split("&").reduce((e,t)=>{const[n,a=""]=t.split("=").filter(Boolean).map(decodeURIComponent);return n&&(t=n.replace(/\]/g,"").split("["),o(e,t,a)),e},Object.create(null))},acfe.getFragment=function(e){e=/^\S+?(#[^\s\?]*)/.exec(e);return e?e[1]:""},acfe.getCurrentUrl=function(){return self.location.href},acfe.getCurrentPath=function(){return self.location.pathname},acfe.getCurrentFilename=function(){return acfe.getFilename(acfe.getCurrentPath())},acfe.getFilename=function(e){return e.split("/").pop()},i=function(e){let n="";const a=Object.entries(e);for(;c=a.shift();){let[e,t]=c;var i,o,c=Array.isArray(t)||t&&t.constructor===Object;if(c)for([i,o]of Object.entries(t).reverse())a.unshift([`${e}[${i}]`,o]);else void 0!==t&&(null===t&&(t=""),n+="&"+[e,t].map(encodeURIComponent).join("="))}return n.substr(1)},t=function(e){let t;try{t=new URL(e,"http://example.com").search.substring(1)}catch(e){}if(t)return t},o=function(n,a,i){var e=a.length,o=e-1;for(let t=0;t<e;t++){let e=a[t];!e&&Array.isArray(n)&&(e=n.length.toString()),e=["__proto__","constructor","prototype"].includes(e)?e.toUpperCase():e;var c=!isNaN(Number(a[t+1]));n[e]=t===o?i:n[e]||(c?[]:{}),Array.isArray(n[e])&&!c&&(n[e]={...n[e]}),n=n[e]}})}(jQuery),function(a){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&(acfe.copyClipboard=function(e,t){t=acf.parseArgs(t,{auto:acf.__("Data has been copied to your clipboard."),manual:acf.__("Please copy the following data to your clipboard.")});function n(e,t){var n=a('<input type="text" style="clip:rect(0,0,0,0);clip-path:none;position:absolute;" value="" />').appendTo(a("body"));n.attr("value",e).select(),document.execCommand("copy")?alert(t.auto):prompt(t.manual,e),n.remove()}navigator.clipboard?navigator.clipboard.writeText(e).then(function(){return alert(t.auto),!0}).catch(function(){n(e,t)}):n(e,t)},acfe.scrollTo=function(e,t=500){acf.isInView(e)||a("body, html").animate({scrollTop:e.offset().top-a(window).height()/2},t)},acfe.versionCompare=function(e,t,n){let a;var i;let o=0;const c={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1};function r(e){return(e=(e=(""+e).replace(/[_\-+]/g,".")).replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,".")).length?e.split("."):[-8]}function f(e){return e?isNaN(e)?c[e]||-7:parseInt(e,10):0}for(e=r(e),n=r(n),i=Math.max(e.length,n.length),a=0;a<i;a++)if(e[a]!==n[a]){if(e[a]=f(e[a]),n[a]=f(n[a]),e[a]<n[a]){o=-1;break}if(e[a]>n[a]){o=1;break}}if(!t)return o;switch(t){case">":case"gt":return 0<o;case">=":case"ge":return 0<=o;case"<=":case"le":return o<=0;case"===":case"=":case"eq":return 0===o;case"<>":case"!==":case"ne":return 0!==o;case"":case"<":case"lt":return o<0;default:return null}})}(jQuery);