=== Advanced Custom Fields: Extended ===
Contributors: hwk-fr
Donate link: https://www.acf-extended.com
Tags: acf, custom fields, meta, admin, fields
Requires at least: 4.9
Tested up to: 6.7
Requires PHP: 5.6
Stable tag: 0.9.1
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

All-in-one enhancement suite that improves WordPress & Advanced Custom Fields.

== Description ==

🚀 All-in-one enhancement suite that improves WordPress & Advanced Custom Fields. This plugin aims to provide a powerful administration framework with a wide range of improvements & optimizations.

**This plugin requires at least ACF Pro 5.8.**
If you don't already own [ACF Pro](https://www.advancedcustomfields.com/pro/), you should consider it. It's one of the most powerful WordPress plugin available.

== ⭐ Highlight ==

* 14+ New ACF Fields
* 10+ ACF Fields Enhanced
* 4+ New Field Groups Locations
* Self/Multi/Bidirectional Fields
* Advanced Fields Validation
* Flexible Content as Page Builder
* Optimize metadata with Performance Mode
* Advanced Front-End Forms Manager
* ACF Options Pages / Block Types Manager
* ACF & WordPress Meta Overview
* WordPress Post Types / Taxonomies Manager
* WordPress Options Manager
* WordPress Admin Enhancements
* WPML & Polylang Multilingual support
* ... And many more features

== 💎 Pro Highlight ==

* 20+ New ACF Fields
* 10+ ACF Fields Enhanced
* 20+ New Locations
* Payment Field with Stripe & PayPal Express
* Flexible Content Grid System
* Flexible Content Layouts Locations Rules
* Templates Manager
* Builtin Classic Editor
* Settings UI
* Screen Layouts
* Force Json Sync
* Field Visibility Settings
* Global Field Conditional Rules
* ... And many more features

== 🤟 Philosophy ==

* Seamless integration
* No extra menu, ads or notices
* Built by developers, for developers

== 🛠️ Links ==

* [Website](https://www.acf-extended.com)
* [Documentation](https://www.acf-extended.com/features)
* [Guides](https://www.acf-extended.com/guides)
* [Roadmap](https://www.acf-extended.com/roadmap)
* [GitHub](https://github.com/acf-extended/ACF-Extended)
* [Twitter](https://twitter.com/ACFExtended)
* [Twitter](https://twitter.com/hwkfr) (Personal)
* [Slack Community](https://slack.acf-extended.com)

== 🧰 Tools ==

* [FAQ](https://wordpress.org/plugins/acf-extended/#faq)
* [Support](https://wordpress.org/support/plugin/acf-extended)
* [Feature Request](https://wordpress.org/support/plugin/acf-extended)
* [Reviews](https://wordpress.org/support/plugin/acf-extended/reviews/#new-post)
* [Donation](https://ko-fi.com/acfextended)

== 📁 Field Groups ==

**[Advanced Settings](https://www.acf-extended.com/features/field-groups/advanced-settings)**
Enable advanced settings for all fields within the Field Group.

**[Auto Sync PHP](https://www.acf-extended.com/features/field-groups/autosync)**
Automatically synchronize field groups with local PHP files upon field group updates. This feature will create, include and update a local PHP file for each field group.

**[Auto Sync Json](https://www.acf-extended.com/features/field-groups/autosync)**
Control which field groups you want to synchronize with local Json files. Display warnings if the Json file has been manually deleted.

**[Categories](https://www.acf-extended.com/features/field-groups/categories)**
Spice up your field groups with a custom taxonomy and filter field groups by terms.

**[Custom Key](https://www.acf-extended.com/features/field-groups/custom-key)**
Set custom field group key. Example: `group_custom_name`.

**[Custom Meta](https://www.acf-extended.com/features/field-groups/custom-meta)**
Add custom metas (key/value) in the field group administration.

**[Display Title](https://www.acf-extended.com/features/field-groups/display-title)**
Display an alternative field group title in post edition screen.

**[Field Group UI](https://www.acf-extended.com/features/field-groups/field-group-ui)** **(PRO)**
Enable enhancements to the Field Group UI for a better user experience.

**[Force Sync](https://www.acf-extended.com/features/field-groups/force-sync)** **(PRO)**
Always keep Json files synchronized with the Field Groups in the database.

**[Hide on Screen](https://www.acf-extended.com/features/field-groups/hide-on-screen)** **(FREE / PRO)**
Hide Gutenberg Block Editor and 10+ more items to hide in the field group settings.

**[Instructions Placement](https://www.acf-extended.com/features/field-groups/instruction-placement)**
New instruction placements let you display field description "above the fields" or in a "tooltip".

**[Location: Advanced Post](https://www.acf-extended.com/features/field-groups/locations/advanced-post)** **(PRO)**
A collection of multiple new Field Groups locations allowing developers to target posts with specific conditions (Post author, date, slug, path etc...).

**[Location: Advanced Menu Item](https://www.acf-extended.com/features/field-groups/locations/advanced-menu-item)** **(PRO)**
Target specific "Menu Item Depth" or "Menu Item Type" from the Field Groups Locations rules.

**[Location: Advanced Taxonomy Term](https://www.acf-extended.com/features/field-groups/locations/advanced-taxonomy)** **(PRO)**
A collection of multiple new Field Groups locations allowing developers to target taxonomy and terms with specific conditions (Term name, parent, slug etc...).

**[Location: All post types](https://www.acf-extended.com/features/field-groups/locations/all-post-types)**
Display field groups on all post types edition screen.

**[Location: Attachment List](https://www.acf-extended.com/features/field-groups/locations/attachment-list)** **(PRO)**
Display field group on attachment admin list screen.

**[Location: Dashboard Widgets](https://www.acf-extended.com/features/field-groups/locations/dashboard)** **(PRO)**
Display field groups and update ACF Fields from the WP Dashboard.

**[Location: Field Value](https://www.acf-extended.com/features/field-groups/locations/field-value)** **(PRO)**
Display a field group based on the field value of an another field group.

**[Location: Post type Archive](https://www.acf-extended.com/features/field-groups/locations/post-type-archive)**
Add an Archive Option Page under the Post Type admin menu. Display and save any field groups within it.

**[Location: Post type List](https://www.acf-extended.com/features/field-groups/locations/post-type-list)**
Display field group on post types admin list screen.

**[Location: Taxonomy List](https://www.acf-extended.com/features/field-groups/locations/taxonomy-list)**
Display field group on taxonomies admin list screen.

**[Location: User List](https://www.acf-extended.com/features/field-groups/locations/user-list)** **(PRO)**
Display field group on user admin list screen.

**[Location: Woocommerce](https://www.acf-extended.com/features/field-groups/locations/woocommerce)** **(PRO)**
Display field groups on Woocommerce pages.

**[Location: WP Settings](https://www.acf-extended.com/features/field-groups/locations/wp-settings)** **(PRO)**
Display field groups on WP Settings pages: General, Writing, Reading, Discussion, Media and Permalinks.

**[Local Field Groups](https://www.acf-extended.com/features/)**
Display local field groups that are loaded by ACF, but not available in the ACF field group administration. Example: Field groups that are registered in the `functions.php` file, but not in the ACF UI.

**[Note](https://www.acf-extended.com/features/field-groups/note)**
Add a personal note in the field group administration. Only visible to administrators.

**[Permissions](https://www.acf-extended.com/features/field-groups/permissions)**
Add permission layer to field groups. Choose which roles can view & edit field groups in the post edition screen.

**[Raw Data](https://www.acf-extended.com/features/field-groups/raw-data)**
Display raw field group data in a modal to check your configuration & settings.

== ⚙️ Fields Settings ==

**[Advanced Settings](https://www.acf-extended.com/features/field-settings/field-advanced-settings)**
A more sophisticated field settings based on specified location (administration/front-end). Example: Field is required only in front-end.

**[Advanced Validation](https://www.acf-extended.com/features/field-settings/advanced-validation)**
A more sophisticated validation conditions (AND/OR) with custom error messages based on specified location (administration/front-end).

**[Self/Multi/Bidirectional fields](https://www.acf-extended.com/features/field-settings/bidirectional-fields)**
An advanced bidirectional setting (also called post-to-post) is available for the following fields: Relationship, Post object, User & Taxonomy terms. Fields will work bidirectionally and automatically update each others. Works in groups & clones.

**[Field Visibility](https://www.acf-extended.com/features/field-settings/field-visibility)** **(PRO)**
Get quick access to "Field Visibility", "Label Visibility", "Instructions Visibility" and "Required Setting" for the following screens: "Everywhere", "Front-end" and "Administration".

**[Global Condition](https://www.acf-extended.com/features/field-settings/global-condition)** **(PRO)**
Enable Global Conditional Logic for a specific field, which can then be used in an another Field Group as condition, both as Field Group Condition and Field Condition.

**[Instruction Placement](https://www.acf-extended.com/features/field-settings/field-instruction-placement)** **(PRO)**
Override a specific field instruction placement to any position: Below labels, below fields, above fields or tooltip.

**[Instruction Read More](https://www.acf-extended.com/features/field-settings/instruction-read-more)** **(PRO)**
Allow to expand instructions text with a "Read More" link. This feature is useful for lengthy instructions text.

**[Min/Max](https://www.acf-extended.com/features/field-settings/min-max)** **(PRO)**
Minimum & maximum items is a global field setting that let you define a specific number of items that can or should be added by the user.

**[Permissions](https://www.acf-extended.com/features/field-settings/field-permissions)**
Add permission layer to fields. Choose which roles can view & edit fields in the post edition screen. (can be combined with field groups permissions).

**[Raw data](https://www.acf-extended.com/features/field-settings/field-raw-data)**
Display raw field data in a modal to check your configuration & settings.

**[Required Message](https://www.acf-extended.com/features/field-settings/required-message)** **(PRO)**
This setting allow developers to define a custom error message within the field settings for a more intuitive user experience.

== 🏷️ Fields ==

**[Advanced Link](https://www.acf-extended.com/features/fields/advanced-link)**
Display a modern Link Selection in a modal. Posts, Post Types Archives & terms selection can be filtered in the field administration.

**[Block Editor](https://www.acf-extended.com/features/fields/block-editor)** **(PRO)**
Display an isolated Block Editor field on admin screen (with Classic Editor enabled) or on the front-end.

**[Block Types](https://www.acf-extended.com/features/fields/block-types)** **(PRO)**
Display an ACF Block Types selector as radio, checkbox or select field type.

**[Button](https://www.acf-extended.com/features/fields/button)**
Display a custom submit or button. Built-in ajax call setting. Usage example available in the field administration.

**[Checkbox](https://www.acf-extended.com/features/fields/checkbox)** **(FREE / PRO)**
Define grouped choices values using `## Title` markup in the field's choices.

**[Clone](https://www.acf-extended.com/features/fields/clone)**
Allow users to edit clone fields in a modal. Choose the edit button text, display close button and the modal size.

**[Code Editor](https://www.acf-extended.com/features/fields/code-editor)**
Edit code using the native WP Core Codemirror library. Default languages: Text/HTML, Javascript, CSS, PHP mixed/plain.

**[Color Picker](https://www.acf-extended.com/features/fields/color-picker)** **(PRO)**
A collection of advanced settings for the ACF Color Picker. The field can now be displayed as a palette, custom colors can be predefined and RGBA mode is supported.

**[Columns](https://www.acf-extended.com/features/fields/columns)** **(FREE / PRO)**
Organize and re-arrange your fields using columns and line-breaks. The field acts like the ACF Accordion/Tab field and allow you to create virtually grouped fields which will be displayed inside columns.

**[Countries](https://www.acf-extended.com/features/fields/countries)** **(PRO)**
Display a Country selector as radio, checkbox or select field type.

**[Currencies](https://www.acf-extended.com/features/fields/currencies)** **(PRO)**
Display a Currency selector as radio, checkbox or select field type.

**[Date/Timepicker](https://www.acf-extended.com/features/fields/datepicker)** **(FREE / PRO)**
Display a modern UI of the ACF Datepicker field. CSS and icons have been enhanced to fit WordPress admin UI and colors.

**[Date Range Picker](https://www.acf-extended.com/features/fields/date-range-picker)** **(PRO)**
Display a Date Range Picker. The field support a wide range of customization, such as: Placeholder, Default dates, Range Restriction, Date restriction, No weekends etc.

**[Dynamic Render](https://www.acf-extended.com/features/fields/dynamic-render)**
Display custom HTML/PHP content using a simple named hook.

**[Field Groups](https://www.acf-extended.com/features/fields/field-groups-selector)** **(PRO)**
Display an ACF Field Groups selector as radio, checkbox or select field type.

**[Field Types](https://www.acf-extended.com/features/fields/field-types)** **(PRO)**
Display an ACF Field Types selector as radio, checkbox or select field type.

**[Fields](https://www.acf-extended.com/features/fields/fields-selector)** **(PRO)**
Display an ACF Fields selector as radio, checkbox or select field type.

**[File](https://www.acf-extended.com/features/fields/file)** **(FREE / PRO)**
Choose the uploader type, enable multi file upload and dropzone.

**[Flexible Content](https://www.acf-extended.com/features/fields/flexible-content)** **(FREE / PRO)**
Displayed an enhanced version of the native Flexible Content field. Dozens of new settings and settings were added, allowing developers to create the most advanced page builder and fully control the field's behavior.

**[Forms](https://www.acf-extended.com/features/fields/forms)**
Select any dynamic form (format: checkbox, radio or select).

**[Google Map](https://www.acf-extended.com/features/fields/google-map)** **(PRO)**
A collection of new settings added to the ACF Google Map Field that allow developers to have more control over the field behavior.

**[Google reCaptcha](https://www.acf-extended.com/features/fields/recaptcha)**
Display a reCaptcha field (compatible v2 & v3).

**[Group](https://www.acf-extended.com/features/fields/group)**
Allow users to edit group fields in a modal Choose the edit button text, display close button and the modal size

**[Hidden Input](https://www.acf-extended.com/features/fields/hidden-input)**
Display a hidden input with custom name/value

**[Image](https://www.acf-extended.com/features/fields/image)** **(FREE / PRO)**
Choose the uploader type, customize the upload folder and set the image as post featured thumbnail

**[Image Selector](https://www.acf-extended.com/features/fields/image-selector)** **(PRO)**
Display an Image Selector field.

**[Image Sizes](https://www.acf-extended.com/features/fields/image-sizes)** **(PRO)**
Display an Image Sizes selector as radio, checkbox or select field type.

**[Languages](https://www.acf-extended.com/features/fields/languages)** **(PRO)**
Display a Language selector as radio, checkbox or select field type, compatible with WPML & Polylang.

**[Menu Locations](https://www.acf-extended.com/features/fields/menu-locations)** **(PRO)**
Display a Menu Locations selector as radio, checkbox or select field type.

**[Menus](https://www.acf-extended.com/features/fields/menus)** **(PRO)**
Display a Menu selector as radio, checkbox or select field type.

**[Options Pages](https://www.acf-extended.com/features/fields/options-pages)** **(PRO)**
Display an ACF Options Pages selector as radio, checkbox or select field type.

**[Payment](https://www.acf-extended.com/features/fields/payment)** **(PRO)**
Display a Payment Field that supports with Stripe & PayPal Express gateways, working on both front-end and back-end.

**[Payment Cart](https://www.acf-extended.com/features/fields/payment-cart)** **(PRO)**
Display an optional Payment Cart to easily setup an e-commerce solution.

**[Payment Selector](https://www.acf-extended.com/features/fields/payment-selector)** **(PRO)**
Display an optional Payment Selector which let the user switch the payment gateway.

**[Phone Number](https://www.acf-extended.com/features/fields/phone-number)** **(PRO)**
Display a fully customizable international Phone Number field.

**[Post Field](https://www.acf-extended.com/features/fields/post-field)** **(PRO)**
The Post Field is a new field that allow developers to move native WordPress fields such as Post Title, Date, Status, Visibility, Permalink etc.

**[Post Formats](https://www.acf-extended.com/features/fields/post-formats)** **(PRO)**
Display a Post Format selector as radio, checkbox or select field type.

**[Post Object](https://www.acf-extended.com/features)** **(FREE / PRO)**
Allow user to enter custom value which will be saved as a new post, or enable the inline post creation/edit.

**[Post Status](https://www.acf-extended.com/features/fields/post-statuses)**
Select any post status (format: checkbox, radio or select)

**[Post Types](https://www.acf-extended.com/features/fields/post-types)**
Select any post type (format: checkbox, radio or select)

**[Radio](https://www.acf-extended.com/features/fields/radio)** **(FREE / PRO)**
Define grouped choices values using `## Title` markup in the field's choices.

**[Relationship](https://www.acf-extended.com/features/fields/relationship)** **(PRO)**
The Relationship field includes new settings allowing users to create and edit post on-the-fly from the post edit screen.

**[Repeater](https://www.acf-extended.com/features/fields/repeater)**
Add stylised to 'Add Row' button, lock rows and remove repeater's actions.

**[Select](https://www.acf-extended.com/features/fields/select)** **(FREE / PRO)**
Change the default "Select" placeholder text and Search Input placeholder and allow user to enter custom values.

**[Slug](https://www.acf-extended.com/features/fields/slug)**
A slug text input (ie: `my-text-input`).

**[Tab](https://www.acf-extended.com/features/fields/tab)** **(PRO)**
Disable the last opened tab user preference. Which means that when the user will refresh the page, it will always load the first tab.

**[Taxonomies](https://www.acf-extended.com/features/fields/taxonomies)**
Select any taxonomy (format: checkbox, radio or select)

**[Taxonomy Terms](https://www.acf-extended.com/features/fields/taxonomy-terms)**
Select any terms of any taxonomies, allow specific terms, level or childrens (format: checkbox or select). Terms can be loaded & saved for the current post (just like the native ACF Taxonomy field)

**[Templates](https://www.acf-extended.com/features/fields/templates-selector)** **(PRO)**
Display an ACF Extended Templates selector as radio, checkbox or select field type.

**[Textarea](https://www.acf-extended.com/features/fields/textarea)**
Switch font family to monospace and allow tab indent.

**[True/False](https://www.acf-extended.com/features/fields/true-false)** **(PRO)**
Five new styles have been added to the native True/False field.

**[User Roles](https://www.acf-extended.com/features/fields/user-roles)**
Select any user role (format: checkbox, radio or select)

**[WYSIWYG Editor](https://www.acf-extended.com/features/fields/wysiwyg-editor)** **(PRO)**
New settings allowing developers to have more control over the field behavior.

== 🛠️ Modules ==

**[Block Types UI](https://www.acf-extended.com/features/modules/dynamic-block-types)** **(FREE / PRO)**
The Dynamic Block Types module allows you to register and manage ACF Block Types from your WordPress admin, in ACF > Block Types menu. Pro version allows to sync Json/PHP files.

**[Classic Editor](https://www.acf-extended.com/features/modules/classic-editor)** **(PRO)**
ACF Extended is bundled with a custom merged version of the Classic Editor & Disable Gutenberg plugins.

**[Developer Mode](https://www.acf-extended.com/features/modules/developer-mode)** **(FREE / PRO)**
The Developer Mode allow you to view all Posts, Terms, Users & Options custom metadata in a readable format. This feature is very useful to check what is actually saved in any WordPress Object.

**[Forms](https://www.acf-extended.com/features/modules/dynamic-forms)** **(FREE / PRO)**
Manage Advanced ACF Forms from the WordPress administration. This module is an enhanced version of the native ACF Form feature. While all native settings can be used, Dynamic Forms adds many new settings and introduce "Actions" for a complete control over the form behavior.

**[Options Pages UI](https://www.acf-extended.com/features/modules/dynamic-options-pages)** **(FREE / PRO)**
The Dynamic Options Pages module allows you to register and manage ACF Options Pages from your WordPress admin, in ACF > Options Pages menu. Pro version allows to sync Json/PHP files.

**[Performance Mode](https://www.acf-extended.com/features/modules/performance-mode)** **(FREE / PRO)**
A unique module that allows developers to optimize database load when dealing with hundreds or thousands of metadata with two different methods: Ultra & Hybrid Engines.

**[Post Types UI](https://www.acf-extended.com/features/modules/dynamic-post-types)** **(FREE / PRO)**
The Dynamic Post Types module allows you to register and manage custom post types from your WordPress admin, in Tools > Post Types menu. Pro version allows to sync Json/PHP files.

All native post types settings can be set within the UI. ACF Extended also adds more advanced settings allowing to manage posts per page, order etc…

**[Rewrite Rules](https://www.acf-extended.com/features/modules/rewrite-rules)** **(PRO)**
Get an overview of all WordPress permalinks structures and rules. Test URLs, export rules and flush permalinks from the UI.

**[Scripts UI](https://www.acf-extended.com/features/modules/scripts)** **(PRO)**
Run custom scripts on thousands of posts. Including builtin "Orphan Meta Cleaner", "Script Launcher" and "Performance Converter" scripts.

**[Settings UI](https://www.acf-extended.com/features/modules/settings-ui)** **(FREE / PRO)**
The Settings UI allows developers to get an overview of all ACF and ACF Extended settings values from the ACF > Settings menu.

**[Taxonomies UI](https://www.acf-extended.com/features/modules/dynamic-taxonomies)** **(FREE / PRO)**
The Dynamic Taxonomies module allows you to register and manage custom taxonomies from your WordPress admin, in Tools > Taxonomies menu. Pro version allows to sync Json/PHP files.

All native taxonomies settings can be set within the UI. ACF Extended also adds more advanced settings allowing to manage posts per page, order etc…

**[Templates](https://www.acf-extended.com/features/modules/templates)** **(PRO)**
Manage default ACF values in an advanced way and sync templates with Json/PHP files.

== 🖥️ WordPress ==

**[Ajax Author Box](https://www.acf-extended.com/features/wordpress/ajax-author-box)**
The native WP Author Metabox has been replaced with an Ajax version allowing to manage thousands of users without slowing down the post administration. The new Author box also include an inline search input.

**[Enhanced UI](https://www.acf-extended.com/features/wordpress/enhanced-ui)**
The Taxonomy, User profile & Settings views have been enhanced for a more consistent administration experience, using CSS/JS only.

**[Polylang](https://www.acf-extended.com/features/wordpress/polylang)**
ACF Extended adds a new layer of compatibility for Polylang. ACF Options Pages and all ACF Extended Modules (Dynamic Post Type, Taxonomy, Options Pages, Block Type) are compatible.

**[Screen Layouts](https://www.acf-extended.com/features/wordpress/screen-layouts)** **(PRO)**
Post Edit screens have been enhanced allowing up to 3 columns layout and multiple variations.

**[WPML](https://www.acf-extended.com/features/wordpress/wpml)**
ACF Extended adds a new layer of compatibility for WPML. ACF Options Pages and all ACF Extended Modules (Dynamic Post Type, Taxonomy, Options Pages, Block Type) are compatible.

**[WP Options](https://www.acf-extended.com/features/wordpress/options)**
Manage WordPress Options from the Settings > Options page. Options value (strings, serialized & Json) will be displayed in a readable form. Values can be edited or deleted.

== ❤️ Early Supporters ==

* Thanks to [Brandon A.](https://twitter.com/AsmussenBrandon) for his support & tests
* Thanks to [Damien C.](https://twitter.com/DamChtlv) for his support & tests
* Thanks to [Valentin P.](https://twitter.com/Val_Pellegrin) for his support & tests
* Thanks to Damian P. for his support & tests
* Thanks to [Jaakko S.](https://twitter.com/jsaarenk) for his support & tests
* Thanks to [Renan A.](https://twitter.com/altendorfme) for his support & tests

== 🥰 Donors ==

* Thanks to RavenSays
* Thanks to Dave A.
* Thanks to Rob H.
* Thanks to Valentin P.
* Thanks to Alan A.
* Thanks to Damian C.
* Thanks to Andrew
* Thanks to Kimantis
* Thanks to Anonymous
* Thanks to Chris
* Thanks to Dennis D.
* Thanks to Cody R.
* Thanks to Jamie
* Thanks to Dave A.
* Thanks to Paul M.
* Thanks to David B.
* Thanks to Swingjac
* Thanks to Erik
* Thanks to Giancarlo P.
* Thanks to Geuer M.

== Installation ==

= Wordpress Install =

1. Install Advanced Custom Fields: Pro
2. Upload the plugin files to the `/wp-content/plugins/acf-extended/` directory, or install the plugin through the WordPress plugins screen directly.
3. Activate the plugin through the 'Plugins' screen in WordPress.
4. Everything is ready!

== Frequently Asked Questions ==

= Where can I find the documentation? =

You'll find the documentation of every features on the [official ACF Extended website](https://www.acf-extended.com/features).

= Where can I submit a bug report? =

You can file a report on the [Plugin Support Forum](https://wordpress.org/support/plugin/acf-extended/) or on the [Github Page](https://github.com/acf-extended/ACF-Extended) if you prefer.

= Where can I submit a feature request? =

You can submit a feature request on the [Plugin Support Forum](https://wordpress.org/support/plugin/acf-extended/). Feature requests are all listed on the [Official Trello Board](https://trello.com/b/QEgpU7CL/acf-extended).

= What is planned for the next update? =

The content of the upcoming patch and work in progress features are all listed on the [Official Roadmap](https://www.acf-extended.com/roadmap) (or the Trello Board).

== Screenshots ==

1. Flexible Content Preview
2. Flexible Content Modal
3. New Fields
4. Post Type List Location
5. Self/Multi/Bidirectional Fields
6. Developer Mode
7. Dynamic Post Types
8. Enhanced WordPress UI

== Changelog ==

= 0.9.1 =

**ACF Extended Pro 0.9.1:**

* Field: Flexible Content - Added "Dynamic Preview Iframe" feature
* Field: Flexible Content - Added "Dynamic Preview Responsive" feature
* Field: Flexible Content - "Dynamic Preview Iframe" render previews in isolated iframes
* Field: Flexible Content - "Dynamic Preview Responsive" allows to switch container preview size
* Field: Address - Cleaned unused argument
* Field: Google Map - Added `acfe_render_google_map()` compatibility with sub fields
* Field: Google Map - Enhanced `acfe_render_google_map()` compatibility with Dynamic Preview
* Field: Radio - Fixed "Custom Choice" render
* Field: WYSIWYG - Enhanced `source_code` modal reset stylesheet
* Field Group Location: Renamed "Dashboard" to "WP Dashboard" for consistency
* Module: Forms - Ajax submission now redraw form on submission

**ACF Extended Basic 0.9.1:**

* Field: Advanced Link - Enhanced malformed values handling
* Field: Flexible Content - Added `acfe/flexible/toggle_hide` filter to control row visibility
* Field: reCaptcha - Enhanced error handling when using incorrect API key
* Field: Taxonomy Terms - Fixed ajax nonce verification when field is cloned
* Module: Forms - Enhanced File/Image/Gallery "Uploaded To" logic using related Form Action

= ******* =

**ACF Extended Pro *******:**

* Field: Address - Added new Address Field
* Field: Address - Allows to enter/search an address with autocomplete (Google Places API)
* Field: Color Picker - Added "Return Format: Slug" setting
* Field: Color Picker - Added "Slug" & "RGBA Array" into the "Color Array" return format
* Field: Color Picker - Added compatibility with CSS variables for "Palette" display
* Field: Color Picker - Added `editor-gradient-presets` theme support
* Field: Date Range Picker - Enhanced compatibility with custom hooks
* Field: Date Range Picker - Allowed to clear to input manually when "Allow null" is enabled
* Field: Date Range Picker - Added "x" icon to clear the input when "Allow null" is enabled
* Field: Date Range Picker - Added "Prepend" & "Append" settings
* Field: Google Map - Added `acfe_render_google_map()` helper to display a map on front-end
* Field: Google Map - Fixed ACF UI settings sync when interacting with the Map Preview

**ACF Extended Basic *******:**

* Field: Flexible Content - Fixed `get_row()` & `get_row_layout()` usage in WP Admin preview
* Field: Taxonomy Terms - Fixed ajax nonce compatibility with ACF 6.3.10
* Field Group: Small UI/CSS tweaks

= ******* =

**ACF Extended Pro *******:**

* Field: Phone Number - Fixed data when querying the "Lightweight Phonenumber Library"
* Field: Image Selector - Fixed images with parameters usage when using "Multiple Select"
* Field: Image Selector - Enhanced file extension detection when using images with parameters
* Field: Image Selector - Reworked image rendering using proper `<img />` with `object-fit`
* Module: Performance - Hybrid - Restoring a revision doesn't append meta references anymore

**ACF Extended Basic *******:**

* Field: Flexible Content - Dynamic Render - Filters can now change `wp_enqueue_style()` args
* Field: Flexible Content - Dynamic Render - Filters can now change `wp_enqueue_script()` args
* Field: Flexible Content - Dynamic Render - Added `get_row_index()` usage compatibility
* Field: Flexible Content - Dynamic Render - Fixed `WP_Query` loop usage with `get_field()`
* Field: Icon Picker - Fixed "Media Library" image preview
* Module: Forms - Allowed usage of fields using the same name as fields in the back-end
* Module: Forms - Fixed field settings nonce verification compatibility with ACF 6.3.10
* Module: Performance - Ultra - Revisions are now supported when allowing specific post types
* Module: Performance - Ultra - Revisions are now compatible with "Save as individual meta"
* Module: Performance - Ultra - Enhanced "Revision Comparison" view with more readable data
* Module: Performance - Ultra - Enhanced `acf` meta detection when the value is malformed
* Field Group: Location - Fixed warning when using third party location that is later uninstalled
* General: Enhanced `acfe_include()` helper
* General: Fixed potentital PHP 8 warning with `acfe_starts_with()` helper
* General: Updated WP 6.7 metabox order handle positioning
* General: Bump WP version up to 6.7

= ******* =

**ACF Extended Pro *******:**

* Field: Block Editor - Added WP 6.6 compatibility

**ACF Extended Basic *******:**

* General: Updated WP 6.6 metabox order handle positioning

= ******* =

**ACF Extended Pro *******:**

* Field: Flexible Content - Grid - Added filter to rename columns (1/12, 2/12, 3/12…)
* Field: Google Map - Added small UI settings tweaks
* Field: Payment - Removed Polyfill library dependency
* Field Settings: Global Condition - Fixed Global Field Condition operators with ACF 6.3
* Field Settings: Instructions Tooltip - Fixed tooltip always visible when switching tab
* Module: Force Sync - Fixed sync from Dashboard with Flexible Content Toggle Layout
* Module: Force Sync - Enhanced Force Delete compatibility with Json and PHP sync

**ACF Extended Basic *******:**

* Field: Flexible Content - Added missing "Copy/Toggle Layout" localized strings
* Field: Flexible Content - Added JS hook `acfe/flexible/preview/layout=my-layout` variation
* Field: Flexible Content - Enhanced ACF UI CSS settings with "Tabs Hidden"
* Field: Forms - Fixed potential warning when switching from Checkbox to Radio
* Module: Form - Added safe guard logic for ACF fields in "Load" actions
* Module: Form - Fixed outsourced Clone Seamless Fields values
* Module: Form - Fixed Template Tags warning with PHP 8
* Module: Form - User - Builtin Validation now check if email is already used
* Module: Form - User - Builtin Validation now check the login during the insert
* Module: Form - User - Builtin Validation now check the `illegal_user_logins` wp filter
* Module: Form - User - Updating user login now automatically re-log the user
* General: Fixed Ajax Nonce verification compatibility with ACF 6.3.2

= ******* =

**ACF Extended Pro *******:**

* Field Group Location: Added "Dashboard > Widget" Location
* Field Group Location: Dashboard Widget allows to update fields from the WP Dashboard
* Field Group Location: Added "Woocommerce" Cart, Checkout, Account, Shop & Terms
* Modules: AutoSync - `acfe/php` & `acfe/json` settings control the AutoSync metabox visbility

**ACF Extended Basic *******:**

* Module: Form - Added ability to load form with ajax
* Module: Form - Added "Validation > Global Error" settings to customize error messages
* Module: Form - Added Instruction Placement "Tooltip" & "Above Field" options
* Module: Form - Cleaned front-end forms HTML markup
* Module: Form - Enhanced front-end forms JS logic
* Module: Form - Enhanced compatibility for multiple forms on the same page
* Module: Form - Enhanced `{field:gallery}` formatted value
* Module: Form - Enhanced "Validation > Grouped Errors" to use the "Errors Class" setting
* Module: Form - Fixed missing `l10n` acf setting compatibility for export
* Module: Form - Fixed Shortcode usage in Success Message
* Module: Form - Fixed slashed text in Success Message template tags
* Module: Form - Post - Fixed Image/File/Gallery "Uploaded to" filter in Media Modal
* Module: Form - Post - Fixed Gallery attachement not being connected to created/updated post
* Module: Form - Post - Fixed Shortcode usage in Post Content
* Module: Form - Post - Fixed current post not displaying new data if updated without redirect
* Module: Form - Email - Fixed Shortcode usage in e-mail content/html
* Module: Form - User - "Log In" action doesn't require to redirect on success anymore
* Module: Form - Added `acfe.renderForm()` & `acfe.renderFormAjax()` JS helpers
* Module: Form - Added `acfe_enqueue_form()` PHP helper
* Module: Form - Added `acfe_get_form_action()` allowing dot notation & default arguments
* Module: Form - Added `acfe/form/submit_success_data` PHP hook to pass data to the JS
* Module: Form - Added `acfe/form/validation_begin` JS hook to target front-end validation
* Module: Form - Added `acfe/form/validation_failure` JS hook to target front-end validation
* Module: Form - Added `acfe/form/validation_success` JS hook to target front-end validation
* Module: Form - Added `acfe/form/validation_complete` JS hook to target front-end validation
* Field Groups: Advanced Settings - Added ability to assign field's sub array settings using dot notation
* Field Groups: AutoSync - Added ability to remove existing Json/PHP sync from the Field Group UI
* Field Groups: AutoSync - `acfe/php` & `acfe/json` settings now control the sync metabox visbility
* Field Groups: AutoSync - Added `acfe/settings/should_delete_php` hook to control the file removal
* Field Groups: AutoSync - Added `acfe/settings/should_delete_json` hook to control the file removal
* Field Groups: AutoSync - Fixed Json files not being correctly deleted when Field Group use a custom path
* General: Modal - Renamed `new_modal` js hook into `acfe/new_modal` for consistency
* General: Enhanced `acfe_get_post_id()` helper

= ******* =

**ACF Extended Pro *******:**

* Modules: Added "Bulk Sync Changes from DB/JSON/PHP" feature
* Field: Block Editor - Fixed field when copy/pasted within a Flexible Content
* Field: Color Picker - Fixed "Palette" tooltip title being duplicated on change

**ACF Extended Basic *******:**

* Module: Form - Fixed `acfe/form/submit_post_args` not passing new `post_id` to fields if changed
* Module: Form - Fixed `{field:field_6635cd66ba409}` not working with group subfields
* Module: Form - Fixed Post Thumbnail reset when a Form has no post thumbnail
* Field: Flexible Content - Fixed "Settings Modal" subfields loop within a sub flexible content
* Field: Flexible Content - Fixed "Copy/Paste Layout" not working on non-HTTPS environments
* Field: Taxonomy Terms - Enhanced front-end forms compatibility
* Field: Taxonomy Terms - Improved `{field:taxonomy_terms}` tag format
* Field: Taxonomy - Enhanced front-end forms compatibility

= ******* =

**ACF Extended Pro *******:**

* Module: Form - Added Ajax Submission feature
* Module: Form - Ajax Submission can be enabled in the Form "Settings" tab

**ACF Extended Basic *******:**

* Module: Form - Added "Scroll to message" setting in the "Success" tab
* Module: Form - Enhanced `acfe/form/load_form` strategy
* Module: Form - Enhanced `map` argument to allow override loaded values from Actions
* Module: Form - Enhanced `{render:field_name}` to first search within mapped field groups
* Module: Form - Renamed `acfe/form/success_form` to `acfe/form/render_success` for consistency
* Module: Form - Added `acfe/form/submit_success` as very early hook, in page headers
* Module: Form - Fixed Select field "Custom Value" setting compatibility with forms
* Module: Form - Fixed duplicate rows in `{field:repeater}` Template Tag
* Module: Form - Added missing `wpautop()` on Success Message
* Module: Form - Minor CSS compatibility tweaks for mac
* Module: Form - User Action - Fixed the "Roles" load value as raw array
* Module: AutoSync - Fixed PHP files not being correctly deleted when Field Group was removed using a custom path
* Field Groups - Local - Fixed missing "Local" tab when there are only AutoSync PHP files
* Field Groups: Minor CSS tweaks
* Field: reCaptcha - Reworked & enhanced code logic
* Settings UI: Fixed missing reCaptcha ACF settings in the "Fields" tab

= ******* =

**ACF Extended Pro *******:**

* Module: Global Field Location - Fixed Field Group Location escaping issues
* Field Groups: Advanced Locations - Fixed Field Group Location escaping issues
* Field: Payment - Fixed `{field:payment}` Template Tag output
* Field: Flexible Content - Grid - Added translatable strings
* General: Country/Currency/Language - Added PHP filters to allow customization

**ACF Extended Basic *******:**

* Module: Form - Enhanced load/validate/submit/render hooks strategy
* Module: Form - Fixed instruction placement undefined key warning
* Module: Form - "Current Post" Target/Source now correctly use the WP Query loop `post_id`
* Module: Form - Enhanced multiple forms on single page support
* Module: Form - Added missing `wpautop()` on email content when using "Content Editor"
* Module: Form - Fixed Taxonomy "Load Terms" & Image "Featured Thumbnail" compatibility
* Fields Condition - Enhanced `acf.newCondition` closer to native ACF logic
* General: Updated French translation

= ******* =

* Module: Forms - Fixed upgrade issues when using distant Auto Update
* Module: Forms - Added safeguard logic when third party plugin enqueue selectWoo
* Field: Flexible Content - Added Navigator Clipboard API to allow copy very large layout data

= 0.9 =

**ACF Extended Pro 0.9:**

* Module: Forms - Added Json & PHP Sync feature
* Module: Forms - PHP sync files are saved/loaded from `/my-theme/acfe-php/forms`
* Module: Forms - Json sync files are saved/loaded from `/my-theme/acf-json/forms`
* Modules: "View" link in posts list view is now removed when the item disabled
* Field: Block Editor - Fixed Woocommerce `get_current_screen()` usage on front-end
* Field: Block Editor - Fixed crash when used within a Flexible Content with Woocommerce
* Field: Block Editor - Enhanced Woocommerce compatibility
* Field: Block Editor - Fixed blocks arguments being stripped when submitted on front-end
* Field: Block Editor - Added WP 6.5 compatibility
* Field: Flexible Content - Grid - Fixed "No Wrap" setting not using "Default size: 12" when adding a layout
* Field: Image Sizes - Fixed field not using the correct "Display Format" setting

**ACF Extended Basic 0.9:**

* Module: Forms - Updated module to v3
* Module: Forms - Ability to register/render forms using PHP code only
* Module: Forms - Updated Template Tags logic with a new API, allowing developers to create their own tag
* Module: Forms - New Template Tags: `{render}`, `{get_post}`, `{get_term}`, `{get_user}` & `{date}`
* Module: Forms - Ability to use Tags in Tags. Ie: `{get_field:my_field:{action:post:ID}:false}`
* Module: Forms - Ability to render submit button with `{render:submit}` anywhere in the form
* Module: Forms - ACF Group subfields can now be saved/loaded individually in Forms Actions UI
* Module: Forms - New Email Action settings: "Content Editor/Raw HTML Editor" switch
* Module: Forms - New Post Action settings: "Post Date/Schedule", "Post Thumbnail" & "Append Post terms"
* Module: Forms - New User Action settings: "Built-in Validation" & "Log user once created"
* Module: Forms - Reworked form arguments for better readability and usability
* Module: Forms - All hooks have been deprecated and renamed. New hooks are simplified and provide more context
* Module: Forms - Added `acfe/modules/forms/top_level` setting to set the "Forms" admin menu as top level
* Module: Performance Mode - Deprecated `acfe_is_single_meta_enabled()` now allow generic call (without id)
* Module: Added safeguard logic to double-check `WP_Post` object in `add_meta_boxes`
* Field: Advanced Link - Fixed URL value in Flexible Content Preview Mode
* Field: Flexible Content - Toggle Layout - Enhanced CSS effect to allow fields edit
* Field: Forms - Field now store forms names as value instead of IDs, for portability
* Field: WYSIWYG - Fixed editor being delayed inside a Flexible Content > Sub Repeater
* Field Settings: Enhanced escaping logic in the "Data" modal
* Field Settings: Enhanced "Permissions" setting to allow new lines
* Field Groups: Local - "Sync Back to DB" feature now correctly ignore Inline Hooks
* Settings UI: Enhanced array settings values render
* General: Fixed ACF postboxes setting icon alignment in WP 6.5
* General: Bump WP version up to 6.5

= 0.8.9.5 =

**ACF Extended Pro 0.8.9.5:**

* Field: Block Editor - Added WP 6.4 compatibility
* Field: Block Editor - Enhanced compatibility with custom blocks
* Field: Block Editor - Fixed Media Upload not working in front-end form
* Field: Block Editor - Fixed shortcodes not applied with `get_field()`
* Field: Block Editor - Fixed Embed Block preview height
* Field: Block Editor - Fixed "Allowed Block Types" setting not working in WP 6.3
* Field: Block Editor - Fixed Inserter Block Preview popup position
* Field: Payment - Enhanced Conditional logic & Tab logic compatibility
* Field: Payment - Fixed PayPal Checkout button being disabled when form has no submit button
* Field: Payment - Added "Paypal Checkout" button text setting
* Field: Payment Cart - Added quotes compatibility in Items name
* Field: Phone Number - Updated Intl Tel Input library and Utilities with latest area codes


**ACF Extended Basic 0.8.9.5:**

* Field: Google reCaptcha v3 - Added token refresh logic
* Module: Developer Mode - Enhanced handle of incomplete class in meta values
* Module: Options UI - Enhanced handle of incomplete class in options values
* Module: Performance Ultra - Fixed slash values in WP Preview & WP Revisions
* General: Bump WP version up to 6.4
* General: Updated french translation
* General: Enhanced internal tools

= 0.8.9.4 =

**ACF Extended Pro 0.8.9.4:**

* Field: Block Editor - Fixed WP 6.3 compatibility
* Field: Block Editor - Enhanced WP 6.0 / 6.1 / 6.2 compatibility
* Field: Block Editor - Fixed duplicated field when using "Duplicate" action with a Flexible Content/Repeater
* Field: Color Picker - Fixed Theme Json Resolver deprecated notice since WP 6.2
* Field: Payment - Fixed PayPal credentials field settings width for better usability
* Field: Phone Number - Updated Libphonenumber PHP Addon to latest 8.13.21 version
* Field: Phone Number - Fixed Libphonenumber PHP Addon deprecated notice
* Module: Performance - Added auto upgrade for `single_meta` into `performance ultra` when using Settings UI
* Core: Fixed PHP 8 deprecated notice on ACF/ACFE Updates admin page

**ACF Extended Basic 0.8.9.4:**

* Module: Form - Fixed PHP 8 deprecated notice when creating a new form
* Module: Form - Fixed escaping in `[acfe_form]` attributes shortcode
* Module: Performance - Removed unnecessary "Save as individual meta" field setting when module is disabled
* Compatibility: ACF 6.2 - Added sidebar column in the new ACF Options Pages UI
* Compatibility: Profile Builder - Fixed PHP notice in settings page
* Compatibility: Query Monitor - Updated internal trigger notice function to correctly register as "Doing it wrong"
* Core: Updated Readme

= ******* =

**ACF Extended Pro *******:**

* Module: Performance - Added "Hybrid" Engine
* Module: Performance - Hybrid Engine divides post meta per 2 while being compatible with search plugins & `WP_Query`
* Module: Scripts - Fixed submit button not being disabled on start
* Module: Template - Template Location can now be used in middle of other locations
* Field: Block Editor - Force array when empty "allow blocks"
* Field: Color Picker - Fixed default black & white color picker on "Palette Display"
* Field: Flexible Content - Grid - Fixed resizable area when column has only "auto" size
* Field: Google Map - Fixed PHP warning when importing field with a "default value"
* Field: Phone Number - Fixed JS initialization in repeater/flexible content on Gutenberg screen
* Field: Post Field - Fixed UI empty space when using Title + Permalink
* Field: Post Field - Fixed "Add Title" placeholder CSS glitch on new post creation when using Title
* Field: Post Field - Added compatibility with Bricks for the Content field
* Global Condition: Removed `acfe_field_group_condition` from fields settings when not needed

**ACF Extended Basic *******:**

* Module: Performance Mode - Added module
* Module: Performance Mode - Renamed "Single Meta" to "Ultra" Engine
* Module: Performance Mode - Added Modes: "Test Drive", "Production" & "Rollback"
* Module: Performance Mode - Added metabox allowing to switch Mode on-demand. Displayed when Developer Mode is enabled
* Module: Performance Mode - Deprecated `acfe/modules/single_meta` setting in favor of `acfe/modules/performance`
* Module: Performance Mode - Deprecated `acfe/modules/single_meta/post_types` hook in favor of `acfe/modules/performance/config`
* Module: Performance Mode - Deprecated `acfe/modules/single_meta/taxonomies` hook in favor of `acfe/modules/performance/config`
* Module: Performance Mode - Deprecated `acfe/modules/single_meta/users` hook in favor of `acfe/modules/performance/config`
* Module: Performance Mode - Deprecated `acfe/modules/single_meta/options` hook in favor of `acfe/modules/performance/config`
* Module: Dev Mode - Fixed Dev Metabox disappearing on Page Template change
* Module: Form - Fixed PHP 8 deprecated notice
* Module: Post Type - Fixed forced 2 minimum `supports` setting
* Field: Advanced Link - Forced empty value when saved as empty
* Field: Flexible Content - Fixed Layouts Categories order to ASC
* Field: Taxonomy Terms - Added REST API return schema array|false|null
* Compatibility: Fixed tooltip instruction placement being wrongly translated
* Compatibility: Added ACFML 2.0.2 compatibility fix with PHP AutoSync and `l10n_textdomain`
* Compatibility: Added ACF 6.1 compatibility fixes

= ******* =

**ACF Extended Pro *******:**

* Module: Dev Mode - Added "Edit" action to edit raw metadata on the fly
* Field: Color Picker - Custom colors are now normalized to enhance compatibility with Iris
* Field: Color Picker - Color gradient is now displayed by default instead of the black & white gradient
* Field: Image - Added "Upload Folder" setting to customize upload path

**ACF Extended Basic *******:**

* Field: Code Editor - Fixed `return_format` throwing an error on newly created/modified Code Editor
* Field: Flexible Content - Dynamic Render - Removed unnecessary HTML comment with layout name
* Field: Flexible Content - Copy/Paste - Enhanced prompt instructions
* General: Updated Translations

= ******* =

**ACF Extended Pro *******:**

* Field: Date Range Picker - Added string value fallback in `format_value()`
* Field: Menu Locations - Added "Name", "Label" & "Both (Array)" Return Format
* Field: Options Pages - Fixed potential warning when "Allowed Options Pages" setting was empty
* Field Group: Global Conditional Logic - Added <, <=, >, >= Field Group Locations Operators
* Module: Rewrite Rules - Enhanced invalid regex rule fallback
* Module: Scripts - Script Launcher - Added `capability` parameter
* Module: Scripts - Script Launcher - Enhanced `executions` argument to allow `false/true/[number]`
* Module: Scripts - Script Launcher - Removed unnecessary `$index` from hooks arguments
* Module: Template - Fixed potential warning when adding a Local Field Group without `location`
* Module: Template - Fixed "After Title" Field Group position which could break saving
* Module: Template - Fixed "Required" setting when using DB Field Groups
* Module: Template - Fixed Clone "Seamless" values prefix
* General: Fixed Currencies returned in Languages queries

**ACF Extended Basic *******:**

* Field: Advanced Link - Formatted value now correctly return an empty value when needed
* Field: Advanced Link - Fixed an issue with custom sub fields when `key` argument was missing
* Field: Code Editor - Added `nl2br()` "Return Format" setting
* Field Groups: Added `acfe/default_field_group` filter to define default Field Group configuration
* Field Groups: ACF 6.0 - Fixed Field Groups tabs CSS when there are many categories
* Field Settings: Bidirectional - Added control to avoid updating values when prevewing changes
* Module: Block Types - Fixed an edge case where an error could be thrown when editing a post
* Module: Post Types - Admin Archive - Added `acfe/validate_post_type_archive` hook
* Module: Post Types - Admin Archive - Deprecated `acfe/post_type_archive_capability` hook
* Module: Post Types - Admin Archive - Added `show_in_menu`  custom page compatibility
* Module: Post Types - Admin Archive - Added Post Type `post` "Edit Archive" adminbar compatibility
* Module: Settings - Fixed warning when using an old ACF version which doesn't handle newer ACF settings
* Modules: Fixed "Enter" keypress breaking the creating of new item
* Modules: Enhanced 0.8.9 module upgrade to use `init` action
* General: Replaced `acf_esc_attrs()` (ACF 5.8.1) usage with `acf_esc_atts()` (ACF 5.8) to meet requirements
* General: Updated Readme

= 0.8.9 =

**ACF Extended Pro 0.8.9:**

* Field: Block Editor - Added field
* Field: Payment - Fixed ACF 6.0.x settings instructions
* Modules: Added PHP/Json AutoSync feature
* Modules: Added "Local" & "Sync Available" views
* Modules: Added DB <> PHP <> Json triangular difference comparison
* Module: Block Types - PHP sync files are saved/loaded from `/my-theme/acfe-php/block-types`
* Module: Block Types - Json sync files are saved/loaded from `/my-theme/acf-json/block-types`
* Module: Options Pages - PHP sync files are saved/loaded from `/my-theme/acfe-php/options-pages`
* Module: Options Pages - Json sync files are saved/loaded from `/my-theme/acf-json/options-pages`
* Module: Post Types - PHP sync files are saved/loaded from `/my-theme/acfe-php/post-types`
* Module: Post Types - Json sync files are saved/loaded from `/my-theme/acf-json/post-types`
* Module: Taxonomies - PHP sync files are saved/loaded from `/my-theme/acfe-php/taxonomies`
* Module: Taxonomies - Json sync files are saved/loaded from `/my-theme/acf-json/taxonomies`
* Module: Templates - PHP sync files are saved/loaded from `/my-theme/acfe-php/templates`
* Module: Templates - Json sync files are saved/loaded from `/my-theme/acf-json/templates`
* Module: Templates - Deprecated `acfe_add_local_template()` in favor of `acfe_register_template()`
* Module: Settings - Fixed `l10n_textdomain` setting as text input

**ACF Extended Basic 0.8.9:**

* Field: Advanced Link - Added value type control during `update_value` to enhance compatibility
* Module: Block Types - Added "Align Text", "Align Content", "Full Height", "Align Matrix" settings
* Module: Enhanced UI - Fixed WP 6.1 Attachment "Alt" field missing
* Module: Post Types - Added "Meta Key" and "Meta Type" orderby settings in Archive and Admin query
* Module: Post Types - Added "Enter Title" setting in Labels
* Module: Post Types - WP Updated Messages now use Labels with Classic Editor
* Module: Taxonomies - Added "Meta Key" and "Meta Type" orderby settings in Single and Admin query
* General: Fixed potential Block Type JS error on layout change
* General: Added ACF 6.0.x Select2 single select CSS fix
* General: Introduced Module v3
* General: Reworked Upgrades logic

= ******** =

**ACF Extended Pro ********:**

* Field: Date/Time Picker - Fixed instructions placement in ACF 6.0.x
* Field: Google Map - Added `is_array()` control in `format_value()`
* Field: Payment - Added initialization on `ready` to avoid too early JS init
* Module: Scripts - Bumped "Clean Orphan Meta" script version to 1.1
* Module: Scripts - Added "Script Launcher" to easily launch scripts using hooks
* Module: Settings - Added `modules/scripts/demo` setting
* General: ACF Updates screen has been uniformized accross 5.10.x to ACF 6.0.x versions

**ACF Extended Basic ********:**

* Field: Advanced Link - Reworked code & unified unformatted value
* Field: Clone - Fixed Seamless Style CSS in `acf-table`
* Field: Columns - Fixed Columns render in Repeater when user sort rows
* Field: Flexible Content - Added check in `acfe/load_fields` to avoid potential PHP warning with third party plugins
* Field: Flexible Content - Fixed "Added Layout" scroll animation
* Field: Flexible Content - Fixed Dynamic Preview Ajax when inside ACF Ajax Screen Check
* Field: Group - Fixed Seamless Style CSS in `acf-table`
* Field: Post Object - Fixed older ACF version < 5.10 Select2 escape
* Field: Tab - Fixed last opened tab preference on page refresh
* Field: Taxonomy Terms - Fixed "Level" ajax setting not working correctly in ACF 6.0.x
* Field: Taxonomy Terms - Enhanced Load Terms/Save Terms settings logic to avoid call on front-end
* Field Group: Fixed Flexible Content "Duplicate Layout" sub fields moved to original layout
* Field Settings: Bidirectional - Fixed potential php warning when deleting related field groups
* Fields: Added `acf_translate()` in `acf/translate_field/type=field_type` for all field types
* Module: Dev Mode - "Bulk actions" is correctly hidden when metabox are cleared
* Module: Enhanced UI - Fixed Tabs "Align Left" placement on User Edit Screen
* Module: Form - Fixed "User Action" uploaded files so it can be used in a later action
* Module: Form - Added "User Action" Target control before processing builtin User Validation
* Module: Form - Added `acfe/form/submit/post_append_terms` filter to change the `wp_set_object_terms` append
* Module: Settings - Added `rest_api_enabled` `rest_api_format` `rest_api_embed_links` `preload_blocks` `enable_shortcode` settings
* General: Fixed various ACF 6.0.x CSS
* General: Added multiple plugin activation warning messages
* General: Added select2 hooks controls to avoid initialize without `field` instance
* General: Introduced `acfe.FieldExtender` and deprecated `acfe.fieldExtend`
* General: Introduced `acfe.Modal` and deprecated `acfe.Popup`
* General: Replaced `_.escape()` with `acf.strEscape()`

= ******** =

**ACF Extended Pro ********:**

* Field: File - Improved default values implementation
* Field: File - Fixed Preview Style "Select2" setting render styling
* Field: WYSIWYG - Fixed "Custom Toolbar" setting not working correctly in ACF 6.0 UI
* Field Settings: Min/Max - Added settings as default values in related fields
* Module: Scripts - Improved validation process to only trigger inside page wrapper
* General: Fixed ACF 6.0 CSS on ACF "Updates" page

**ACF Extended Basic ********:**

* Field: Image/File - Fixed undefined index notice when ACFE Form forced specific uploader type
* Field: Flexible Content - Clear fields storage in Field Group UI to avoid third party plugins messing with it
* Module: Forms - Reverted logic for the Image/File/Gallery media modal
* Field Groups: Fixed Advanced Settings/Validation settings not working correctly in ACF 6.0 UI
* General: Added several ACF 6.0 Field Groups CSS fixes
* General: Hooks - Added `acf_add_filter_variations()` to general hooks
* General: Enhanced `acfe_is_admin_screen()` helper
* General: Enhanced `ftype` js logic to avoid overriding prototype
* General: Enhanced `acfe.fieldExtend` js logic & added dependencies
* General: Enhanced js helpers

= ******* =

**ACF Extended Pro *******:**

* Field: Code Editor - Fixed ACF 6.0 UI field setting width
* Field: Google Map - Fixed potential undefined `zoom` key in some edge case
* Field: Post Object - Fixed an issue with the Inline Post Edit modal which would not work correctly in some edge case
* Field: Relationship - Reworked & Enhanced Post Creation/Edit code logic

**ACF Extended Basic *******:**

* Field: Dynamic Render - Fixed potential undefined `render` key when using the old `acfe_dynamic_message` field type
* Field: Flexible Content - Fixed duplicated Modal Select Categories
* Field: Flexible Content - Fixed Sortable ui helper css margin
* Field: Post Object - Fixed ACF 6.0 UI wrong border color
* Field: Post Object - Fixed sortable items when Inline Post Edit is enabled with "Allow Multiple values"
* Field: Select - Fixed potential JS issue while searching a value when using "Allow custom value"
* Field: Tab - Fixed ACF 6.0 UI missing colors
* Field: Taxonomy - Fixed JS error on initialization
* Module: Advanced Validation/Settings - Fixed ACF 6.0 UI settings css
* Module: Forms - Bail early in the Block Editor in the `[acfe_form]` shortcode to avoid conflict with Media Modal (ACF core logic)
* Module: Dev - Enhanced module logic
* General: Added several ACF 6.0 & ACF 6.0.1 UI compatibility fixes
* General: Added several JS helpers
* General: Enhanced ACFE Modal logic
* General: Enhanced Readme
* General: Enhanced Build Tools
* General: Introduced `acfe.fieldExtend`

= ******* =

**ACF Extended Pro *******:**

* Module: Dev Mode - `post_content` is unserialized in Post Object view when possible
* Module: Dev Mode - Added data overview & clean orphan meta on Attachment
* Module: Global Conditional Logic - Fixed postbox being hidden when user interacted with Screen Options
* Module: Scripts - Fixed Attachments post type in Orphan Meta Cleaner
* Module: Rewrite Rules - Fixed non-registered rewrite tag php notice
* Field: Color Picker - Added `theme.json` compatibility for color palette
* Field: Color Picker - Added Return Format "Label" and "Color + Label Array"
* Field: Countries / Languages / Currencies - Removed potential notice in some edge cases
* Field: Date Range Picker - The `end_date` sub field is now correctly cleared upon save
* Field: Date Range Picker - Displaying only one date when the user select the same start/end date
* Field: File - Added field key to attributes for nonce generation
* Field: Payment - Added "Hide Postal Code" setting
* Field: Payment - Added `acfe/fields/payment/stripe_args` JS hook to change Stripe elements args
* Field: Payment - Fixed Payment Summary Render rounding the total amount
* Field: Payment - Fixed PayPal popup blocked by Safari
* Field: Payment - Fixed PayPal validation when "Display Button" is disabled
* Field: Payment Cart - "## Title" markup is now allowed
* Field: Payment Cart - Field is now compatible with Repeater/Flexible Content
* Field: Payment Cart - Cart items are now automatically passed to js in `acf.data.acfe.carts`
* Field: Phone Number - Added National & International return format when libphonenumber is installed
* Field: Phone Number - Added Geolocation API Token setting for ipinfo.io
* Field: Phone Number - Field value now only save the phone number instead of an array for data accessibility
* Field: Phone Number - Server Validation now also take care of Allowed Countries setting
* Field: Post Field - Fixed Content reinit JS undefined variable
* Field: Post Object - Inline Post Creation/Edit - Added data in iframe URL for customization
* Field: Relationship - Inline Post Creation/Edit - Added data in iframe URL for customization
* Field: Relationship - Added edit button RTL CSS support
* Field: True False - Added "Default Rounded", "Small", "Small Rounded", "Alt" & "Alt Rounded" styles
* Field: WYSIWYG - Removed jQuery dependency on Source Code modal
* Field Group: Added Alternative Add Field Mode with Shift+Click (add by field type)

**ACF Extended Basic *******:**

* Module: Dev Mode - Fixed "Bulk Delete Meta" not working correctly on Post Type List screen
* Module: Dev Mode - Added meta overview on Attachment screen
* Module: Enhanced UI - Added Attachment Enhanced UI
* Module: Enhanced UI - Fixed WordPress 6.0 User Profile PHP notice
* Module: Form - Fixed "Library: Attached to this post" front-end Media Modal
* Module: Form - Enhanced `acfe/form/load` to allow hidding field
* Module: Form - User - Added builtin Insert/Update User Email validation
* Module: Form - Post Action - Fixed post parent setting which could break in some edge cases
* Module: Form - PHP Success hook
* Module: Multilang - Fixed Post Types Archive Page detection when using `get_field()` very early
* Module: Options Pages - Admin List - Added "Position" column
* Module: Options UI - Enhanced search escape
* Module: Options UI - Enhanced specialchars entities & added raw serialized output
* Module: Settings UI - Enhanced registered values column to correctly use `acf/settings` filter
* Field: Advanced Link - Display preview when URL or Title is entered
* Field: Code Editor - Fixed value update when used inside an ACF Block Type
* Field: Code Editor - Fixed missing Field Object Settings in the Field Group UI on duplicate
* Field: Columns - Fixed CSS when inside a collapsed Repeater
* Field: Columns - Added field to GraphQL
* Field: Clone - Fixed Modal Edit title when in repeater table
* Field: Flexible Content - Select Modal - Fixed Layout Category showing in all categories when there is no category set
* Field: Flexible Content - Edit Modal - Fixed usage with `acf/fields/flexible_content/layout_title`
* Field: Group - Fixed Modal Edit title when in repeater table
* Field: Post Statuses - slugs are only shown when duplicated labels
* Field: Select - Enhanced Select2 "on clear" logic by closing selection
* Field: Taxonomy Terms - Enhanced Radio Field Type when using Load Terms with specific allowed terms
* Field Settings: Validation - Added regex unicode compatibility
* Locations: Post Type Archive - Enhanced `have_archive()` to find post type when there is no posts
* Locations: Post Type Archive - Fixed native `post`, `page` & `attachment` post types archive location matching field groups
* General: Compatibility - Enhanced Elementor Dynamic Tags detection
* General: Compatibility - Added ACF 6.0 new UI compatibility fixes
* General: Core - Fixed editable `<code>` tag for facetwp
* General: Core - Enhanced localize data
* General: Core - Enhanced scripts enqueue logic
* General: Core - Updated tooltip icon & added on/off logic on click
* General: Gutenberg - Enhanced metabox CSS
* General: Helpers - Added `acfe_query_fields()` helper
* General: Readme - Enhanced Readme (Plugin URI, typos, Slack URL...)

= ******* =

**ACF Extended Pro *******:**

* Module: Templates - Fixed top-level Seamless Clones values not being correctly loaded
* Module: Templates - Fixed Templates List columns data
* Module: Global Conditional Logic - Fixed Field Group Locations when using a Global Field on the "Add Term" screen
* Module: Global Conditional Logic - Enhanced Field Group Locations to only use matched groups when using the same field on different screens
* Module: Global Conditional Logic - Fixed ACF Ajax Screen rules being incorrectly applied
* Field: Date Range Picker - Fixed "Default Start/End" settings not working correctly
* Field: Date Range Picker - Added "Show Dropdowns" settings
* Field: Date Range Picker - Enhanced dropdowns CSS
* Field: Flexible Content - Grid System - Renamed "Wrap" setting to "No Wrap" for consistency
* Field: Payment - Fixed Conditional Logic not working correctly
* Field: Payment - Fixed "Incorrect Payment Data" in Summary Render when no items saved
* Field: Payment - Enhanced Summary Render & Data validation
* Field: Payment - Fixed recursive "Payment Field Selection" when payment fields are inside sub fields
* Field: Post Field - Taxonomy - Fixed initialization with non-hierarchical taxonomy
* Field: Post Field - Editor - Added delayed re-initialization
* Field: Post Object - Fixed "Allow Creation" setting not triggering Conditional Logic on post creation
* Field: WYSIWYG - Added "Auto Init" setting when using "Delay Init"

**ACF Extended Basic *******:**

* Module: Ajax Authorbox - Fixed disappearing authorbox when ACF Ajax Screen is triggered (category selection etc...)
* Module: Clean Orphan Meta - Enhanced logic for cloned fields with sub fields
* Module: Forms - Enhanced `acfe/form/prepare` arguments when using `return false`
* Module: Developer Mode - Fixed potential PHP notice when using Local Field Groups
* Module: Developer Mode - `ACFE_DEV` & `ACFE_SUPER_DEV` constants are now uppercase
* Module: Options UI - Fixed `orderby` column sanitization
* Module: Single Meta - Fixed Preview Changes not working correctly with Single Meta
* Field: Columns - Fixed CSS when used inside Tabs Aligned Left
* Field: Flexible Content - Fixed "Modal Select Size" being always forced to "Full"
* Field: Flexible Content - Fixed sub fields CSS when displayed inside a "Modal Edit" from the Sidebar
* Field: Flexible Content - Fixed sidebar Gutenberg title CSS glitch
* Field: WYSIWYG - Fixed "Delay init" setting being automatically initialized on page load
* Field Groups: Fixed potential PHP notice when no Field Group are saved in DB in the "Sync Available" Tab
* Locations: Post Type list/Taxonomy list/User list/Attachment list - Fixed Flexible Content field initialization
* Locations: Post Type list/Taxonomy list/User list/Attachment list - Fixed fields CSS padding
* General: Added `acfe_get_field_descendants()` & `acfe_map_any_field()` helpers
* General: Fixed native ACF 5.11 bug with multiple front-end forms which wrongly trigger validation when one field was required
* General: Fixed `acfe/validate_save_post` to correctly work with `acfe_add_validation_error()` helper
* General: Enhanced Select2 CSS integration for ACF 5.10/5.11.3/5.11.4 and YOAST
* General: Enhanced french translation
* General: Code Cleanup & Formatting

= ******* =

**ACF Extended Pro *******:**

* Field: File - Fixed single file not being correctly saved when removed on Gutenberg screen
* Field: Payment - Added PayPal Production & Test API URL in settings instructions
* Field: Payment - Enhanced Stripe/PayPal JS enqueue on Gutenberg screen
* Field: Payment Cart - Fixed multiline "Default Value" setting
* Field: Phone Number - Fixed format value causing problem in some edge case
* Field: Phone Number - Changed default "Return Format" to "Number"
* Field Settings: Instructions More - Fixed `&dash;` in field settings instructions with ACF 5.10
* Module: Dev Mode - Fixed "Object Data" modal on Post Screen when sidebar is fixed
* Module: Forms - Fixed Shortcode Preview issue when the ACFE Form module was disabled
* Module: Orphan Meta Cleaner Script - Added support of multi-sub level clones seamless fields
* General: Removed "No license key has been provided" annoying message on Updates Screen

**ACF Extended Basic *******:**

* Field: Flexible Content - Fixed WYSIWYG copy/paste layout feature with ACF 5.10
* Fields: Fixed ACFE Modal position on Gutenberg screen
* Module: Dev Mode - Added support of multi-sub level clones seamless fields
* Module: Enhanced UI - Added LearnDash Taxonomies compatibility
* Module: Forms - Added `acfe_form_format_value()` back-compatibility with 3rd argument
* Module: Forms - Enhanced "Save ACF Fields" instructions text
* Module: Forms - Added "Post Excerpt" in the "Post Action" fields
* Module: Settings UI - Fixed tabs badge count with ACF 5.10
* General: Enhanced `acfe_is_json()` helper
* General: Added `acfe_is_block_editor()` helper
* General: Fixed typo in changelog
* General: Enhanced french translation
* General: Enhanced readme

= ******* =

**ACF Extended Pro *******:**

* Field: Added "Payment" Field - Compatible with Stripe & PayPal Express gateways
* Field: Added "Payment Selector" Field - Select Payment Field gateway. Supports Radio/Select field type and icons
* Field: Added "Payment Cart" Field - Easily setup a cart selector with items and price
* Module: Added "Scripts UI" - Run scripts on thousands Posts/Terms/Users/<USER>
* Module: Added "Rewrite Rules UI" - Overview, debug and test permalinks rules
* Module: Scripts UI - Added "Orphan Meta Cleaner" builtin script to clean orphan meta from any objects
* Module: Scripts UI - Added "Single Meta Converter" builtin script to convert any object meta to Single Meta
* Module: Scripts UI - Added "Count Posts", "Export Posts" & "Import Posts" example scripts for developers
* Module: Developer Mode - Reworked and enhanced the module logic
* Module: Developer Mode - Enhanced & moved "Object Overview" Metabox into the respective object submit box
* Module: Developer Mode - Added "Meta Count", "Meta Clean" & "Single Meta Status" infos on all objects
* Module: Developer Mode - Added module on "Attachment List", "User List" and "WP Settings" locations
* Module: Developer Mode - Fixed potential warning if the ACF Options Page `menu_slug` wasn't found
* Module: Force Sync - Added "Deleted File Sync" setting `acfe/modules/force_sync/delete` to automatically sync deleted json files to DB
* Module: Forms - Added `[acfe_form]` shortcode preview mode setting `acfe/modules/forms/shortcode_preview` to render the Form Preview in the WP Editor
* Field: Color Picker - Added ACF 5.10 "Enable Transparency" compatibility
* Field: Countries - Added missing conditional logic compatibility
* Field: Currencies - Added missing conditional logic compatibility
* Field: Date Range Picker - Enhanced field to allow users to enter dates manually in the text input
* Field: Date Range Picker - Fixed virtual keyboard on mobile device when user click on the input
* Field: Date Range Picker - Enhanced Load/Save/Format/Delete Sub Fields logic
* Field: File Upload - Added Repeater upload compatibility when "Multiple File Upload" setting is disabled
* Field: Flexible Content Layouts Locations Rules - Fixed "Minimum Layout" setting being incorrectly triggered on unmatched screen
* Field: Flexible Content Layouts Locations Rules - Fixed various problems when used within the Templates module
* Field: Flexible Content Layouts Grid System - Fixed placeholder height CSS when using "Stretch Size" setting
* Field: Image Selector - Fixed PHP notice when creating the field in the Field Group admin UI
* Field: Image Selector - Reworked and fixed "Return Format" setting to use new formats: "Value", "Array" or "Image"
* Field: Languages - Added missing conditional logic compatibility
* Field: Options Pages - Fixed possible notice when no Options Pages are registered
* Field: Phone Number - Fixed value reset to "0" when empty and using Single Meta
* Field: Phone Number - Fixed input padding calculation when using "Separated Dial Code" setting with Gutenberg
* Field: Users - Added "Min/Max" restriction setting compatibility
* Locations: Added "Attachment List" Field Group Location
* Locations: Added "User List" Field Group Location
* General: Added WP auto-update feature compatibility
* General: Fixed PHP warning on "Update Core" screen when an update is available

**ACF Extended Basic *******:**

* Field: Button - Added JS ajax data filter `acfe/fields/button/data` and added all form fields in the post dataset
* Field: Code Editor - Added "Json" Mode
* Field: Code Editor - Added "Return HTML Entities" setting to render entities instead of HTML
* Field: Code Editor - Fixed duplicated textarea when the field was duplicated within a Repeater or a Flexible Content
* Field: Columns - Fixed CSS when columns when used in Tabs
* Field: Dynamic Message - Renamed field to "Dynamic Render"
* Field: File - Added `acfe/upload_dir` and `acfe/upload_file` hooks (+ variations) to allow developers easily change upload folder and file name during an upload
* Field: Flexible Content - Fixed "Toggle Layout" feature which rendered duplicated layouts on the front-end when a layout was toggled off
* Field: Flexible Content - Fixed "Layouts State: Always Opened" not being correctly applied to previously closed layout
* Field: Flexible Content - Fixed "Modal Edit Title" not being correctly displayed when not using the "Inline Title Edit" setting
* Field: Flexible Content - Added links to documentation in settings instructions
* Field: Image - Fixed "Set as Featured Image" not working correctly in the WP "Post Preview" mode when the post wasn't saved
* Field: Select2 - Enhanced placeholder CSS
* Field: Taxonomy Terms - Added `acfe/fields/taxonomy_terms/result` filters to change the Term Name result
* Field: Taxonomy Terms - Fixed potential warning when using non-existent taxonomies as setting (after an import for example)
* Field: Taxonomy Terms - Fixed potential notice when trying to update the field incorrectly with a Term Name instead of the ID
* Field: WYSIWYG Editor - Fixed dropdown placement when user scroll inside an ACFE modal
* Module: Ajax Author Box - Reworked module code base to enhance compatibility
* Module: Ajax Author Box - Disabled the module on Block Editor screen
* Module: Block Types UI - Fixed potential PHP notice when using a Clone field with non-existent cloned fields
* Module: Developer Mode - Added meta overview on "Post Type List" and "Taxonomy List" locations
* Module: Enhanced UI - Fixed Rankmath metabox compatibility on Terms Edit screen
* Module: Enhanced UI - Enhanced various metaboxes CSS
* Module: Forms - Enhanced `[acfe_form]` shortcode to allow users to override any form setting
* Module: Forms - Added `[acfe_form]` shortcode placeholder in the WP Editor
* Module: Forms - Added links to documentation and removed code examples from the UI
* Module: Forms - Fixed "Log User" Action not displaying error messages when the user/pass were incorrect
* Module: Forms - Enhanced general pre-save logic to avoid side effects
* Module: Forms - Enhanced old usage of `{query_var}` Template Tag detection when trying to retrieve an action output
* Module: Forms - Added "#Generated ID" value for the "Post Title" of the "Post Action"
* Module: Forms - Enhanced `acfe_add_validation_error()` to use form fields in priority when using a field name selector
* Module: Forms - Added Repeater format value when using `{field}` & `{fields}` Template Tags
* Module: Options UI - Reworked code base
* Module: Single Meta - Enhanced meta compilation logic for even better performance
* Module: Single Meta - Enhanced revision meta compilation
* Module: Single Meta - Enhanced "Clean orphan Meta" logic
* Module: Single Meta - Enhanced "Clean orphan Meta" metabox. Now only available when the "Developer Mode" module is enabled
* Module: Single Meta - Meta conversion to "Single Meta" no more delete native ACF meta in the process. Users have to click on "Clean Orphan Meta" action manually
* Module: Single Meta - Added "User Object" type compatibility
* Module: Single Meta - Added `acfe/modules/single_meta/users` hook to filter specific user roles
* Fields: Bidirectional Setting - Field Groups Json/PHP files are now automatically synced when a new bidirectional is setup
* Locations: Taxonomy List - Fixed "Side" Field Group position
* General: ACFE Modal - Enhanced "Field Label" in the "Modal Title" to avoid using third party dev code
* General: ACFE Modal - Added an easy way for developers to trigger a custom ACFE Modal
* General: Added plugin localization compatibility with pot/mo files
* General: Added french translation
* General: Updated Readme and Tested Up To version

= ******* =

**ACF Extended Pro *******:**

* Field: Flexible Content - Added "Layouts Locations Rules" setting
* Field: Flexible Content Grid System - Fixed undefined index in `get_flexible_grid()` when using "Container Size" setting
* Field: Flexible Content Grid System - Added `has_flexible_grid()` helper
* Field: Flexible Content Grid System - `has_flexible_grid()`, `get_flexible_grid()` & `get_flexible_grid_class()` now accept a post id as 2nd parameter
* Field: Checkbox/Radio - Added Dynamic Render Choice hooks
* Field: Added Phone Number field
* Field: Added Countries Selector field
* Field: Added Languages Selector field
* Field: Added Currencies Selector field
* Field: Added Post Formats Selector field
* Field: Color Picker - Added selected palette border color compatibility for gradients
* Field: Columns - Added "Fill" size and swapped the setting with "Auto" size. The "Auto" size will now fit the field size
* Field: Select - Added "Prepend" & "Append" settings
* Field: Image Selector - Choices are now compatible with `value : image` logic, allowing developers to save a custom value instead of Image ID/URL
* Field: Image Selector - Removed gallery setting selector due to unnecessary complexity. Choices are now all merged into one single setting
* Field: Relationship - Enhanced Gutenberg post update detection for the Inline Add/Edit Post settings
* Field: Color Picker - Fixed RGBA library not being correctly initialized in the Gutenberg Editor
* Field: Date Range Picker - Fixed the JS not being correctly initialized in the Gutenberg Editor
* Field: Date Range Picker - Enhanced CSS Style of invalid dates to match the ACF Date Picker style
* Field: WYSIWYG - Fixed potential PHP warning when using an un-existent toolbar
* Module: Force Sync - Fixed json file timestamp update during the sync
* Field Settings: Required message - It is now recommended to use `{label}` to display the field label instead of the previous `%s`, for consistency (`%s` will still work)

**ACF Extended Basic *******:**

* Field: Advanced Link - Fixed typo in the field settings
* Field: Columns - Fixed Field Group Seamless Style columns render
* Field: Enhanced UI - Fixed "Add New Term" button on Taxonomy List when using Tabs
* Field: Flexible Content - Fixed "Inline Title Edit" setting generating a duplicated layout title when using "Modal Edit" setting
* Field: Flexible Content - Enhanced "Disable Legacy Ajax Title" & "Async Layouts" settings UI
* Field: Flexible Content - Added missing "Hide Collapse" action setting
* Field: Flexible Content - Fixed "Locked" layout handle cursor CSS
* Field: Forms - Added missing "Search placeholder" setting when using "Select" Field Type
* Field: ReCaptcha - Value are now updated silently to avoid triggering the `acf_changed` popup logic
* Field: Select2 - Enhanced various CSS Style
* Field: Taxonomy Terms - Added missing "Search placeholder" setting when using "Select" Field Type
* Field: Text - Fixed input wrap overflow CSS (while waiting for ACF to fix it)
* Fields: Fixed "Placeholder" & "Search placeholder" conditional logic when using "Select" field type
* Module: Forms - Removed reCaptcha field type from `{fields}` Template Tag render
* Module: Forms - Enhanced field mapping hook with the form context
* Module: Options Pages - Fixed PHP notice when using a Sub Options Page with the latest ACF Pro 5.9.6 update
* Module: Settings UI - Fixed "Modification in code" not being correctly rendered
* Field Groups: Hide On Screen - Fixed Classic Content Editor appearing when should be hidden, while using a second Field Group Hide on Screen and selecting a category
* Compatibility: GraphQL: Fixed typo in Taxonomy Terms field registration
* Compatibility: GraphQL: Added basic field values resolvers (while waiting for official third party implementation documentation)
* General: Added `acfe_is_gutenberg()` helper
* General: Enhanced Readme

= ******* =

**ACF Extended Pro *******:**

* Field: Added "Image Selector" field
* Field: Added "Image Sizes Selector" field
* Field: Column - Added "Column Auto" size setting
* Field: Column - Added "Column Border" & "Fields Border" settings
* Field: Color Picker - Fixed CSS Position of the Color Picker in palette mode
* Module: Global Conditional Logic - Fixed multiple Conditional Groups not being correctly detected
* Module: Templates - Enhanced Templates Values detection
* Module: Templates - Added `acf/init` hook in the PHP Export code
* Module: Templates - Added Template Detection on Term & Post Edit screens
* Module: Templates - Fixed potential PHP notice in the Template UI sidebar when using a custom location
* Fields: Added missing ACF Conditional Logic rules on Pro Fields (Block Types, Color Picker, Field Groups etc...)

**ACF Extended Basic *******:**

* Field: Column - Upgraded CSS to use Flexbox
* Module: Forms - Fixed Honeypot Field not being correctly rendered
* Module: Forms - Fixed potential slashes on the Success Page when using Single Meta Save
* Module: Forms - Fixed potential slashes in e-mail content & fields
* Module: Forms - Added context & variations to the `acfe/form/render` hooks
* Module: Multilang - Enhanced WPML String Translation Registration for all modules
* Module: Dev Mode - The module now check the `acf_current_user_can_admin()` function
* Module: Dev Mode - Tweaked CSS margin of the Bulk Action select
* Module: Single Meta Save - Disabled "Save as individual meta" on Column, Google reCaptcha & Dynamic Message fields
* Module: Single Meta Save - Fixed WP Revisions Comparison compatibility
* Module: Single Meta Save - Fixed slashes in WP Revisions
* Modules: Fixed Draft Post Status when an item is reverted from Trash
* General: Local Meta - Enhanced preload Post ID logic

= 0.8.8.2 =

**ACF Extended Pro 0.8.8.2:**

* Field: Added Advanced "Color Picker" field settings with RGBA support, Palette display style, Custom predefined colors & Allow null
* Field: Added "Date Range Picker" field with Custom ranges, No weekends, Min/max date & Min/max days support
* Field: Flexible Content Grid System - Fixed sub Flexible Content Grid CSS bug
* Field: Flexible Content Grid System - Tweaked `get_flexible_grid_class()` prefix
* Field: Fields Selector - Added "Field Name" return value setting
* Field: Post Field - Fixed Permalink "Save" & "Cancel" missing text when editing a permalink
* Module: Added "Force Sync" module allowing to automatically sync Json Files to DB with the newest version
* Module: Dynamic Template - Improved instruction
* Module: Global Conditional Logic - Fixed compatibility with Terms & Users screen when Enhanced UI is disabled

**ACF Extended Basic 0.8.8.2:**

* Field: Clone - Fixed internal ACFE module field groups which where selectable in clone
* Field: Google reCaptcha - Renamed the field to Google reCaptcha
* Field: Post Object - Fixed duplicated post creation when using "Custom Value" setting
* Module: Forms - Fixed Clone Render when using "Override Form Render" settings
* Module: Forms - Fixed "Redirect Action" named hook not working with a custom action name
* Module: Forms - Added render actions hooks
* Module: Forms - Fixed `acfe_import_form()` function
* Module: Block Types - Changed the "Mode" setting default value to "Preview", as in the documentation
* Module: Multilang - Options Post ID `options` can now be excluded from the module translation
* Module: Multilang - Added `acfe/modules/multilang/exclude_options` filter to exclude specific Options Post ID from module translation
* Module: Multilang - Added `acfe/modules/multilang/include_options` filter to include specific Options Post ID from module translation
* Module: Multilang - Deprecated the `acfe/modules/multilang/options` filter
* Module: Multilang - WPML string translations now use `wpml_translate_single_string` instead of `__()`
* Module: Settings UI - Fixed potential duplicated table `thead` columns
* Module: Enhanced UI - Fixed possible metaboxes screen name collision with taxonomy name
* General: ACFE Modal - Fixed `show_field` on modal open
* General: ACFE Modal - Fixed possible duplicate field instructions in repeaters
* General: The `acfe_get_post_id()` helper now correctly retrieve the Post ID in ACF Block Types
* General: Fixed `get_fields()` calls in `acfe/save` hooks when a bidirectional value is set
* General: Enhanced Local Meta logic
* General: Enhanced `acfe/save` & `acfe/validate_save` hooks logic
* General: Code format cleanup
* Compatibility: Added ACFE Field Types to WP GraphQL ACF plugin

= 0.8.8.1 =

**ACF Extended Pro 0.8.8.1:**

* Module: Added "Screen Layouts" module allowing to customize Post Edit Screen up to 3 columns
* Field: Flexible Content - Added "Container Size" setting in the `get_flexible_grid()` helper
* Field: "Menus" & "Menu Locations" - Added compatibility with min/max items settings
* Field: Added "ACFE Template" Selector field
* Field: Added "ACF Block Types" Selector field
* Field: Added "ACF Field Groups" Selector field
* Field: Added "ACF Field Types" Selector field
* Field: Added "ACF Fields" Selector field
* Field: Added "ACF Options Pages" Selector field
* Field: Post Field - Fixed field type category translation

**ACF Extended Basic 0.8.8.1:**

* Field: reCaptcha - Fixed missing field Site key & Secret Key on field render
* Fields: Fields types are now sorted in ASC order in the Field Group UI
* Fields: Added "ACF" & "WordPress" Field Types Categories in the Field Group UI to de-clutter the "Relational" category
* Module: Forms - Fixed empty `get_field()` used inside an action when a previous action saved additional meta
* Module: Forms - Fixed Form Name not being correctly updated when changed
* Locations: Post Type Archive - Added `acfe/post_type_archive_capability` filter to change Archive Page admin menu capability
* Locations: Post Type Archive - Admin menu item on front-end now correctly check the user permissions
* Field Groups: Permissions - Fixed undefined index notice when using the permission setting
* General: Fixed "ACF Title" metabox position to become usable with drag&drop function when empty (while waiting for ACF to fix it)

= 0.8.8 =

**ACF Extended Pro 0.8.8:**

* Field: Flexible Content - Added Grid System setting, `get_flexible_grid()` & `get_flexible_grid_class()` helpers
* Field: Added Menus Selector
* Field: Added Menu Locations Selector
* Field: File - Added "Button Label" setting to customize the "Add File" text
* Field: File Multiupload - The field now always return an array when multiupload is enabled
* Field: File Multiupload - Fixed missing ID format value
* Field: Post Field - Added "Taxonomies" field type allowing to move Taxonomies metaboxes
* Field: Post Field - Fixed "Content" field type width when used inside a modal
* Field: Relationship/Post Object - Fixed Inline Post Creation/Edit which wasn't working correctly with Gutenberg
* Field: Relationship/Post Object - Fixed Inline Post Creation/Edit which wasn't working correctly with Attachment post type
* Field: WYSIWYG - Fixed source code textarea border flickering during modal initialization
* Field Groups: Added Menu Item Depth Location
* Field Groups: Added Menu Item Type Location
* Module: Settings - Enhanced UI, description, tabs. Settings now display default and registered values difference
* Module: Settings - Added Enable/Disable features allowing to switch modules directly from the UI
* Module: Settings - Added Export (PHP & Json) / Import tools
* Module: Developer Mode - Added Post / Term / User / Options Page Object Data overview
* Module: Dynamic Block Types - Added "Active" setting allowing to enable/disable one specific block type
* Module: Dynamic Forms - Added "Active" setting allowing to enable/disable one specific form
* Module: Dynamic Options Pages - Added "Active" setting allowing to enable/disable one specific options page
* Module: Dynamic Post Types - Added "Active" setting allowing to enable/disable one specific post type
* Module: Dynamic Taxonomies - Added "Active" setting allowing to enable/disable one specific taxonomy
* Module: Dynamic Templates - Added "Active" setting allowing to enable/disable one specific template
* Module: Dynamic Templates - Fixed required fields which weren't always disabled in the template view
* Module: Dynamic Templates - Fixed a bug with Flexible Content Preview when preloading values
* Module: Dynamic Templates - Removed Field Group Hide on Screen effects in the Template UI
* Module: Dynamic Templates - Added Export (PHP & Json) / Import tools
* Module: Dynamic Templates - Added `acfe_add_local_template()` function to locally register template values
* Module: Dynamic Forms - Option Action - Fixed code example labels
* Module: Global Field Condition - Fixed duplicated operators

**ACF Extended Basic 0.8.8:**

* Field: Flexible Content - Added Modal Select size setting
* Field: Flexible Content - Added Modal Edit size setting
* Field: Flexible Content - Added Modal Edit size setting for each layouts
* Field: Flexible Content - Reworked code base & Enhanced Field Settings UI
* Field: Flexible Content - Fixed placeholder height when the layout is toggled and the preview is reloaded
* Field: Flexible Content - Added modal Select/Edit CSS classes with field name, key & layout name for developers
* Field: Advanced Link - Fixed error if the field was using a Term which has been deleted
* Field: Button - Added name field attribute to be able to retrieve the button click during `acf/save_post`
* Field: Checkbox/Radio - Fixed `## Title` Group Options not working on the front-end
* Field: Code Editor - Fixed CSS `break-work` property when used in repeater
* Field: Columns - Re-introduced the field in Terms/Users views when Enhanced UI module is enabled
* Field: Conditional Logic - Fixed "contains" operator which would not work properly on Forms/Post Statuses/Post Types/Taxonomies/Taxonomy Terms/User Roles fields when using Checkbox/Radio field type
* Field: Dynamic Message - Added `render` field setting to be used as a callback to write content in PHP
* Field: Forms/Post Statuses/Post Types/Taxonomies/Taxonomy Terms/User Roles - Fixed Multiple Lines "Default Value" setting now working correctly
* Field: Group/Clone - Fixed modal edit which included the instruction in the modal title when using the Modal Edit setting
* Field: Image/File - Added file upload validation when using Basic Upload (temporary fix while waiting for ACF to fix officially)
* Field: Image/File - Added "Default" option in the "Uploader Type" setting to fallback to the native behavior
* Field: Image - Fixed "use as Featured Image" which was wrongly saved during page preview
* Field: Select2 - Added dropdown CSS classes with field name & key for developers
* Module: Enhanced UI - Fixed WPMU missing "Add User" button in "Add User" view
* Module: Enhanced UI - Fixed Woocommerce Product Category CSS when using Enhanced UI
* Module: Renamed `acfe/modules/dynamic_block_types` setting to `acfe/modules/block_types`
* Module: Renamed `acfe/modules/dynamic_forms` setting to `acfe/modules/forms`
* Module: Renamed `acfe/modules/dynamic_options_pages` setting to `acfe/modules/options_pages`
* Module: Renamed `acfe/modules/dynamic_post_types` setting to `acfe/modules/post_types`
* Module: Renamed `acfe/modules/dynamic_taxonomies` setting to `acfe/modules/taxonomies`
* Module: Dynamic Block Types/Forms/Options Pages/Post Types/Taxonomies - Enhanced code base & UI
* Module: Dynamic Block Types/Forms/Options Pages/Post Types/Taxonomies - Added Export Json / PHP in the row action
* Module: Dynamic Block Types/Forms/Options Pages/Post Types/Taxonomies - Added Export Json / PHP bulk actions
* Module: Dynamic Block Types/Forms/Options Pages/Post Types/Taxonomies - Fixed possible desync when switching to draft post status
* Module: Dynamic Post Types/Taxonomies - Added "View" in the row action
* Module: Dynamic Post Types/Taxonomies/WP Options/Dev Mode now correctly use the ACF `show_admin` setting
* Module: Dynamic Forms - Fixed Clone Fields filter when using Custom HTML Render
* Module: Dynamic Forms - Deprecated `{current:post|term|user|author}` Template Tags for a more simple version `{post|term|user|author}`
* Module: Dynamic Forms - Added compatibility with Flexible Content Preview Mode & Gutenberg/ACF Block Type Preview mode
* Module: Dynamic Forms - Added a 2nd parameter to `acfe_form_get_action()` to directly retrieve the key value
* Module: Dynamic Forms - Fixed `acfe_add_validation_error()` which wasn't working correctly with field names in the Action Validation Hooks
* Module: Dynamic Forms - User Login Action - Added `acfe/form/validation/user/login_errors` filter to change the default error messages
* Module: Dynamic Forms - Fixed `acfe_import_dynamic_form()` function to programmatically import a form
* Module: Settings - Enhanced UI and updated the module list
* General: Helpers - Deprecated `acfe_form_is_front()` & `acfe_form_is_admin()` in favor of `acfe_is_front()` & `acfe_is_admin()`
* General: Helpers - Added `acfe_get_post_id()` function as a universal solution to always retrieve the correct ACF Post ID in front-end and back-end
* General: Hooks - Added `acfe/save_post` hook and variations on page submission. Compatible with `get_field()`, `have_rows()` etc...
* General: Hooks - Added `acfe/validate_save_post` hook and variations to validate the whole page. Compatible with `get_field()`, `have_rows()` etc...
* General: Hooks - Deprecated `acfe/load_field_front`. `acfe/load_field` should be used with `acfe_is_front()` instead
* General: Hooks - Deprecated `acfe/load_field_admin`. `acfe/load_field` should be used with `acfe_is_admin()` instead
* General: Modal - Fixed CSS `z-index` overlay when using "Edit in Modal" feature inside a WordPress Widget
* General: Updated `acfe` option data structure
* General: Compatibility - Fixed Elementor which list all private ACF Extended Field Groups in the "Dynamic ACF tags" dropdown
* General: Compatibility - Fixed Error in Field Groups UI when trying to use ACF Extended with ACF Free
* General: Compatibility - Fixed Gutenberg "seamless" class on "Seamless" Field Group style to match WP/ACF style
* General: Compatibility - Fixed PHP 8 deprecated notices
* General: Compatibility - Fixed YOAST Rewrite & Republish feature when using the Authorbox UI

= ******* =

**ACF Extended Pro *******:**

* Field Groups: WP Settings Locations - Added new "General Settings", "Writing", "Reading", "Discussion", "Media" & "Permalinks" locations
* Module: Global Field Condition - Fixed compatibility with required fields
* Module: Dynamic Templates - Added Polylang/WPML compatibility
* Module: Dynamic Templates - Improved code logic & compatibility with Clone fields
* Module: Dynamic Forms - Added "Options Page Action". Allowing to Save & Load meta to ACF Options Page
* Module: Dynamic Forms - Fixed "Email Action" to correctly use Multiple Upload files
* Module: Dynamic Forms - Fixed Multiple Upload files on front-end for not logged users
* Module: Dynamic Forms - Fixed Multiple Upload & Dropzone in Basic mode on Firefox
* Field: Relationship/Post Object - Fixed Inline Post Creation when only one post type was allowed in the field setting

**ACF Extended Basic *******:**

* Module: Enhanced UI - Improved logic, compatibility and style. ACF Field Groups can now use any position and any style in Users & Terms views
* Module: Enhanced UI - Fixed Terms WPML widget compatibility
* Module: Dynamic Post Types - "Archive Page" submenu is now correctly translated
* Module: Dynamic Forms - Fixed values loaded from Clones Fields in Seamless display
* Module: Dynamic Forms - Enhanced Image/File format value when using the `{field:my_file}` Template Tag
* Module: Multilang - Added "Current Language" text widget in Options Page submit box
* Module: Multilang - Polylang - Added fallback to "Default Language" in Options Page values if a translated option was never saved before
* Module: Multilang - Polylang - Fixed "Dynamic Forms" & "Dynamic Template" post types detection
* Module: PHP AutoSync - Fixed Local Field Group detection when using a custom submenu on the ACF Field Group menu
* Core: Added default `acf.data.acfe` in core JS
* Core: Fixed ACF Pro 5.8 JS compatibility
* Core: Fixed Google Map suggestions z-index CSS in modals

= ******* =

**ACF Extended Pro *******:**

* Module: Added Enhanced Field Group UI module.
* Module: Added Classic Editor module. Disabled by default, can be enabled using `acf_update_setting('acfe/modules/classic_editor', true)`
* Fields: Added "Instruction Placement" override setting. This will allow to set specific instruction placement for any field. The Advanced Field Group Setting must be enabled
* Fields: Added "Instructions Read More" setting allowing to add collapsed instructions. You may use the `---` or `---Learn more---` template tags to split the instruction
* Fields: Added Quick Visibility Settings widget allowing hide the field, label, instructions or required settings based on the screen: Everywhere, administration or front-end. The Advanced Field Group Setting must be enabled
* Fields: Added inline hooks callbacks for the `acf_add_local_field_group()` function. Allowing to hook in `load_field`, `prepare_field`, `render_field`, `load_value`, `update_value`, `format_value`, `validate_value`, `delete_value`
* Module: Global Fields Condition can now be used as a specific field conditional logic
* Field Groups: Added new "Hide on Screen" settings: "Hide Title", "Hide save draft", "Hide preview", "Hide post status", "Hide visibility", 'Hide publish date", "Hide move to trash", "Hide publish", "Hide minor publishing actions", "Hide misc publishing actions" & "Hide major publishing actions"
* Field: File - Added "Allow Multiple Upload", "Preview Style", "File Count", "Min/max allowed files", "Custom Upload Folder", "Stylised Button" (for Browser uploader) and built-in dropzone (for Browser uploader)
* Field: WYSIWYG - Added "Disable Native WP Style", "Custom Stylesheet Enqueue", "AutoResize", "Max Height", "Codemirror as Source Editor via the `source_code` button" & "Valid Elements" settings
* Field: WYSIWYG - Added a new "Basic Enhanced" Toolbar which automatically includes the new `source_code` (Code Mirror source editor) and `wp_add_media` buttons
* Field: WYSIWYG - Fixed the "Path" render when using the "Transparent Background" setting
* Field: Post Field - Added "Instructions" setting
* Field: Tab - Added "No Preference Save" setting to not save latest opened tab when refreshing the page
* Field: Datepicker: Added "Placeholder", "Min/max Date" & "No Weekends" settings
* Field: Datetime picker: Added "Placeholder", "Min/max Date", "Min/max Time", "Min/max H:i:s" & "No Weekends" settings
* Field: Time picker: Added "Placeholder", "Min/max Time" & "Min/max H:i:s" settings
* Core: Added the `ACFE_PRO_KEY` constant allowing to set the licence key in PHP

**ACF Extended Basic *******:**

* Field Groups: Instruction Placement - Added "Above Fields" setting
* Field Groups: Instruction Placement - "Tooltip" instructions can now be clicked on mobile device
* Field Groups: Hide on screen - All field groups "Hide on screen" settings are now merged, instead of using only the first field group setting
* Field Groups: Advanced Settings - Enhanced code logic
* Field Groups: Permissions setting is now displayed when the Advanced Field Group setting is enabled
* Field: Select - Removed the `- -` characters in the placeholder
* Field: Taxonomy Terms - Fixed "Load Terms" setting when using the radio field type
* Field: Flexible Content - Fixed & enhanced the "Minimum" setting badge duplication when using the "Modal Select" setting
* Field: Flexible Content - Fixed `$is_preview` hard reset in the `get_flexible()` function. This will allow to use `get_flexible()` inside an another `get_flexible()` and correctly get the `$is_preview` variable
* Field: Flexible Content - Fixed a potential PHP notice when adding a new Flexible Content
* Field: Hidden Input - Value can now be used as conditional setting
* Field: Datetime Picker - Value can now be used as conditional setting
* Field: Time Picker - Value can now be used as conditional setting
* Field: File - Renamed the `acfe_uploader` setting back to the native `uploader` name
* Field: Image - Renamed the `acfe_uploader` setting back to the native `uploader` name
* Fields: Permissions settings are now displayed when the Advanced Field Group setting is enabled
* Fields: Enhanced Advanced Settings & Advanced Validation UI
* Module: Dev Mode - Fixed deprecated `acf_get_term_post_id()` usage
* Module: Enhanced UI - Enhanced the responsive CSS
* Module: Enhanced UI - Fixed WPMU styles
* Module: Enhanced UI - Enhanced User Profiles compatibility with Woocommerce plugin
* Module: Enhanced UI - Enhanced Terms Views compatibility with Woocommerce, Yoast, WPML & User Role Editor plugins
* Module: Dynamic Forms - Added the new "Redirect Action"
* Module: Dynamic Forms - Added the new `acfe_form_get_action('post')` function to retrieve the previous Action output within an Action
* Module: Dynamic Forms - Deprecated the "Redirection" setting. The new "Redirect Action" should be used instead
* Module: Dynamic Forms - Deprecated the `{query_var:my-post}` Template Tag to retrieve previous Action output. The new `{action:post}` Template Tag should be used instead
* Module: Dynamic Forms - Deprecated the `{current:form:key}` Template Tag. The new `{form:key}` Template Tag should be used instead
* Module: Dynamic Forms - Deprecated the `acfe/form/query_var/email` hook & variations. The new `acfe/form/output/email` hook should be used instead
* Module: Dynamic Forms - Deprecated the `acfe/form/query_var/post` hook & variations. The new `acfe/form/output/post` hook should be used instead
* Module: Dynamic Forms - Deprecated the `acfe/form/query_var/term` hook & variations. The new `acfe/form/output/term` hook should be used instead
* Module: Dynamic Forms - Deprecated the `acfe/form/query_var/user` hook & variations. The new `acfe/form/output/user` hook should be used instead
* Module: Dynamic Forms - Deprecated the `acfe_form_is_submitted()` function. The new `acfe_is_form_success()` function should be used instead
* Module: Dynamic Forms - Enhanced Actions "Code Examples"
* Module: Dynamic Forms - Updated "Cheatsheet" tab
* Module: Dynamic Forms - Multiple clicks on the submit button are now prevented (Also apply to the native ACF Forms)
* Module: Dynamic Forms - Applying Date, Date time & time pickers fields input class settings
* Module: Dynamic Forms - Removed a potential "Draft Post" if the "Post Action" was cancelled during the `acfe/form/submit/post_args` hook
* Module: Dynamic Block Types - Added "Anchor" supports setting
* Module: Dynamic Post Types - "Menu position" setting now correctly use `null` as default value (as in the `register_post_type()` documentation)
* Module: Single Meta Save - Fixed slashed values after saving a menu in the WP Nav Menu Items screen
* Module: Single Meta Save - Fixed typo in instruction
* Tools: Fixed the empty message when clicking "Import" without selecting a field group file
* General: Fixed ACF Updates box CSS
* General: Updated the donors list
* General: Enhanced assets build tools, now using Gulp, Postcss, Autoprefixer & Minifier
* Core: Enhanced JS code base
* Core: Enhanced Enqueue

= 0.8.7.4 =

**ACF Extended Pro 0.8.7.4:**

* Module: Added "Dynamic Templates" module allowing to manage advanced field groups default values based on post type, taxonomy, user etc...
* Field: Google Map - Added "Preview", "Height", "Zoom", "Min/Max Zoom", "Marker Image", "Map Type", "Hide UI", "Hide Zoom", "Hide Map Type", "Hide Fullscreen", "Hide Street view", "Map Style", "API Key" settings
* Field: WYSIWYG - Added "Height", "Remove path", "Disable resize", "Menu bar", "Transparent editor", "Merge Toolbars", "Customize Toolbars" settings
* Field: Post Object - Added "Inline Post Edit" & "Inline Post Creation" settings
* Field: Relationship - Added "Inline Post Edit" & "Inline Post Creation" settings
* Field: Post Field - Added field, allowing to move native WP Post fields: Attributes, Author, Comments, Content, Date, Discussion, Excerpt, Featured Image, Name, Permalink, Preview, Revisions, Revisions list, Status, Title, Trackbacks, Visibility inside a field group
* Field Groups: Post Locations - Added "Post Author", "Post Author Role", "Post Date", "Post Date Time", "Post Path", "Post Screen", "Post Slug", "Post Time", "Post Title" conditional rules
* Field Groups: Taxonomy Locations - Added "Taxonomy Term", "Taxonomy Term name", "Taxonomy Term Parent", "Taxonomy Term Slug", "Taxonomy Term Type" conditional rules
* Field Setting: Added "Global Field Condition" setting allowing to use the field value as a Field Group conditional rule
* Field Setting: Added "Required Message" setting allowing to customize the field required message
* Field Setting: Added "Min/max" setting allowing to customize the minimum & maximum items for the following fields: Checkbox, Post Object, Select, Taxonomy, Forms, Post Statuses, Post Types, Taxonomies, Taxonomy Terms, User Roles

**ACF Extended Free 0.8.7.4:**

* Module: Json AutoSync - Fixed an issue where json file would not be updated when the field group was created without ACF Extended (Json Sync checkbox was unchecked)
* Module: Dynamic Forms - Added the ability to pass an array to the `acfe_form()` function allowing to override settings
* Module: Dynamic Forms - Fixed Elementor + YOAST infinite loop when using the `[acfe_form]` shortcode to create a new post
* Module: Dynamic Forms - Post/Term/User Actions load values setting is now disabled by default
* Module: Dynamic Post Type - Updated Block Editor instructions for the `show_in_rest` setting
* Module: Dynamic Taxonomy - Updated Block Editor instructions for the `show_in_rest` setting
* Field: Hidden - Fixed the field render when in table element
* Field: Columns - Enhanced Field Group UI
* Field: Columns - Hidden in Users / Terms views to avoid display problem (Due to table render)
* Field: Advanced Link - Fixed ACF CSS that adds a `display:none` on sub fields buttons
* Field: Taxonomy Terms - Added hook `filter('acfe/fields/taxonomy_terms/query', $args, $field, $post_id)` to change the ajax query

= ******* =
* Field: Flexible Content - Fixed "Copy/Paste" function which wasn't working correctly with WYSIWYG & Code Editor fields since the latest ACF 5.9 update
* Field: Column - Columns sizes are now based on a grid of 12 (1/12, 2/12, 3/12, 4/12 ...)
* Field: Taxonomy - Added compatibility with "Value equal", "Value not equal", "Value pattern" & "Value contains" conditional logic
* Field: Select - "Placeholder" setting is now only available when "Stylised UI" is enabled with "Allow multiple" or "Allow null"
* Module: Dynamic Post Type - Fixed "Post Type Archive Location = All" which incorrectly matched Options Pages
* Module: Dynamic Post Type - "Edit Post Type Archive" Admin bar item is now only displayed when the "Admin Archive Page" setting is enabled
* Module: Dynamic Post Type/Taxonomy - WP Permalinks are now automatically regenerated on each save
* Module: Dynamic Options Pages - Sub Options Pages are now ordered based on the "Position" setting
* Module: Dynamic Forms - Fixed an issue where visitors could not upload Image/File when using the WP modal field setting
* Module: PHP AutoSync - Updated save logic to match the new ACF 5.9.1 version (See ACF Pro 5.9.1 changelog)
* Field Groups - Added more width to the "Fields" column
* General: JS & CSS files are now minified and concatenated. `SCRIPT_DEBUG` controls the unminified version
* General: Fixed potential error during plugin activation when ACF Pro wasn't activated
* General: Gutenberg - Enhanced WP Metabox Styles

= ******* =
* Module: AutoSync - Reworked codebase, enhanced Field Groups Columns and added more information about files/folders detection
* Module: AutoSync - Added filters to target a specific field group for the PHP & Json save
* Module: AutoSync - Fixed an issue where "Available Json Sync" wouldn't be visible if the field group was using PHP Sync
* Module: Dynamic Forms - Fixed missing "Log User" icon in the Forms List screen
* Module: Dynamic Forms - Added `{current}`, `{get_field}`, `{get_option}`, `{query_var}`, `{request}` mapping in the "Custom Form Render" setting
* Module: Dynamic Forms - Added `acfe-form` to available post types in Polylang
* Module: Dynamic Forms - Local Field Groups can now be mapped
* Field Groups: Added Export PHP & Export Json to bulk actions
* Field Groups: Description column is now hidden by default
* Field Groups: Local - Added Export PHP, Export Json & Sync to database to bulk actions
* Field Groups: Categories - Added `acfe/modules/categories` setting to disable the custom taxonomy
* Field: Post Object - Fixed undefined PHP function when using the "Allow custom value" setting with ACF version below 5.8.10
* Field: Columns - Fixed "Endpoint" description typo
* General: Settings - Fixed postbox header CSS
* General: Compatibility - Fixed Post Type Order plugin which automatically drag&drop on ACF Field Groups UI
* General: Bumped minimum ACF Pro version to 5.8

= ******* =
* General: WordPress 5.5 Compatibility - Fixed the new "Postbox Order Icons" size & only display them on postbox hover
* General: ACF 5.9 Compatibility - Added missing legacy ACF 2 columns CSS
* General: ACF 5.9 Compatibility - Fixed Fields Groups UI `colspan` when empty
* General: Added `pre > code` CSS compatibility
* General: Added JS hooks for ACFE Modal: `acf.doAction('acfe/modal/open', $modal, args)` & `acf.doAction('acfe/modal/close', $modal, args)`
* Field Groups: Fixed "All Images Formats" location which triggered a PHP warning
* Field Groups: Added `acfe-postbox-top` CSS preload to avoid UI blink during admin page load
* Field Groups: Categories - Changed the `register_taxonomy()` to priority `9`
* Module: Multilang - Added `filter('acfe/modules/multilang/options', $options_pages_id)` allowing to allow/exclude specific Options Pages Post ID
* Module: Dynamic Forms - Email Action - Fixed 'Reply-to' headers which weren't correctly set
* Module: Dynamic Forms - Fixed `{field:my_field:false}` Template Tag to disable value format
* Module: Dynamic Post Types - Added additional check before `register_post_type()`
* Module: Dynamic Post Types - Added "Show in menu (text)" to allow string value
* Module: Dynamic Post Types - Fixed "Archive Slug" incorrectly set when empty while using the Import Tool
* Module: Dynamic Block Types - Added additional check for Template, Script & Styles arguments before `register_block_type()`
* Module: Dynamic Taxonomies - Added additional check before `register_taxonomy()`
* Module: Options - Fixed "Serialized" typo on edit screen
* Field: Button - Fixed typo in "Button Type" setting instructions
* Field: Button - Added default class value `button button-secondary`
* Field: Clone - Fixed "Edit in modal" setting to be available only when the "Display" setting is set to "Group"
* Field: Code Editor - Fixed "Editor Mode" setting instructions
* Field: Forms - Changed default order to Title ASC
* Field: reCaptcha - Fixed form submission when the field isn't required
* Field Settings: Bidirectional - Fixed potential PHP notice `Undefined index`

= 0.8.7 =
* General: ACF 5.9 Compatibility - Modules - Added the ACF 5.9 Header menu
* General: ACF 5.9 Compatibility - Field Groups - Removed ACFE "Locations" column
* General: ACF 5.9 Compatibility - Field Groups - Removed ACFE "Json Sync" column
* General: ACF 5.9 Compatibility - Field Groups - Enlarged "PHP Sync" & "Load" columns
* General: ACF 5.9 Compatibility - Field Groups - Removed the "Field Group Key" value from row actions
* General: ACF 5.9 Compatibility - Field Groups - Changed the text color of "Disabled" state
* General: ACF 5.9 Compatibility - Field Groups - "Description" column is hidden by default
* General: ACF 5.9 Compatibility - Flexible Content - Removed "Clone" field setting
* General: ACF 5.9 Compatibility - Flexible Content - Added "Hide: Duplicate Button" setting
* General: ACF 5.9 Compatibility - Block Type - Added "Inner Block" setting
* General: ACF 5.9 Compatibility - Block Type - Added "Supports: Align Content" setting
* General: ACF 5.9 Compatibility - Block Type - Added "Align Content" setting
* Module: Dynamic Block Type - Fixed Template, Style & Script Render paths during Block Type registration
* Field: Radio - Enhanced Group display for horizontal layout

= ******* =
* Field: Flexible Content - Dynamic Render - External Layout Style & Script files now check for current domain before trying to automatically enqueue `{file}-preview.css` in Preview Mode
* Field: Flexible Content - Dynamic Render - Fixed `$field`, `$layout` & `$is_preview` variables which weren't correctly passed to the Layout Template file
* Field: Flexible Content - Dynamic Render - Fixed Layout Style & Script enqueue handles which weren't using the real Flexible Content field's name
* Field: Radio / Checkbox - Added Group display settings. `## Group` markup can be used in choices settings to create a new group
* Field: Taxonomy Terms - Added "Radio Button" display type

= ******* =
* Field: Flexible Content - Enhanced Code Base
* Field: Flexible Content - Dynamic Render - Enhanced `get_flexible()` & `the_flexible()` functions logic
* Field: Flexible Content - Dynamic Render - Enhanced Template, Style & Script files detection. Now detects paths from WP root directory, and from `/wp-content/` directory
* Field: Flexible Content - Dynamic Preview - Automatically include the layout `{template}-preview.php` file instead of `template.php`, if it is found within the same path
* Field: Flexible Content - Dynamic Preview - Automatically enqueue the layout `{style}-preview.css` file in addition of `style.css`, if it is found within the same path
* Field: Flexible Content - Dynamic Preview - Automatically enqueue the layout `{script}-preview.js` file instead of `script.js`, if it is found within the same path
* Field: Flexible Content - Dynamic Preview - Fixed full path match for Layouts Templates files
* Field: Flexible Content - Added `filter('acfe/flexible/prepend/template/name=my_flexible', $path, $flexible, $layout)` and its variations to alter the field's setting prepend (display only)
* Field: Flexible Content - Added `filter('acfe/flexible/prepend/style/name=my_flexible', $path, $flexible, $layout)` and its variations to alter the field's setting prepend (display only)
* Field: Flexible Content - Added `filter('acfe/flexible/prepend/script/name=my_flexible', $path, $flexible, $layout)` and its variations to alter the field's setting prepend (display only)
* Field: Flexible Content - Deprecated `acfe/flexible/layout/thumbnail/layout=my_layout` hooks and its variations. Use `acfe/flexible/thumbnail/layout=my_layout` instead
* Field: Flexible Content - Deprecated `acfe/flexible/layout/render/template/layout=my_layout` hooks and its variations. Use `acfe/flexible/render/template/layout=my_layout` instead
* Field: Flexible Content - Deprecated `acfe/flexible/layout/render/before_template/layout=my_layout` hooks and its variations. Use `acfe/flexible/render/before_template/layout=my_layout` instead
* Field: Flexible Content - Deprecated `acfe/flexible/layout/render/after_template/layout=my_layout` hooks and its variations. Use `acfe/flexible/render/after_template/layout=my_layout` instead
* Field: Flexible Content - Deprecated `acfe/flexible/layout/render/style/layout=my_layout` hooks and its variations. Use `acfe/flexible/render/style/layout=my_layout` instead
* Field: Flexible Content - Deprecated `acfe/flexible/layout/render/script/layout=my_layout` hooks and its variations. Use `acfe/flexible/render/script/layout=my_layout` instead
* Field: Flexible Content - Deprecated `acfe/flexible/layout/enqueue/layout=my_layout` hooks and its variations. Use `acfe/flexible/enqueue/layout=my_layout` instead
* Field: Flexible Content - Deprecated `acfe/flexible/preview` hooks and its variations
* Module: Dynamic Block Types - The "Template Render" setting is now independent from the theme
* Module: Dynamic Block Types - Added `filter('acfe/block_type/prepend/template/name=my-block-type', $path, $name)` and its variations to alter the field's setting prepend (display only)
* Module: Dynamic Block Types - Added `filter('acfe/block_type/prepend/style/name=my-block-type', $path, $name)` and its variations to alter the field's setting prepend (display only)
* Module: Dynamic Block Types - Added `filter('acfe/block_type/prepend/script/name=my-block-type', $path, $name)` and its variations to alter the field's setting prepend (display only)
* Module: Post Type Archive Page - `have_archive()` now allows to pass the post type name. Usage example: `while(have_archive('my-post-type')): the_archive()`
* Module: Multilang - Enhanced Options Pages Post ID detection
* Module: Multilang - Added "Post Type List" & "Taxonomy List" Locations compatibility
* Module: Multilang - Fixed the "Disable module" setting which wasn't working correctly
* Module: Multilang - Fixed Polylang Ajax language detection
* General: Enhanced `acfe_update_setting()`, `acfe_get_setting()` functions and `filer('acfe/settings/{name}')` hook
* General: Removed `ACFE_THEME_PATH` & `ACFE_THEME_URL` constants
* General: Added `acfe/theme_path`, `acfe/theme_url` & `acfe/theme_folder` settings
* General: The default `acfe/php_save`, `acfe/php_load` & `acfe/theme_folder` settings are now generated based on the new `acfe/theme_path` & `acfe/theme_url` settings
* General: The `acfe/theme_folder` setting is now used to prepend Flexible Content & Block Types render fields settings (Display only)

= 0.8.6.6 =
* Module: Multilang - Fixed WPML front-end language detection for custom languages
* Module: Settings - Added "Multilang" & "Single Meta" settings in the UI
* Module: Settings - Fixed `l10n_textdomain` which wasn't correctly displayed
* Module: Dev Mode - Fixed option "Edit" action link
* Module: PHP AutoSync - Added l10n support
* Module: Single Meta - Enhanced "Delete Orphan Meta" setting logic & performance
* Field: Taxonomy Terms - Added "Term (All childs)" to display any childs level terms
* Field: Taxonomy Terms - Renamed "Term (Childs)" to "Term (Direct childs)" to avoid confusion with the new filter
* Field: Taxonomy Terms - Fixed "Term (Direct childs)" which could be duplicated in some cases

= 0.8.6.5 =
* General: Added WPML & Polylang compatibility for Options Pages with custom post ids. ie: `my-theme` post id will be translated to `my-theme_en` with WPML & `my-theme_en_US` with Polylang
* Modules: Dynamic Post Types, Taxonomies, Options Pages & Block Types - Added Multilingual compatibility for WPML & Polylang. Items are automatically registered as strings for both WPML & Polylang plugins
* Modules: Dynamic Post Types, Taxonomies, Options Pages & Block Types - Added l10n support for the ACF Exporting Tools
* Module: Dynamic Post Type Archive - Added "Edit Archive Page" in the Admin Bar in the front-end when visiting an Archive Page
* Module: Dynamic Post Type Archive - Added l10n on the Label
* Module: Dynamic Post Type - Fixed Typo in UI
* Module: Single Meta Save - Enhanced code logic & compatibility
* Module: Single Meta Save - Added `get_fields()` support
* Module: Single Meta Save - Removed the "Delete Orphan Meta" feature from Options Pages
* Module: Dev Mode - Fixed Bulk Actions being displayed when the Postboxes were hidden by user
* Module: Dev Mode - Added Clone in Sub Fields support (previously displayed as `Undefined`)
* Module: Dev Mode - Fixed PHP 7.4 notices
* Module: Dynamic Forms - Query Var setting has been removed
* Module: Dynamic Forms - Query vars are now always generated. If no action name has been set, the query var will be named `{form_name}-{action}`. ie: `my-form-post`. The next post action will be named `my-form-post-2`
* Module: Dynamic Forms - Added "Label Placement: Hidden" setting
* Module: Dynamic Forms - Added `{current:post:post_author_data}` template tags in the "Cheatsheet" tab
* Module: Dynamic Forms - Added all available `{query_var}` template tags in the "Cheatsheet" tab
* Module: Dynamic Forms - Form Fields Groups, E-mail Action From/To/Subject fields are not required anymore
* Module: Dynamic Forms - Added JS Form Submit Action code example in the "Submission" tab
* Module: Dynamic Forms - Tweaked UI
* Field: Flexible Content - Added "Layout Toggle" setting allowing the user to hide specific layout from the front-end
* Field: Flexible Content - Fixed "Layout State" setting which incorrectly forced it as "Closed" when using the "Dynamic Render" setting
* Field: Flexible Content - Fixed "Layout Title" CSS
* Field: WYSIWYG - Fixed the Smart Delayed Init in the Flexible Content when the layout had the "Layout State" on "Opened"
* Fields: Advanced Validation - Added `count()`, `is_array()`, `is_string()`, `is_numeric()`, `get_post_type()`, `get_post_by_id()`, `get_post_by_title()` rules and `Matches regex`, `Doesn't matches regex`, `!= true`, `!= false`, `!= null` operators
* Field: Date Picker - Added `==`, `!=`, `>`, `<`, `Pattern match` & `Contains` conditional logic
* Field: Image - "Use as Featured Image" setting now loads the Featured Image if it was already set
* Field: Taxonomy Terms - Added Async Load setting support
* Field: Taxonomy Terms - Fixed "Level" filter input CSS width
* Field: Post Object - Merged "Allow Custom" & "Save Custom Value as New Post" settings
* Field: Post Object - Fixed the "Save as New Post" setting when allowing "Multiple Values"
* Field: Post Object - Added instructions & code examples to alter the New Post arguments programmatically
* Field: Code Editor - Added border radius to match WP admin input style
* Field: Group - Fixed "Edit in Modal" settings which weren't displayed
* Field Groups: Location Column - Fixed potential consuming query when using the `Post == ID` Conditional Rule
* General: Introduced `acfe_update_setting()`, `acfe_get_setting()` functions and `acfe/settings/{name}` filter
* General: Compatibility - Fixed WPML PHP notices in Field Groups
* General: Compatibility - Fixed Advanced Forms PHP notice during ACF Extended: Forms submission
* General: Readme - Added Field Bi-directional instructions to migrate already existing data
* General: Readme - Added Multilingual section
* General: Readme - Added Donators section

= ******* =
* Module - Enhanced UI - Added WP User & WP Settings UI enhancements
* Module - Enhanced UI - Taxonomies UI enhancements were moved in the general Enhancement module. The setting `acf_update_setting('acfe/modules/taxonomies')` has been replaced by `acf_update_setting('acfe/modules/ui')`
* Module: Dev Mode - Added "Field Type" column on ACF meta overview
* Module: Dev Mode - Added "Autoload" column on Options meta overview
* Module: Dev Mode - Added "Delete" action for each meta & options fields
* Module: Dev Mode - Added "Bulk Delete" action
* Module: Single Meta Save - Reworked codebase
* Module: Single Meta Save - Added Options pages compatibility (disabled by default. See `filter('acfe/modules/single_meta/options')` usage in the readme to enable specific Options ID)
* Module: Single Meta Save - Added `filter('acfe/modules/single_meta/post_types')` to allow specific post types only (default to: all)
* Module: Single Meta Save - Added `filter('acfe/modules/single_meta/taxonomies')` to allow specific taxonomies only (default to: all)
* Module: Single Meta Save - Fixed bidirectional setting which wasn't working when Single Meta Save was enabled
* Fields settings: Bidirectional - Added Self-bidirectional setting, allowing to link a field on itself
* Fields settings: Bidirectional - Added Multi-bidirectional setting, allowing to link multiple fields
* Field: Taxonomy Terms - Fixed a bug with last childs choices not being correctly rendered
* Field: Code Editor - Fixed duplicated field from the Field Group UI when user cloned the field

= ******* =
* Module: Dynamic Post Types/Taxonomies/Block Types/Options Pages - Slugs can now to edited & updated from the UI
* Module: Dynamic Options Pages - Added a configuration icon on top of options page to easily switch to options page settings
* Module: Dynamic Forms - User Action query var now returns the password if it has been created/generated during the action. This will allow developers to send the password confirmation via an e-mail action using query vars
* Module: Dynamic Forms - Added `{request:name}` & `{request:name:key}` template tags to retrieve `$_REQUEST` `$_GET` & `$_POST` data
* Module: Dynamic Forms - Added `{get_option:name}` & `{get_option:name:key}` template tags to retrieve an option from database
* Module: Dynamic Forms - Enhanced "Post Action" query var which now returns post author data in the `{query_var:my-action:post_author_data:field}` template tag
* Module: Dynamic Forms - Enhanced JS detection of the `acf` object to avoid potential errors when using aggressive minification
* Module: Dynamic Forms - Fixed `{current:user:show_welcome_panel}` which was printed twice in the "Cheatsheet" tab
* Module: Dynamic Forms - Fixed `wp_unslash()` on the success message
* Module: Dynamic Forms - Fixed undefined `post` key notice which could occurs in some cases
* Module: Dynamic Forms - Fixed template tags which were using `id` instead of `ID`
* Module: Dynamic Forms - Fixed the conditional logic on custom form render when using complex fields like repeaters or flexible content
* Module: Single Meta Save - `acf` meta is now updated on submission instead of being reset each time it is saved. This fix a potential problem when fields wouldn't be displayed to specific user roles
* Module: PHP AutoSync - PHP files are now included only if the filename starts with `group_*.php`
* Field: Columns - Added responsive breakpoints
* Field: Button - Added Advanced Settings compatibility
* Field: Advanced Link - Fixed a bug which could render an empty "Archive" default value artifact in post objects fields
* Field: Advanced Link - Fixed required field validation
* Field: Code Editor - Fixed line wrap in terms views
* Field: WYSIWYG - Fixed the automatic smart delayed initialization in flexible content layouts
* Field: WYSIWYG - Fixed init in Flexible Content layouts which could break when trying to open a layout before the complete page load
* General: Settings API - Enhanced upgrade process
* General: Settings API - `acfe` option now regenerate Post Types/Taxonomies/Block Types/Options Pages on reset

= 0.8.5.5 =
* Field: Button - Added nominative JS hooks `action('acfe/fields/button/before/name=my_field', response, $el, data);`
* Field: Button - Added nominative JS hooks `action('acfe/fields/button/success/name=my_field', response, $el, data);`
* Field: Button - Added nominative JS hooks `action('acfe/fields/button/complete/name=my_field', response, $el, data);`
* Field: Button - Deprecated JS hooks `acfe/fields/button/before_ajax` & `acfe/fields/button/ajax_success`. Replaced by `acfe/fields/button/before` & `acfe/fields/button/success`
* Field: Flexible Content - Added generic `acfe/flexible/thumbnail` hook
* Module: Dynamic Forms - Fixed a bug where 2 forms with 2 fields with the same name on the same page, will override `default_value` during render
* Module: Dynamic Forms - Fixed a nasty bug with "Post Action" which could trigger an infinite loop when using Elementor & YOAST. See bug report: https://github.com/elementor/elementor/issues/10998
* Module: Dynamic Forms - Fixed jQuery not recognized on form success in some specific case
* Module: Dynamic Forms - Added "No form element" compatibility allowing validation settings to be applied when form tag isn't printed
* Module: Dynamic Forms - Fixed typo in "Custom Action" code example
* Module: Dynamic Forms - Added to the possibility to use `get_field('my_field')` to retrieve form input value inside `acfe/form/load` hooks
* Module: Single Meta Save - Fixed hook arguments which could trigger a PHP error in some specific cases
* General: Readme - Added Flexible Content Settings Modal example

= 0.8.5 =
* General: Added Settings/Options API
* General: Improved modules performance
* Module: Dynamic Forms - Improved module UI, fields UI & documentation
* Module: Dynamic Forms - Added "Cheatsheet" tab to list all available template tags
* Module: Dynamic Forms - Added "Validation" tab
* Module: Dynamic Forms - Email Action - Added "Reply-to", "Cc", & "Bcc" fields
* Module: Dynamic Forms - Email Action - Added "Delete attachment once sent" setting for each Dynamic attachments
* Module: Dynamic Forms - Email Action - Added "Static attachments"
* Module: Dynamic Forms - User Action - Added "Log user" action type
* Module: Dynamic Forms - Post Action - Terms can now be created on the fly using custom template tags. Example: `My new term|my-taxonomy`
* Module: Dynamic Forms - Post Action - Fixed terms not being set correctly when user wasn't logged
* Module: Dynamic Forms - Template tags are now allowed everywhere
* Module: Dynamic Forms - Added "Apply field groups rules" setting to take field group conditional locations into account
* Module: Dynamic Forms - Added a switch on "Form HTML override" to explicitly explain this setting is optional
* Module: Dynamic Forms - Added `{get_field:field_name}` template tags to retrieve field value from DB. More information in the "Cheatsheet" tab
* Module: Dynamic Forms - Added `{current:post/term/user/author/form}` template tags to retrieve current data information. More information in the "Cheatsheet" tab
* Module: Dynamic Forms - Added `{current:***}` template tags to retrieve current post/term/user/post author/form information. More information in the "Cheatsheet" tab
* Module: Dynamic Forms - Added "Hide successful re-validation notice" setting
* Module: Dynamic Forms - Google Map return value is now correctly formatted
* Module: Dynamic Forms - Fixed WPML compatibility problem when using "honeypot" field
* Module: Dynamic Forms - Fields conditional logic are now working when fields are wrapper within custom div
* Module: Dynamic Forms - Added CSS to WP Media modal & cleaned accessibility text
* Module: Dynamic Forms - Added "Default Uploader Type" setting (based on field setting)
* Module: Dynamic Forms - Fixed Loading value not working correctly on repeaters/groups/flexible content
* Module: Dynamic Forms - Added PHP function to easily import form. `acfe_import_dynamic_form($array|$json);`
* Module: Dynamic Forms - Fixed shortcode being interpreted in admin view (Gutenberg/Dynamic Ajax Preview)
* Module: Dynamic Post Type - Fixed capabilities not being correctly mapped
* Module: Dynamic Post Type - Fixed Admin orderby & order not working correctly when `has_archive` was disabled
* Module: Dynamic Taxonomy - Fixed capabilities not being correctly mapped
* Module: Dynamic Taxonomy - Fixed Front posts per page, orderby & order settings
* Module: Dynamic Author - Added better post author ID cast
* Module: AutoSync - New fields groups now have Json/PHP AutoSync setting checked by default if the corresponding folder exists
* Field: Flexible Content - Modal Categories Selection setting is now using Select2 with tags
* Field: Flexible Content - The bottom bar in modal now clear potential floating elements
* Field: Flexible Content - Fixed clone function not working correctly with tabs that have conditional logic
* Field: Select - Added "## Title" markdown to add option group in field's choices
* Field: Select - Added "Allow custom" setting
* Field: Select2 - Tweaked CSS to match WordPress 5.3 UI
* Field: Select2 - Fixed WPML CSS overriding select2 style
* Field: Code Editor - The field now correctly trigger `change()` on input
* Field: Code Editor - Added "Max rows" setting
* Field: Code Editor - Fixed "Default value" javascript code being executed within the Data modal
* Field: Advanced Link - Added Term link types
* Field: Advanced Link - Added Post Type Archive link types
* Field: Advanced Link - Fixed custom sub fields not working correctly when retrieving values
* Field: Advanced Link - Reworked the custom sub fields declaration. It's now easier to add custom fields
* Field: reCaptcha - Fixed v2 theme & size settings not working correctly
* Field: reCaptcha - Fixed v3 JS error on reset
* Field: Datepicker/Datetime picker/Timepicker - Added CSS to match WordPress 5.3 UI
* Field Group: Fixed Instructions "Tooltip" mode not working correctly in groups
* Field Group: Added pre-rendering CSS for field groups with labels on left, removing potential during admin page load
* Field Group: Renamed "Third Party" Field Groups types to "Local"
* General: Gutenberg - Fixed CSS for modal

= ******* =
* Field: Taxonomy Terms - Fixed a problem with value return when "Load Terms" was enabled
* General: Modal - Fixed z-index problem with WP attachment modal

= ******* =
* Field Group: Hide on Screen - Added "Block Editor" (Gutenberg) setting, allowing administrator to disable the block editor on field group location
* Field Group: Third Party - Fixed Export/Sync Clones fields being processed during the action
* Field Group: Postbox Seamless CSS class are now added in PHP, which remove the blink during admin page load caused by the class being added in JS
* Field Group: Raw Data button now also display the WP_Post object
* Field: Button - Before/After HTML settings are now using code editor
* Field: Button - Ajax call - Added nominative hooks allowing to target specific field. Hook is now easier. Instructions have been updated
* Field: Flexible Content - Added "Advanced Flexible Content" setting (ON/OFF). All advanced settings are now hidden by default and depend on that setting to be shown. (Retro compatibility: if any advanced setting has been saved before, this setting will be set to ON)
* Field: Flexible Content - Added "Clone" button as a setting (Not enabled by default in all flexible content anymore)
* Field: Flexible Content - Added "Hide: Add Layout Button" setting
* Field: Flexible Content - Added "Hide: Remove Layout Button" setting
* Field: Flexible Content - Added "Lock Flexible Content" (sortable) setting
* Field: Flexible Content - Flexible Content can now be completely locked and all actions removed from the field settings
* Field: Flexible Content - Layout Settings modal now allow multiple clones
* Field: Flexible Content - Layout Settings modal size can now be selected for each layout (small/medium/large/extra large/full)
* Field: Flexible Content - Fixed "Force State: Open" not working correctly in some specific cases
* Field: Flexible Content - Fixed Modal Selection z-index problem on attachment screen
* Field: Flexible Content - Fixed Modal Edition z-index problem with button group (when already inside a modal)
* Field: Flexible Content - Fixed a bug causing duplicated "Layout Setting" & "Layout Title Edition" fields when export/re-importing a field group with flexible content that had this settings
* Field: Taxonomy Terms - Fixed a PHP notice when "Load Terms" was enabled with "select" as field type
* Field: Group/Clone - Seamless style CSS fixed in term view
* Field: Color Picker - Added position relative property when the field is used inside a modal
* Field: Hidden - Added global CSS style
* Module: Single Meta Save - Improved save process performance (it's now even faster!)
* Module: Dynamic Forms - Added `{query_var:var}` template tag in E-mail action, Post Load source, Post Target, Term Load source, Term Target & User Load source, Redirection & Updated Message fields. This will allow user to retrieve a specific `query_var` and use it dynamically
* Module: Dynamic Forms - `{query_var:var}` template tag also allow to specific key if the value is an array, using the following tag: `{query_var:var:key}`
* Module: Dynamic Forms - New "Query var" settings on Post, Terms, Users & E-mail actions that generate a custom query var based on the "Action name" after completing the action. For example: Post Action with Action name "my-action" will generate a `query_var` named `my-action` and as value the post data (array) that has been created/updated. This `query_var` can be accessed using `get_query_var('my-action');` or the template tag `{query_var:my-action:ID}` (Post ID), `{query_var:my-action:post_title}` (Post Title), `{query_var:my-action:permalink}` (Post Permalink), `{query_var:my-action:admin_url}` (Post Admin URL)
* Module: Dynamic Forms - Fixed shortcode not working correctly when using Form ID instead of Form name
* Module: Dynamic Forms - Actions UI Layouts can now be collapsed
* Module: Dev Mode - Added Meta Overview for Options Page
* Module: Options Page - Child Options page are now correctly displayed as child in the Options Page UI
* General: Modal - Added localization for the "Close" button
* General: Modal - Added Small/Medium/Large/Extra Large/Full sizes

= 0.8.4.1 =
* General: Fixed loading sequence when `get_field` was called directly in the functions file
* Field: Flexible Content - Fixed Dynamic Preview overlay z-index

= 0.8.4 =
* Module: Added Single Meta Save feature - Compress all fields values from the current post, term or user into one single meta data. This feature is disabled by default, to enable it, use `acf_update_setting('acfe/modules/single_meta', true);`. More information available in the readme.
* Module: Dynamic Post Types - Added `while(have_archive()): the_archive();` logic when the "Admin Archive Page" setting is turned on. This template tags can be used in the `archive-post-type.php` template which makes `get_field('my_field')` calls easier
* Module: Dynamic Post Types/Taxonomies - Fixed Posts/Terms column data when object was trashed.
* Module: PHP AutoSync - PHP sync files are now removed when the field group is disabled/trashed.
* Module: Dynamic Forms - In the "E-mail Action" the "From" field is now required.
* Module: Dynamic Forms - The Javascript hook `acfe/form/submit/success` is now correctly fired on form submission
* Module: Dev Mode - Added the WP & ACF Meta Overview on User pages
* Module: Dynamic Post Types/Taxonomies/Options Pages/Block Types - Removed Draft button action
* Field: Flexible Content - Significant Performance Boost (~50/60% faster during the loading). Many settings are now loaded in PHP. This massive rework brings some great performance on complex Flexible Content fields.
* Field: Flexible Content - Added "Layouts: Asynchronous" setting which add layouts using Ajax method (instead of having hidden layouts models in the DOM). This setting increase performance on complex Flexible Content fields.
* Field: Flexible Content - Added "Layouts: Settings" setting which let you choose a field group to clone and to be used as a configuration modal for each layout. Settings can then be used with `while(have_settings()): the_setting(); get_sub_field('my_setting');` in the Layout Template.
* Field: Flexible Content - Initial "Dynamic Preview" are now processed during page administration load, and not Ajax anymore. This tweak also speed-up the loading speed.
* Field: Flexible Content - Fixed a potential duplicated categories bug in the Selection Modal if the category name had spaces.
* Field: Flexible Content - "Remove Actions" now correctly remove Clone & Copy/Paste buttons.
* Field: Flexible Content - Added "Disable Legacy Layout Title Ajax" setting. It disables the native ACF Layout Title Ajax call on `acf/fields/flexible_content/layout_title`.
* Field: Flexible Content - Fixed the `$is_preview` variable not being available in specific situations.
* Field: Flexible Content - Fixed Dynamic Preview repeater fields inside a layout which could send an additional `acfcloneindex` during the preview mode
* Field: Flexible Content - Fixed Dynamic Preview with WP Query in the layout, which could be duplicated due to how WP Admin manage custom queries
* Field: Flexible Content - Fixed "Edit" icon vertical align with the latest WP 5.3 update
* Field: Flexible Content - Added shorter CSS class for preview wrapper `-preview`
* Field: Flexible Content - Fixed the native ACF setting "Select layout" with a wrong height on WP 5.3
* Field: Flexible Content - Fixed Enter key opening duplicated modal with Modal Edition setting
* Field: Flexible Content - Added `filter('acfe/flexible/layouts/div/name=my_flexible', $div, $layout, $field)` to change layout div attributes (with 5 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/layouts/handle/name=my_flexible', $handle, $layout, $field)` to change layout handle attributes (with 5 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/layouts/icons/name=my_flexible', $icons, $layout, $field)` to change layout handle icons (with 5 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/secondary_actions/name=my_flexible', $secondary_actions, $field)` to change Flexible Content secondary actions (copy, paste...) (with 3 variations)
* Field: Advanced Link - Fixed required validation which could fail if a post object was selected.
* Field: Advanced Link - Fixed a `z-index` problem in menu/items
* Field: Code Editor - Added compatibility with WP Code Editor Settings (editor themes).
* Field: Group/Clone - In Seamless Style mode instructions could be truncated in some specific cases
* Field: Group/Clone - Seamless Style mode wasn't working correctly in the Term administration
* Field: Group/Clone - Fixed "Seamless Style" typo
* Field: Group/Clone - Fixed "Edit in modal" which wasn't correctly working in menus
* Field Group: Fixed Category sync which failed to create & set new field group category if not already available in WP
* Field Groups: Fixed empty field groups list colspan
* Fields: Post Statuses/Post Types/Taxonomies/Taxonomies Terms/User Roles can now be used as conditional display field
* General: ACF Extended now correctly detects ACF Pro when included in the WP Theme.
* General: ACF Extended can now be included in WP Themes (following the same logic as ACF)

= ******* =
* Field: Flexible Content - Fixed PHP `Undefined index: acfe_flexible_modal` notice
* Fields: Select2 CSS Enhanced - Fixed forced height when in multiple mode
* Fields: Select2 CSS Enhanced - Global standardization of generic select input & select2 style
* Module: Dynamic Forms - Added missing hook `filter('acfe/form/load/action=my-action-alias',  $args, $post_id);`
* General: Fixed typo in Readme

= 0.8.3 =
* Field: Advanced Link - Added "Allowed Post Types" & "Allowed Taxonomies" setting to filter allowed Post types & Taxonomy terms in the post selection
* Field: Flexible Content - Categories in the Layouts Selection Modal are now sticky, the vertical scrollbar is now applied to layouts (Thanks @Damien C.)
* Field: Flexible Content - Added filter to disable a potentially unnecessary ACF ajax call when closing a layout: `filter('acfe/flexible/remove_ajax_title/name=my_flexible', false, $field);`
* Field: Flexible Content - Fixed Layout Title Edition input which could disappear in some rare cases
* Field: Flexible Content - Fixed `z-index` CSS conflict in modals when the flexible content was inside an accordion field (Thanks @Damian P.)
* Field: Flexible Content - Fixed `border-bottom` CSS on layout handle when edition modal is set to ON
* Field: Flexible Content - Fixed an issue where Categories in the Layouts Modal would still appear when setting was set to ON then to OFF
* Field: Post Object - Added "Allow custom value" setting when "Advanced UI" is ON
* Field: Post Object - Added "Save custom value as post" setting when "Allow custom value" is ON
* Field: reCaptcha - Changed `file_get_contents()` to `curl` method for better compatibility (Thanks @Brandon A.)
* Field: Select - Placeholder setting is now also available if "Advanced UI" is set to ON
* Fields: Select2 - CSS enhancements have been moved to the WP admin and are not enqueued in the front-end anymore (Thanks @jaakkosaarenketo)
* Field: Taxonomy Terms - Added "Load Terms" & "Save Terms" allowing the user the load & add terms to the current post, just like ACF does with the "Taxonomy" field (Feature request: @gptrading)
* Field Settings: Bidirectional - Fixed multiple sub fields check which bail too early during the field relation selection process (Thanks @doublesharp)
* Fields Groups: Fixed Json/PHP Sync warnings that were not properly checking the ACF setting `load_json` paths (Thanks @doublesharp)
* Fields Groups: Added Export Json & PHP in the Field Group single view (sidebar)
* Fields Groups: Categories are now synced with field groups during the export/import process
* Module: Author - Fixed duplicated post revision when updating an ACF value (Thanks: @François B.)
* Module: Author - Fixed an issue where the module would not show up on post types which are registered using a priority higher or equal to 5 (Thanks @yangkennyk)
* Module: Dev Mode - Added fields counter in the metabox title (Feature request: @Damien C.)
* Module: Dynamic Forms - Added the ability to use `{field:field_name}` & `{field:field_key}` values in the "Updated message" setting (Feature request: @alexene22)
* Module: Dynamic Forms - Better handling of select/checkbox/radio values render (Thanks @jabbadu)
* Module: Dynamic Forms - Fixed the ACF form submit button which would be still displayed, even if the setting was set to OFF (Thanks @Damien C.)
* Module: Dynamic Forms - Added "Post field groups" setting in the "Advanced" tab to override displayed field groups by a specific post field groups
* Module: Dynamic Forms - Added fallback when rendering complex fields values (array)
* Module: Dynamic Forms - Added `filter('acfe/form/format_value/name=my_field', $value, $unformatted_value, $post_id, $field)` to format field output used in email/post/term/user actions. (Also works with `/type` & `/key` selectors)
* Module: Dynamic Forms - Added Javascript ACF Action on successful form submission: `acf.doAction('acfe/form/submit/success/name=my-form')`
* Module: Dynamic Forms - Added compatibility fix for the plugin "MC ACF Flexible Template" (Thanks @MarcinKilarski)
* Module: Dynamic Forms - Added Manual Json Export & Import tools
* Module: Dynamic Forms / Post Types / Taxonomies / Block Types / Options - Added Manual Export in the single view
* Module: Dynamic Forms / Post Types / Taxonomies / Block Types / Options - Added compatibility fix for PolyLang, allowing user to translate modules items (Thanks @Drashka)
* Module: Dynamic Post Types / Taxonomies / Block Types / Options - Added Manual PHP Export action (Feature request: @jaakkosaarenketo)
* General: Added CSS styles to match WP 5.3 Update UI

= 0.8.2 =
* Dynamic Forms: Fixed error position 'below' not working on some specific fields (Select)
* Dynamic Forms: `acfe_form_is_front()` & `acfe_form_is_admin()` helpers now also check native ACF Form screen
* Dynamic Forms: Added action alias name setting for each action allowing better targeting when using hooks
* Dynamic Forms: Reworked forms actions hooks and added 'Advanced' tab for each action with code examples
* Field: Groups/Clones - CSS integration tweaks are now optional (Thanks @Brandon A.)
* Field: Groups/Clones - Added "Seamless style" setting which enable better CSS integration (remove borders and padding)
* Field: Code Editor - Added Field (use the WP Core CodeMirror script)
* Field: Taxonomy Terms - Added advanced settings allowing specific taxonomies or specific terms in the field (with level or parent/child dependencies)
* Field: reCaptcha - Fixed a bug where reCaptcha would not work properly
* Field: Flexible Content - Updated JS filter `acf.doAction('acfe/fields/flexible_content/preview', response, flexible.$el, $layout, ajaxData)` & moved it after preview HTML parse
* Field: Flexible Content - Updated JS filters & added variations `acf.doAction('acfe/fields/flexible_content/preview/name=my_flexible&layout=my_layout', response, flexible.$el, $layout, ajaxData)`
* Field: Flexible Content - Fixed "Remove Collapse" setting not working on newly added layout (Moved RemoveCollapse from acf-extended-fc-control.js to acf-extended-fc.js (acfe/flexible/layouts))
* Fields: Fields are now registered using `acf_register_field_type()`. This will allow developers to use `acf_get_field_type('acfe_field')`
* Fields settings: Bidirectional - Added filter which allow to force related field to be updated when migrating from already existing values
* Fields settings: Bidirectional - Added `filter('acfe/bidirectional/force_update/name=my_field', true, $field, $post_id)` (available with /type & /key) (Feature request: @anjanphukan)
* Field Groups: Fixed CSS which wrongly hide instructions when there's no field label (Thanks @Damien C.)
* Field Groups: Third Party - Added PHP filter to change source column output `filter('acfe/field_groups_third_party/source', $source, $post_id, $field_group)`
* General: Added `filter('acfe/field_wrapper_attributes/type=$field_type', $wrapper, $field)` (also with /name & /key variations)
* General: Splitted CSS for front-end/back-end
* General: Fixed ACF Setting Tab "Module: Taxonomies Enhancements" title (thanks @doublesharp)

= 0.8.1 =
* Field: Advanced Link - Added instructions to add custom fields in the field administration
* Field: Advanced Link - Added filters `acfe/fields/advanced_link/fields/name=my_field` & `acfe/fields/advanced_link/fields/key=field_xxxxxxx`
* Field: Advanced Link - Changed values keys to: `type`, `url`, `post`, `title` & `target`
* Field: Button - Updated JS hook `acf.doAction('acfe/fields/button/before_ajax', $el, data)`
* Field: Button - Updated JS hook `acf.doAction('acfe/fields/button/ajax_success', response, $el, data)`
* Field: Column - Fixed endpoint column not correctly closing the row
* Field: Flexible Content - Added JS hook `acf.doAction('acfe/fields/flexible_content/before_preview', $el, data)`
* Field: Flexible Content - Added JS hook `acf.doAction('acfe/fields/flexible_content/preview', response, $el, data)`
* Field: Flexible Content - Clone/Copy/Paste - Fixed a problem where new select option values weren't properly duplicated (thanks @chrisschrijver)
* Module: Dynamic Forms - Advanced settings: Fixed `prepare_field` on form front
* Module: Dynamic Forms - Fixed a compatibility problem if a dynamic form was named `form`
* Module: Dynamic Forms - Rename `field class` to `input class`
* Module: Dynamic Forms - Fixed a problem where native ACF Form would not properly work (thanks @maximelessard)
* Module: Dynamic Forms - Added Javascript to avoid re-submission on page refresh when 'Hide form' is set to ON

= 0.8 =
* Module: Dynamic Forms - Added module. Forms UI available under ACF menu
* Module: Dynamic Forms - Added PHP helper `acfe_form('my_form_name')` & `acfe_form(188)` to display a form
* Module: Dynamic Forms - Added shortcode `[acfe_form name="my_form_name"]` & `[acfe_form ID="188"]` to display a form
* Module: Dynamic Forms - Added setting to disable Dynamic Forms: `acf_update_setting('acfe/modules/dynamic_forms', false)`
* Module: Dynamic Forms - Added `action('acfe/form/validation', $form, $post_id)` & `action('acfe/form/validation/name=form_name', $form, $post_id)` to validate form before submission. `get_field()`, `have_rows()`, `get_sub_field()` functions can be used.
* Module: Dynamic Forms - Added `acfe_add_validation_error('field_name_or_key', 'message')` helper to add error on specific field during the validation
* Module: Dynamic Forms - Added `action('acfe/form/submit', $form, $post_id)` & `action('acfe/form/submit/name=form_name', $form, $post_id)` to add custom action on submission
* Module: Dynamic Forms - Added 8 new hooks per form action allowing developers to customize each action
* Field: Added Dynamic Form Select field
* Field: Added Google reCaptcha field (compatible v2 & v3)
* Field: Google reCaptcha - Added global setting: `acfe/field/recaptcha/site_key` (API site key)
* Field: Google reCaptcha - Added global setting: `acfe/field/recaptcha/secret_key` (API secret key)
* Field: Google reCaptcha - Added global setting: `acfe/field/recaptcha/version` (v2 or v3)
* Field: Google reCaptcha - Added global setting: `acfe/field/recaptcha/v2/theme` (light or dark)
* Field: Google reCaptcha - Added global setting: `acfe/field/recaptcha/v2/size` (normal or compact)
* Field: Google reCaptcha - Added global setting: `acfe/field/recaptcha/v3/hide_logo` (true or false)
* Field: File - Removed the native 'No file selected' text
* Field: File & Image - Added 'Uploader type' setting to choose which uploader to use (Basic or Media)
* Field: Post Type Selection - Code has been reworked. The field is now compatible with all select, checkbox & radio settings
* Field: Taxonomy Selection - Code has been reworked. The field is now compatible with all select, checkbox & radio settings
* Field: Button - Code has been reworked & added Ajax call setting
* Field: Button - Added Ajax action: 'acfe/fields/button', POST parameters: $post_id, $field_key, $field_name
* Field: Button - Added Javascript Ajax action: `('acfe/fields/button/before_ajax', this.$el)`
* Field: Button - Added Javascript Ajax action: `('acfe/fields/button/ajax_success', response, this.$el)`
* Field: Slug - Code has been reworked
* Field: Dynamic Message - Added instructions & code example in the field setting view
* Field: Column - Added Field
* Field: Post Status Selection - Added Field
* Field: User Roles Selection - Added Field
* Field: Hidden Input - Added Field
* Field: Advanced Link - Added Field. Added `filter('acfe/fields/advanced_link/fields', $fields, $field, $link)` allowing developers to add custom field to the modal
* Field: Taxonomy Terms - Added Field
* Field: Group - Added Modal Edition setting allowing users to edit group values in a modal
* Field: Group - Added CSS fixes for better integration
* Field: Clone - Added Modal Edition setting allowing users to edit clone values in a modal (Only in group mode)
* Field: Clone - Added CSS fixes for better integration
* Field: Textarea - Added Code mode setting to switch font family to monospace and allow tab indent
* Field: Select - Added placeholder setting when allow null is activated
* Field: Flexible Content - Added 'Remove Collapse Action' setting
* Field: Flexible Content - Added `filter('acfe/flexible/lock', true, $field)` to lock flexible content layouts (disable sortable)
* Field: Flexible Content - Added `filter('acfe/flexible/lock/name=my_flexible', true, $field)` to lock flexible content layouts (disable sortable)
* Field: Flexible Content - Added `filter('acfe/flexible/remove_actions', true, $field)` to remove the "Add layout" button
* Field: Flexible Content - Added `filter('acfe/flexible/remove_actions/name=my_flexible', true, $field)` to remove the "Add layout" button
* Field: Flexible Content - Clone & Copy/Paste functions are now compatible with min/max settings for each layout
* Field: Flexible Content - Added `action('acfe/flexible/render/before_template', $field, $layout, $is_preview)` to add wrapper around the template render (with 6 variations)
* Field: Flexible Content - Added `action('acfe/flexible/render/after_template', $field, $layout, $is_preview)` to add wrapper around the template render (with 6 variations)
* Field: Flexible Content - Fixed `text-align:center` applied to placeholder css
* Field: Flexible Content - Fixed Style/Script render filters when returning a full URL
* Field: Flexible Content - Fixed a bug where the layout Title Edition input could disappear when clicking on the handle
* Field: Flexible Content - Fixed a bug where the close button would not appear after adding a new layout
* Field: Repeater - Added Stylised button setting
* Field: Repeater - Added CSS fixes when table is empty
* Field: Repeater - Added `filter('acfe/repeater/lock', true, $field)` to lock repeater rows (disable sortable)
* Field: Repeater - Added `filter('acfe/repeater/lock/name=my_repeater', true, $field)` to repeater rows (disable sortable)
* Field: Repeater - Added `filter('acfe/repeater/remove_actions', true, $field)` to remove the "Add row" button
* Field: Repeater - Added `filter('acfe/repeater/remove_actions/name=my_repeater', true, $field)` to remove the "Add row" button
* Field: Fixed fields label CSS when label is empty (top & left placement)
* Fields: "Advanced Validation" settings are now disabled by default. To display them, the "Advanced settings" must be turned ON in the field group
* Fields: Added "Advanced Settings" allowing administrator to set custom field properties based on the current screen (administration/front-end). "Form settings" must be turned ON in the field group
* Fields: Added Hide Label setting (in the Advanced Settings)
* Fields: Added Hide Field setting (in the Advanced Settings)
* Fields: Added `filter('acfe/load_field', $field)` to filter field settings everywhere but not in field group & tools management
* Fields: Added `filter('acfe/load_field_front', $field)` to filter field settings in the front-end (and ajax coming from front-end) but not in field group & tools management
* Fields: Added `filter('acfe/load_field_admin', $field)` to filter field settings in the administration (and ajax coming from administration) but not in field group & tools management
* Field Groups: Instructions Placement - Added "Tooltip" placement for instructions
* Field Groups: Third Party - Added PHP / Json Export & Sync actions
* Field Groups: Third Party - Added Source column
* Field Groups: Fixed potential undefined index in location column
* Location: Old Location "Post Type Archive" & "Taxonomy Archive" (in the admin list) have been renamed "Post Type List" & "Taxonomy List"
* Location: Post Type List/Taxonomy List - Fixed Image & File Upload fields being forced on basic mode (Thanks @dominikkucharski)
* Location: New Location "Post Type Archive" creates an option page under post types menu when argument `acfe_admin_archive` is set to true (also available in Dynamic Post Type)
* Module: Settings - Added ACF Extended tab to list the plugin's current settings
* Module: Settings - Fixed `save_json` setting being incorrectly displayed in the ACF Setting tab
* Module: PHP Sync - Fixed a problem where field group would not be recognized as loaded in PHP if Json AutoSync is also activated
* Module: PHP Sync - Renamed PHP Sync settings to `acfe/php`, `acfe/php_save`, `acfe/php_load`, `acfe/php_found`
* Module: Dev Mode - Added mode which replace the WP Post Meta box with more details data (in posts & terms). It also enables `SCRIPT_DEBUG`. Can be activated using `acf_update_setting('acfe/dev', true)` or `define('ACFE_dev', true)`
* Module: Dynamic Taxonomy - Added missing `meta_box_cb` setting (thanks @DavidGMiles)
* Module: Options - Fixed potential validation problem
* General: Improved ACF Extended modal CSS style & Added Modal inside modal overlay

= *******.9 =
* Field: Flexible Content - Fixed Copy/Paste function doing incorrect checks on radio, checkboxes and select inputs
* Field Group: Fixed field 'Data' button being displayed on newly created fields

= *******.8 =
* Field: Flexible Content - Fixed Clone & Copy/Paste functions in multi level flexible content (flexible inside flexible inside flexible...) (Thanks @AsmussenBrandon)
* Field: Flexible Content - Fixed CSS border glitch

= *******.6 =
* Field: Flexible Content - Fixed Clone & Copy/Paste functions for accordions fields (Thanks @Damian P.)
* Field: Flexible Content - Fixed Clone & Copy/Paste functions for FontAwesome fields (Thanks @Damian P.)
* Field: Flexible Content - Close Button setting is now always available and is not conditional anymore
* Field: Flexible Content - Render Template/Style/Script path now supports parent/child theme. If a file is found in the child theme, it will be included. Otherwise it will be checked against the parent theme path (Feature Request: @r3dridl3)
* Field: Flexible Content - Fixed Layout Title Edition not working in some rare cases (Thanks @Damian P.)
* Field: Post Types & Taxonomies Select - Fixed two PHP noticed
* General: Added ACF Extended GitHub repository URL in the readme

= ******* =
* Field: Flexible Content - Settings are now dynamic (and not global anymore) (Thanks @Val)
* Field: Flexible Content - Added CSS class on cloned layouts
* Field: Flexible Content - Removed `esc_attr()` from Layout Title Edition, allowing icons to be displayed correctly
* Field: Flexible Content - Fixed potential duplicated clone buttons in specific cases (Thanks @chrisschrijver)
* Field: Flexible Content - Added "Layout Placeholder" setting, disabled by default (feature request: @Matt H.)
* Field: Flexible Content - Added "Layout Title Edition" setting, disabled by default
* Field: Flexible Content - Fixed Enter key closing modal in textarea inputs (thanks @dominikkucharski)
* Field: Flexible Content - Fixed Clone & Copy/Paste functions on select2 fields (Thanks @AsmussenBrandon)
* Field: Flexible Content - Multiple Layouts Categories are now allowed in the Selection Modal, using pipes "|". ie: Main|Shopping|Interactive (Feature request: @Damian P.)
* Field: Flexible Content - Fixed a problem where "Min/Max Layouts" limitation (setting per layout) weren't working properly when using the Layout Selection Modal (Thanks: @Matt H.)
* Module: Taxonomy - Added Polylang compatibility when translating a term (Thanks @jaakkosaarenketo)
* Module: Taxonomy - Fixed spacing when a meta field has no label
* Field: Bidirectional - Values are now saved as string when Post Object & User "Allow multiple values" setting is disabled (Thanks @screamingdev)
* Fields Groups: Added `word-break` on field description
* Fields Groups: Fixed PHP Notice when group location is an attachment (Thanks @herrschuessler)
* General: Added multiples settings in order to disable specific plugin's modules. See FAQ (Feature request: @Matt H.)
* General: Added `ACFE_VERSION` constant to force cache flush on plugin update
* General: PHP Strict Type checks globally (Thanks @Liam S.)
* General: Added Flexible Content Dynamic Preview Video in readme

= ******* =
* Module: Author Box - Hotfix

= ******* =
* Field: Flexible Content - Added `filter('acfe/flexible/thumbnail/name={flexible:name}', $thumbnail, $field, $layout)` to change all layouts thumbnails (must return `attachment ID` or `URL`) (Thanks @Dam)
* Field: Flexible Content - Fixed `$is_preview` not being available during the Dynamic Layout Preview (thanks @Dam)
* Module: Author Box - Added custom authors roles being able to be selected in the Author Box (Thanks @Andremacola)
* General: Fixed Readme typos

= 0.7.9 =
* Field: Flexible Content - Added Inline Layout Title Edition
* Field: Flexible Content - Added Auto scroll + Modal edit on One Click layout
* Field: Flexible Content - Removed native "Controls Icons" visibility being visible on all sub flexible content fields (better readability)
* Field: Flexible Content - Added WP Unslash on preview values to prevent backlashes on values (thanks @Dam)
* Field: Flexible Content - Added compatibility for layouts that have been synced and not manually created (thanks @T. Dubois)
* Field: Flexible Content - Copy/Paste functionality is now a Flexible Content setting (Default: Disabled) (Feature request: @louiswalch)
* Field: Flexible Content - 'Close Button' (collapse) on layouts is now a Flexible Content setting(Default: Disabled)
* Field: Flexible Content - Layouts Thumbnails aspect ratio are now locked (base ratio: 450px * 200px) (Feature request: @louiswalch)
* Field: Flexible Content - Dynamic Layout Preview refresh has been optimized. The preview content is now kept instead of being reset
* Field: Flexible Content - Dynamic Layout Preview style & script enqueue now use wp_enqueue_style() & wp_enqueue_script()
* Field: Flexible Content - Modal Edition - 'Enter' & 'ESC' keys now close Modals (instead of submitting the form)
* Field: Flexible Content - Added `action('acfe/flexible/enqueue', $field, $is_preview)` to enqueue new style/script (back & front) (with 6 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/layout/thumbnail/layout={layout:name}', $thumbnail, $field, $layout)` to change layout thumbnail (must return `attachment ID` or `URL`) (with 3 variations)
* Field: Flexible Content - Added `action('acfe/flexible/preview', $field, $layout)` to change Dynamic Layout Preview content (with 6 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/render/template', $template, $field, $layout, $is_preview)` to change Layout Render: Template Path (with 6 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/render/style', $style, $field, $layout, $is_preview)` to change Layout Render: Style Path (with 6 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/render/script', $script, $field, $layout, $is_preview)` to change Layout Render: Script Path (with 6 variations)
* Field: Flexible Content - Added `filter('acfe/flexible/placeholder/icon', $class, $field)` to change the Placeholder Button Dashicons class (default: 'dashicons dashicons-edit') (with 3 variations)
* Module: Dynamic Options Page - Fixed 'Undefined $post_id' PHP warning in Dynamic Options Page screen
* Module: Dynamic Options Page - Fixed registration order for child options pages (thanks @Val)
* Module: Dynamic Post Type - Fixed undefined ID php Warning on edit screen when Dynamic Post Type is registered locally (thanks @Val)
* Module: Dynamic Taxonomies - Taxonomy name character limit has been fixed to 32 instead of 20 (thanks @Damian)
* Module: Dynamic Taxonomies - 'Add New' button is now based on Taxonomy capabilities & Taxonomy Label (thanks @absolute_web)
* Module: Author - Field groups 'Hide on screen' is now taken in account (thanks @louiswalch)
* Tools: Dynamic Taxonomies Import - Fixed 'undefined index' PHP warning on taxonomy import (thanks @Val)

= 0.7.8 =
* Field: Flexible Content - Removed 'Layouts Thumbnail as Preview' setting. You should now use 'Layouts: Dynamic Preview'
* Field: Flexible Content - Added 'Layouts: Dynamic Preview' ('Layouts: Render' setting must be turned ON)
* Field: Flexible Content - Reworked layouts settings order (better readability)
* Field: Flexible Content - Modal Edition title now removes eventual extra HTML tags (thanks @Thomas D.)
* Field: Flexible Content - Modal Edition CSS has been fixed on Gutenberg Editor view (thanks @Val)
* Field: Flexible Content - Fixed 'Empty Message' placeholder setting using wrong `__()` function (thanks @illiminal)
* Field: Flexible Content - Removed query vars from `get_flexible()`. Global variables `$layout` & `$field` can be used in the template to retrieve current settings
* Field: Flexible Content - Added global variable `$is_preview` which is true when the template file is called as a layout preview
* Field: Flexible Content - `get_flexible()` now uses `wp_enqueue_style()` & `wp_enqueue_script()` when rendering on front-end
* Field: Image - 'No image selected' text has been removed
* Module: Dynamic Post Types/Taxonomies - Fixed 'index key not found' PHP warning (thanks @Val)
* Module: Dynamic Post Types/Taxonomies/Options & Block Types - Added `edit_posts` capabilities matching the ACF capability setting
* Tools: Dynamic Post Type Import - Fixed 'capabilities key not found' PHP warning during import process (thanks @Val)
* General: Improved Metaboxes CSS on Gutenberg Editor views
* General: Reworked JS enqueue. Flexible Content JS is now excluded from ACF Field Groups views

= ******* =
* Field: Flexible Content - Completely revamped Flexible Content JavaScript for a more solid & optimized code
* Field: Flexible Content - Automatically scroll to the layout position when adding a new layout
* Field: Flexible Content - Automatically open layout edition modal when adding a new layout
* Field: Flexible Content - Added 'Close' (collapse) button at the bottom of layout when opened
* Field: Flexible Content - Fixed typo error in the 'Paste Layouts' prompt
* Field: Flexible Content - Added Flexbox CSS compatibility
* Field: Flexible Content - Better Multi Modal Handling (modal inside a modal inside a modal...)
* Field: Flexible Content - Better Field Validation Handling inside layouts
* Field: Flexible Content - Added `has_flexible($field_name, $post_id)` front-end function to check if rows exists
* Field: Flexible Content Control - Automatically scroll to the new layout position when using 'Clone Layout'
* Field: Flexible Content Control - Fixed 'Clone Layout' when an already cloned layout had an 'Editor' field
* Field: Flexible Content Control - Fixed 'Clone Layout' unwanted icon when a layout had an 'Accordion' field
* Field: Advanced Validation/Update - The settings are now hidden on non-necessary fields (Clone, Flexible content, Tabs etc...)
* Module: Dynamic Options Pages - Now forces a unique slug to avoid duplication
* Module: Dynamic Post Types/Taxonomies/Options Pages & Block Types - Manual Json export has been removed from possible actions on the trashed status screen
* Module: Options - Fixed a CSS enqueue problem introduced in last patch
* Location: Post Type Archive & Taxonomy Archive options now use ACF multi-languages settings
* General: Removed jQuery UI & jQuery UI Dialog dependency (ACF Extended now uses its own lightweight modal system)

= 0.7.5 =
* Field: Flexible Content - Added 'Control': Copy, Paste & Duplicate Layouts on the fly using icons in the layouts handle
* Field: Flexible Content - Control: Copy & Paste all layouts on the fly using the new icon next to 'Add row' button (can be used to transfer layout data from one page to an another)
* Field: Flexible Content - Added 'Modal: Edition' setting, allowing to edit layouts in a modal
* Field: Flexible Content - Added 'Layouts Previews' setting, allowing to display the layout thumbnail as preview (collapsed state)
* Field: Flexible Content - Added `filter('acfe/flexible/previews/name=$field_name', $thumbnails, $field)` allowing to override the preview image for each layout (usage example is available in the FAQ)
* Field: Flexible Content - Added `filter('acfe/flexible/previews/key=$field_key', $thumbnails, $field)` allowing to override the preview image for each layout (usage example is available in the FAQ)
* Field: Flexible Content - When using `get_flexible()`, `get_query_var('acf_flexible_field')` & `get_query_var('acf_flexible_layout')` can be used in the template file to retrieve current field & layout information
* Field: Flexible Content - When using `get_flexible()`, an HTML comment has been added for each rendered templates
* Field: Flexible Content - Fixed the possibility to render the same layout multiple times when using `get_flexible()` (thanks to @Val_Pellegrin)
* Field: Flexible Content - `get_flexible()` now enqueue each style.css & script.js only one time on the whole page
* Field: Flexible Content - Added more width spacing for the 'Modal: Category' checkbox (compatibility for small screens)
* Tools: Added Export & Import Tools for Dynamic Post Types, Taxonomies, Block Types & Options Pages using Json files
* Location: Post Type Archive & Taxonomy Archive now use field group location (High, Normal or Side) & field group style (WP Box or seamless) (Feature Request)
* Module: Taxonomy - Added some spacing on the term edition screen (compatibility with YOAST/Rank Math metaboxes)
* Module: Taxonomy - Fixed Edit Screen CSS for Repeaters & Groups (thanks to @Val_Pellegrin)
* Module: Dynamic Taxonomies - Fixed 'Post Type' column when a post type does not exist anymore (thanks to @Val_Pellegrin)
* Module: Dynamic Taxonomies - Fixed Single Posts per page, Orderby & Order
* Module: Dynamic Post Types - Fixed 'Taxonomies' column when a taxonomy does not exist anymore (thanks to @Val_Pellegrin)
* Module: Dynamic Post Types & Taxonomies - Fixed Admin Orderby, Order & Menu position which weren't working properly (thanks to @Val_Pellegrin)
* Module: Dynamic Post Types & Taxonomies - Fixed user Posts per page, Orderby & Order option screen which were forced (thanks to @Val_Pellegrin)
* Field Groups: Hide 'Category' column if there's no term
* Misc: Added 'Advanced Custom Fields' tab in the WP 'Add plugin' page

= 0.7.0.3 =
* Field: Flexible Content - 'Modal: Title' - The custom modal title now works correctly (thanks to Damian P.)
* Field: Flexible Content - 'Layouts State' - Fixed a problem where layouts title were incorrect when forcing layouts state (thanks to Damian P.)
* Compatibility: ACF Pro 5.7.13 - Fixed Archive Location 'All' PHP error (acf/location/rule_match filter)

= 0.7 =
* Field: Flexible Content - Added 'Stylised Button' setting which automatically hide native ACF 'empty' message and add style to 'Add row' button
* Field: Flexible Content - Added 'Hide Empty Message' setting to hide native ACF 'empty' message
* Field: Flexible Content - Added 'Empty Message' text setting to change the native ACF 'click the Add Row button below...' message
* Field: Flexible Content - Added 'Layouts Thumbnails' setting to add image thumbnails for each layout in the admin layout selection
* Field: Flexible Content - Added 'Layouts Render' setting to add template, style & script file for each layout. Those settings can be then accessed on the front-end
* Field: Flexible Content - Added `get_flexible($selector, $post_id)` and `the_flexible($selector, $post_id)` functions to automatically use the 'Layouts Render' settings in front-end
* Field: Flexible Content - Added 'Modal' setting to change the layout selection into a proper modal in the administration
* Field: Flexible Content - Added 'Modal: Title' setting to change the layout modal title
* Field: Flexible Content - Added 'Modal: Columns' setting to change the layout modal columns grid. 1, 2, 3, 4, 5 or 6 columns available
* Field: Flexible Content - Added 'Modal: Categories' setting to add a category for each layout in the layout modal
* Field: Flexible Content - Added 'Layouts State' setting to force layouts to be collapsed or opened by default
* Field: Flexible Content - Added 'Button Label' native compatibility fix to make it work with Dashicons (CSS to fix vertical alignment)
* Field: Flexible Content - Added 'One click' hidden function. In the post administration, the 'Add row' button will add a layout without the selection modal if there is only one layout available in the flexible content
* Field: Flexible Content - Note - The following settings: Layouts Thumbnails, Layouts Render & Modal Categories will be visible after saving field group
* Module: Ajax Author - Fixed a bug where field groups 'Hide on screen' setting wasn't applied on post administration
* Module: Json AutoSync - Added "'/acf-json' folder not found" warning message if Json Sync is set in a field group and the '/acf-json' folder doesn't exist
* Module: Taxonomy - Forced Tabs to be 'Aligned Top' in taxonomies fields (JS Only - ACF Bug) & added better CSS style (thanks to @Val_Pellegrin)
* Module: Dynamic Post Type/Taxonomy/Option Page/Block Type - Hidden 'Minor publishing' panel (Save as draft, visibility...) to avoid confusion (thanks to @Val_Pellegrin)
* Field: Bidirectional - Removed the 'bail early if old values == new values' check. This will let users convert existing fields with saved values into bidirectional without hassle (thanks to @Val_Pellegrin)
* Field: Repeater - Added CSS spacing for block repeaters (better readability)
* Field Group: Location 'Taxonomy All' - Fix native ACF location 'Taxonomy == All' matching all ACF Extended 'Taxonomies Archives' locations
* Compatibility: Added compatibility fix for Rank Math SEO & YOAST Plugin to avoid the plugin's post metabox being above ACF metaboxes

= ******* =
* Field Group: Latest Post Type 'All' location fix was too sensitive. The location now works as expected
* Module: Dynamic Post Types, Taxonomies & Block Types modules now set the 'slug' as disabled once it's saved (to avoid duplication). A more flexible solution will be introduced later (WIP)

= 0.6.7 =
* Module: Added Block Types Module. You can now add, edit and delete Block Types in the ACF > Block Types UI
* Module: Added Options Pages Module. You can now add, edit and delete Options Pages in the ACF > Options UI
* Field Group: Fixed Post Type 'All' location that could render field groups on internal/excluded post types

= 0.6.5 =
* Field: Added 'Featured Thumbnail' setting on image fields. When selected, the field will update the post featured thumbnail
* Field: Fixed bidirectional ON/OFF switch 'width:auto' causing warning with ACF Pro 5.8
* Module: Options - Added support of Json value (introduced by WordPress 5.2 Health Check transients)
* Module: Dynamic Post Type & Taxonomy - Removed 'sanitize_title()' pass on archive & single rewrite settings. Allowing rewrite slugs to be saved as: 'prefix1/prefix2'
* General: Added Gutenberg CSS on post metaboxes. More contrast for better metaboxes integration & visibility

= 0.6.3 =
* Module: Dynamic Post Type & Taxonomy now deregister post types /taxonomies that have been deleted (or trashed) via the Tools > Post Types / Taxonomies
* Module: Dynamic Post Type & Taxonomy now register post types / taxonomies in ASC order
* Module: Dynamic Post Type - Fixed a bug where hierarchical post types had a query error in the admin archive
* General: Improved the ACF Pro dependency style in plugins list when ACF Pro isn't activated
* Plugin: Readme - Reworked structure
* Plugin: Readme - Added Supporters section
* Plugin: Readme - Trying to implement emojis ✌

= 0.6.1 =
* Admin: Re-introduced 'Options' admin screen under Settings > Options. Code has been completely refactored using native WP List Table. New features: Searchbox, item per page preference (default: 100), sortable columns, bulk delete and ability to edit serialized values.

= 0.6.0.2 =
* Field Group: Lowered 'Field Group Data' Metabox priority which was too high and was displayed above fields.

= 0.6.0.1 =
* General: Fixed backward compatibility for ACF Pro 5.7.10. The function: acf_add_filter_variations() was causing problems.
* Admin: Temporarily removed the 'Options Beta' admin screen. Still needs some works. (thanks to @DamChtlv)

= 0.6 =
* Field Group: New location available - Post type archive (under Post type). Field group will be displayed on post type list view, as a sidebar. Fields will be saved in the option: `{post_type}_options`. Frontend usage example: `get_field('my_field', 'page_options')`.
* Field Group: New location available - Taxonomy archive (under Taxonomy). Field group will be displayed on taxonomy list view, as a sidebar. Fields will be saved in the option: `tax_{taxonomy}_options`. Frontend usage example: `get_field('my_field', 'tax_category_options')`.
* Taxonomies: Taxonomies list & edit views have been tweaked for a more consistent administration experience, using CSS/JS only. Views are now similar to post type edition screens.
* Field Groups: Added a 'Third party' status (just like 'Sync available') in order to display local field groups that are loaded by ACF, but not available in the ACF field group administration. Example: a field group is registered locally in the `functions.php` file.
* Dynamic Post Type: Added a configuration button next to the post type title, if the post type was generated by the Dynamic Post Type tool.
* Dynamic Taxonomy: Added a configuration button next to the taxonomy title, if the taxonomy was generated by the Dynamic Taxonomy tool.
* Field Groups: Better 'Load' column data source. Now display: DB, Json or PHP.
* Field Groups: Now forcing Json / PHP Sync if local files are loaded by ACF. In order to disable it, and if the setting is already enabled, you must manually delete the `group_xxxxxxxxx` file in your theme folder. This behavior is applied to avoid any data desync.
* Field: Fixed a PHP notice in the Advanced Validation setting update.
* Field Groups: Taxonomy acf-field-group-category - Better exclusion from ACF taxonomy selection (location & fields)

= ******* =
* Plugin: Less aggressive ACF Pro check on activation. Now displaying a notice (allowing pre-activation of ACF Extended)
* Plugin: Readme text fix

= 0.5.8 =
* Field: Added Bidirectional setting for the following fields: relationship, post object, user & taxonomy terms
* Module: Added 'Ajax Author' field to replace the native WP Author Meta Box
* Module: Dynamic Post Type & Taxonomy - Better exclusion from ACF post types selection (location & fields)
* General: Fixed ACF Select2 CSS to fit ACF input styles (border-radius, border-color & line-height)
* General: Renamed ACF-Extended assets for better readability in the browser console resources tab
* Compatibility: Removed the Taxonomy Order submenu created under ACF for the taxonomy 'Field Group Category' by the plugin 'Category Order and Taxonomy Terms Order'

= ******* =
* Module: Dynamic Taxonomy - Fixed Terms PHP warning
* General: Plugin readme

= 0.5.5 =
* Module: Added Dynamic Post Type module
* Module: Added Dynamic Taxonomy module
* Admin: Added WP Options page
* Field: Added Post Type Selection field
* Field: Added Taxonomy Selection field
* Field: Added Slug field
* Field Groups: Fixed 'no field groups found' wrong colspan
* General: Reworked plugin folders and files hierarchy

= ******* =
* Field Groups: Fixed unused category column on Field Groups Sync page
* Fields: Fixed subfields 'ghost' acfcloneindex saved when duplicating flexible content (thanks to @AsmussenBrandon)

= ******* =
* Field Group: Fixed Left Label Placement overwriting existing field groups (thanks to @AsmussenBrandon)

= 0.5.2 =
* Fields: Added new dynamic message field
* Fields: Added new button field
* General: Added compatibility filters for 'Post Types Order' plugin
* Plugin: Updated assets
* Plugin: Reworked readme
* Plugin: Fixed typos

= 0.5.1 =
* Plugin: Added screenshots
* Field Group: Moved Auto Sync Warnings below Auto Sync instructions
* Field: Added filters variation to `acfe/validate` & `acfe/update`

= 0.5 =
* Initial release