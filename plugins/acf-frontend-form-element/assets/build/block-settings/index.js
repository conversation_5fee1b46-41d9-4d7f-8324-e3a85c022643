(()=>{"use strict";var t={251:(t,e,r)=>{var o=r(196),n=Symbol.for("react.element"),s=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,r){var o,c={},a=null,d=null;for(o in void 0!==r&&(a=""+r),void 0!==e.key&&(a=""+e.key),void 0!==e.ref&&(d=e.ref),e)s.call(e,o)&&!u.hasOwnProperty(o)&&(c[o]=e[o]);if(t&&t.defaultProps)for(o in e=t.defaultProps)void 0===c[o]&&(c[o]=e[o]);return{$$typeof:n,type:t,key:a,ref:d,props:c,_owner:i.current}}e.jsx=c,e.jsxs=c},893:(t,e,r)=>{t.exports=r(251)},196:t=>{t.exports=window.React}},e={};function r(o){var n=e[o];if(void 0!==n)return n.exports;var s=e[o]={exports:{}};return t[o](s,s.exports,r),s.exports}(()=>{var t=r(893);const e=window.wp.hooks,o=window.wp.element,n=window.wp.blockEditor,s=window.wp.components,i=window.wp.compose;(0,e.addFilter)("blocks.registerBlockType","frontend-admin/button-custom-attribute",(function(t,e){return"core/button"===e&&(t.attributes=Object.assign(Object.assign({},t.attributes),{submitButton:{type:"boolean",default:!1}})),t}));var u=(0,i.createHigherOrderComponent)((function(e){return function(r){if("core/button"!==r.name)return(0,t.jsx)(e,Object.assign({},r));var i=r.attributes,u=r.setAttributes,c=i.submitButton;return(0,t.jsxs)(o.Fragment,{children:[(0,t.jsx)(e,Object.assign({},r)),(0,t.jsx)(n.InspectorControls,{children:(0,t.jsx)(s.PanelBody,{title:"Frontend Admin",children:(0,t.jsx)(s.ToggleControl,{label:"Submit Button",checked:c,onChange:function(t){return u({submitButton:t})}})})})]})}}),"withInspectorControls");(0,e.addFilter)("editor.BlockEdit","frontend-admin/with-inspector-controls",u)})()})();