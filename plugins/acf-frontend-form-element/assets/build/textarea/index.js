(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,a=window.wp.blockEditor,n=window.wp.components,l=function(e){var n=e.attributes,l=e.setAttributes,r=n.label,c=n.hide_label,o=n.required,i=n.instructions;return React.createElement("div",{className:"acf-field"},React.createElement("div",{className:"acf-label"},React.createElement("label",null,!c&&React.createElement(a.RichText,{tagName:"label",onChange:function(e){return l({label:e})},withoutInteractiveFormatting:!0,placeholder:(0,t.__)("Text Field","acf-frontend-form-element"),value:r}),o&&React.createElement("span",{className:"acf-required"},"*"))),React.createElement("div",{className:"acf-input"},i&&React.createElement(a.<PERSON>ex<PERSON>,{tagName:"p",className:"description",onChange:function(e){return l({instructions:e})},withoutInteractiveFormatting:!0,value:i}),React.createElement("div",{className:"acf-input-wrap",style:{display:"flex",width:"100%"}},e.children)))},r=window.React;var c="acf-frontend-form-element";const o=function(e){var l=e.attributes,o=e.setAttributes,i=l.label,u=l.hide_label,s=l.required,d=l.instructions,f=function(e){return e.toLowerCase().replace(/[^a-z0-9 _]/g,"").replace(/\s+/g,"_")};return(0,r.useEffect)((function(){"field_key"in l&&!l.field_key&&o({field_key:Math.random().toString(36).substring(2,10)})}),[]),React.createElement(a.InspectorControls,{field_key:"fea-inspector-controls"},React.createElement(n.PanelBody,{title:(0,t.__)("General",c),initialOpen:!0},React.createElement(n.TextControl,{label:(0,t.__)("Label",c),value:i,onChange:function(e){return o({label:e})}}),React.createElement(n.ToggleControl,{label:(0,t.__)("Hide Label",c),checked:u,onChange:function(e){return o({hide_label:e})}}),"name"in l&&React.createElement(n.TextControl,{label:(0,t.__)("Name",c),value:l.name||f(i),onChange:function(e){return o({name:f(e)})}}),"field_key"in l&&React.createElement(n.TextControl,{label:(0,t.__)("Field Key",c),value:l.field_key,readOnly:!0,onChange:function(e){}}),React.createElement(n.TextareaControl,{label:(0,t.__)("Instructions",c),rows:"3",value:d,onChange:function(e){return o({instructions:e})}}),React.createElement(n.ToggleControl,{label:(0,t.__)("Required",c),checked:s,onChange:function(e){return o({required:e})}}),e.children))};var i="acf-frontend-form-element";const u=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"frontend-admin/textarea","title":"Textarea","description":"Displays a textarea.","category":"frontend-admin","textdomain":"frontend-admin","supports":{"align":["wide"]},"attributes":{"field_key":{"type":"string","default":""},"name":{"type":"string","default":""},"label":{"type":"string","default":"Textarea"},"hide_label":{"type":"boolean","default":""},"required":{"type":"boolean","default":""},"default_value":{"type":"string","default":""},"placeholder":{"type":"string","default":""},"instructions":{"type":"string","default":""},"maxlength":{"type":"number","default":""},"rows":{"type":"number","default":""}},"editorScript":"file:../../textarea/index.js"}');(0,e.registerBlockType)(u,{edit:function(e){var r=e.attributes,c=e.setAttributes,u=r.default_value,s=r.placeholder,d=r.maxlength,f=r.rows,m=(r.cols,(0,a.useBlockProps)());return React.createElement("div",m,React.createElement(o,e,React.createElement(n.TextareaControl,{label:(0,t.__)("Default Value",i),value:u,onChange:function(e){return c({default_value:e})}}),React.createElement(n.TextareaControl,{label:(0,t.__)("Placeholder",i),value:s,onChange:function(e){return c({placeholder:e})}}),React.createElement(n.TextControl,{type:"number",label:(0,t.__)("Character Limit",i),value:d,onChange:function(e){return c({maxlength:e})}}),React.createElement(n.TextControl,{type:"number",label:(0,t.__)("Rows",i),value:f,onChange:function(e){return c({rows:e})}})),React.createElement(l,e,React.createElement("textarea",{maxLength:d,placeholder:s,rows:f,value:u,onChange:function(e){c({default_value:e.target.value})}},u)))},save:function(){return null}})})();