/*!***************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./src/advanced-custom-fields-pro/assets/src/sass/acf-dark.scss ***!
  \***************************************************************************************************************************************************************************************************************/
/*--------------------------------------------------------------------------------------------
*
*  Dark mode
*
*  WordPress plugin: https://en-au.wordpress.org/plugins/dark-mode/
*  Github Documentation: https://github.com/danieltj27/Dark-Mode/wiki/Help:-Plugin-Compatibility-Guide
*
*--------------------------------------------------------------------------------------------*/
/*---------------------------------------------------------------------------------------------
*
*  Global
*
*---------------------------------------------------------------------------------------------*/
.acf-box {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}
.acf-box .title,
.acf-box .footer {
  border-color: #23282d;
}
.acf-box h2 {
  color: #bbc8d4;
}
.acf-box table, .acf-box tbody, .acf-box tr {
  background: transparent !important;
}

.acf-thead {
  color: #bbc8d4;
  border-color: #191f25;
}

.acf-tfoot {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-table.-clear,
.acf-table.-clear tr {
  background: transparent !important;
}

.acf-loading-overlay {
  background: rgba(0, 0, 0, 0.5);
}

/*---------------------------------------------------------------------------------------------
*
*  Fields
*
*---------------------------------------------------------------------------------------------*/
.acf-fields > .acf-field {
  border-color: #23282d;
}

.acf-fields.-left > .acf-field:before {
  background: rgba(0, 0, 0, 0.1);
  border-color: #23282d;
}

.acf-fields.-border {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}

.acf-field[data-width] + .acf-field[data-width] {
  border-color: #23282d;
}

.acf-input-prepend,
.acf-input-append {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}

.acf-fields > .acf-tab-wrap {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}
.acf-fields > .acf-tab-wrap .acf-tab-group {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-fields > .acf-tab-wrap .acf-tab-group li a {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-fields > .acf-tab-wrap .acf-tab-group li a:hover {
  background-color: #2d3136;
  border-color: #23282d;
  color: #bbc8d4;
}
.acf-fields > .acf-tab-wrap .acf-tab-group li.active a {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}

.acf-fields.-sidebar:before {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-fields.-sidebar.-left:before {
  background-color: #2d3136;
  border-color: #23282d;
  background: #23282d;
}
.acf-fields.-sidebar.-left > .acf-tab-wrap.-left .acf-tab-group li a {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-fields.-sidebar.-left > .acf-tab-wrap.-left .acf-tab-group li.active a {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-file-uploader .show-if-value {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}
.acf-file-uploader .show-if-value .file-icon {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-oembed {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-oembed .title {
  background-color: #50626f;
  border-color: #191f25;
  color: #fff;
}

.acf-gallery {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-gallery .acf-gallery-main {
  background: #23282d;
}
.acf-gallery .acf-gallery-attachment .margin {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-gallery .acf-gallery-side {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-gallery .acf-gallery-side .acf-gallery-side-info {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-gallery .acf-gallery-toolbar {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-button-group label:not(.selected) {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-switch:not(.-on) {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-switch:not(.-on) .acf-switch-slider {
  background-color: #50626f;
  border-color: #191f25;
  color: #fff;
}

.acf-link .link-wrap {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-relationship .filters {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}
.acf-relationship .selection {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-relationship .selection .choices,
.acf-relationship .selection .choices-list,
.acf-relationship .selection .values {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-taxonomy-field .categorychecklist-holder {
  background-color: #2d3136;
  border-color: #23282d;
}

.acf-google-map {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-google-map .title {
  background-color: #50626f;
  border-color: #191f25;
  color: #fff;
}

.acf-accordion {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}

.acf-field.acf-accordion .acf-accordion-content > .acf-fields {
  border-color: #191f25;
}

.acf-flexible-content .layout {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}
.acf-flexible-content .layout .acf-fc-layout-handle {
  background-color: #2d3136;
  border-color: #23282d;
}
.acf-flexible-content .layout .acf-fc-layout-handle .acf-fc-layout-order {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}

#wpbody .acf-table {
  background-color: #2d3136;
  border-color: #23282d;
}
#wpbody .acf-table > tbody > tr,
#wpbody .acf-table > thead > tr {
  background: transparent;
}
#wpbody .acf-table > tbody > tr > td,
#wpbody .acf-table > tbody > tr > th,
#wpbody .acf-table > thead > tr > td,
#wpbody .acf-table > thead > tr > th {
  border-color: #191f25;
}

.acf-field select optgroup, .acf-field select optgroup:nth-child(2n) {
  background: #50626f;
}

/*---------------------------------------------------------------------------------------------
*
*  Field Group
*
*---------------------------------------------------------------------------------------------*/
#acf-field-group-fields .acf-field-list-wrap {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}
#acf-field-group-fields .acf-field-list .no-fields-message {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
}
#acf-field-group-fields .acf-field-object {
  background-color: #32373c;
  border-color: #191f25;
  color: #bbc8d4;
  border-color: #23282d;
}
#acf-field-group-fields .acf-field-object table, #acf-field-group-fields .acf-field-object tbody, #acf-field-group-fields .acf-field-object tr, #acf-field-group-fields .acf-field-object td, #acf-field-group-fields .acf-field-object th {
  background: transparent;
  border-color: #23282d;
}
#acf-field-group-fields .acf-field-object .acf-field .acf-label {
  background-color: #2d3136;
  border-color: #23282d;
}
#acf-field-group-fields .acf-field-object.ui-sortable-helper {
  border-color: #191f25;
  box-shadow: none;
}
#acf-field-group-fields .acf-field-object.ui-sortable-placeholder {
  background-color: #2d3136;
  border-color: #23282d;
  box-shadow: none;
}
#acf-field-group-fields .acf-field-object + .acf-field-object-tab::before,
#acf-field-group-fields .acf-field-object + .acf-field-object-accordion::before {
  background-color: #2d3136;
  border-color: #23282d;
}

/*---------------------------------------------------------------------------------------------
*
*  Admin: Tools
*
*---------------------------------------------------------------------------------------------*/
.acf-meta-box-wrap .acf-fields {
  background-color: #50626f;
  border-color: #191f25;
  color: #fff;
  background: transparent;
}

/*# sourceMappingURL=acf-dark.css.map*/