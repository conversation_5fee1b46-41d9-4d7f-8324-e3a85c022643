{"version": 3, "file": "acf-dark.css", "mappings": ";;;AAAA;;;;;;;8FAAA;AAqFA;;;;+FAAA;AAOA;EAzBC,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;ACvBf;AD8EC;;EAnBA,qBA/BmB;ACxBpB;AD+EC;EA7CA,cAfc;AChBf;ADgFC;EACC;AC9EF;;ADmFA;EAvDC,cAfc;EAkBd,qBAdkB;ACZnB;;ADkFA;EA1CC;EACA,qBA5BmB;ACRpB;;ADkFA;;EAEC;AC/ED;;ADmFA;EACC;AChFD;;ADoFA;;;;+FAAA;AAUC;EAhEA,qBA/BmB;ACUpB;;AD8FE;EACC;EA1EF,qBA/BmB;ACepB;;ADiGA;EA1FC,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;AC0Bf;;ADgGA;EAtFC,qBA/BmB;ACyBpB;;ADiGA;;EApGC,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;ACqCf;;ADoGA;EA9GC,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;AC2Cf;ADiGC;EA5GA;EACA,qBA5BmB;AC0CpB;ADiGG;EAhHF;EACA,qBA5BmB;AC8CpB;ADgGI;EAnHH;EACA,qBA5BmB;EAOnB,cAZc;ACwDf;ADiGG;EA9HF,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;AC6Df;;ADoGC;EAjIA;EACA,qBA5BmB;AC6DpB;;ADqGC;EAvIA;EACA,qBA5BmB;EAoKlB,mBAxKkB;ACuEpB;ADoGE;EA5ID;EACA,qBA5BmB;ACuEpB;ADoGE;EAhJD;EACA,qBA5BmB;AC2EpB;;ADyGC;EA9JA,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;ACsFf;ADsGE;EA5JD;EACA,qBA5BmB;ACqFpB;;ADyGA;EAnKC;EACA,qBA5BmB;AC0FpB;ADuGC;EA/JA,yBAjCoB;EAkCpB,qBAhCiB;EAiCjB,WAlCe;AC6FhB;;ADwGA;EA5KC;EACA,qBA5BmB;ACoGpB;ADsGC;EA1LA,mBApBmB;AC2GpB;ADwGE;EApLD;EACA,qBA5BmB;AC2GpB;ADyGC;EAzLA;EACA,qBA5BmB;AC+GpB;ADwGE;EA5LD;EACA,qBA5BmB;ACmHpB;ADyGC;EAjMA;EACA,qBA5BmB;ACuHpB;;AD6GC;EAzMA;EACA,qBA5BmB;AC4HpB;;AD8GA;EA/MC;EACA,qBA5BmB;ACiIpB;AD2GC;EA1MA,yBAjCoB;EAkCpB,qBAhCiB;EAiCjB,WAlCe;ACoIhB;;AD4GA;EAvNC;EACA,qBA5BmB;AC2IpB;;AD6GC;EAlOA,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;ACsJf;AD0GC;EAhOA;EACA,qBA5BmB;ACqJpB;ADwGE;;;EAlOD;EACA,qBA5BmB;AC2JpB;;AD2GA;EA3OC;EACA,qBA5BmB;ACgKpB;;AD2GA;EAhPC;EACA,qBA5BmB;ACqKpB;ADyGC;EA5OA,yBAjCoB;EAkCpB,qBAhCiB;EAiCjB,WAlCe;ACwKhB;;AD0GA;EA9PC,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;ACqLf;;ADuGA;EA1QC,qBAdkB;ACqLnB;;ADyGC;EAvQA,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;AC+Lf;ADsGE;EArQD;EACA,qBA5BmB;AC8LpB;ADqGG;EA7QF,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;ACwMf;;ADwGA;EAhRC;EACA,qBA5BmB;ACwMpB;ADwGE;;EACC;ACrGH;ADuGG;;;;EAtSF,qBAdkB;ACmNnB;;AD2GC;EACC,mBA7TmB;ACqNrB;;AD4GA;;;;+FAAA;AAUC;EAtTA,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;ACsOf;ADgHE;EA3TD,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;AC2Of;ADiHC;EAjUA,yBAxBqB;EAyBrB,qBAxBkB;EAyBlB,cA7Bc;EAoCd,qBA/BmB;AC4OpB;ADgHE;EACC;EA9TF,qBA/BmB;ACgPpB;ADkHG;EAvUF;EACA,qBA5BmB;ACoPpB;ADoHE;EA3VD,qBAdkB;EA2WhB;AClHH;ADqHE;EAlVD;EACA,qBA5BmB;EA+WjB;AClHH;ADsHC;;EAxVA;EACA,qBA5BmB;ACkQpB;;ADwHA;;;;+FAAA;AASC;EAjWA,yBAjCoB;EAkCpB,qBAhCiB;EAiCjB,WAlCe;EAmYd;ACvHF,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_dark.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/acf-dark.scss"], "sourcesContent": ["/*--------------------------------------------------------------------------------------------\n*\n*  Dark mode\n*\n*  WordPress plugin: https://en-au.wordpress.org/plugins/dark-mode/\n*  Github Documentation: https://github.com/danieltj27/Dark-Mode/wiki/Help:-Plugin-Compatibility-Guide\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Dark Mode Colours.\n$white:           #ffffff;\n$black:           #000000;\n$blue:            #0073aa;\n$medium-blue:     #00a0d2;\n$clear:           transparent;\n\n$accent-red:      #dc3232;\n$accent-orange:   #f56e28;\n$accent-yellow:   #ffb900;\n$accent-green:    #46b450;\n$accent-blue:     $blue;\n$accent-purple:   #826eb4;\n\n$base-grey:       #23282d;\n$light-grey:      #bbc8d4;\n$heavy-grey:      #37444c;\n$dark-grey:       #32373c;\n$ultra-grey:      #191f25;\n$dark-silver:     #50626f;\n$base-blue:       #2e74aa;\n$light-blue:      #4092d2;\n$dark-blue:       #2c5f88;\n$ultra-blue:      #1f3f58;\n$bright-blue:     #30ceff;\n\n$editor-lavender: #c678dd;\n$editor-sunglo:   #e06c75;\n$editor-olivine:  #98c379;\n\n// Custom variables.\n$body_text: \t\t\t#bbc8d4;\n$body_background: \t\t#23282d;\n$body_background2: \t\t#191f25;\n$postbox_background: \t#32373c;\n$postbox_border: \t\t#191f25;\n$postbox_divider: \t\t#23282d;\n$input_background: \t\t#50626f;\n$input_text: \t\t\t#fff;\n$input_border: \t\t\t#191f25;\n\n// Mixins.\n@mixin dark-text() {\n\tcolor: $body_text;\n}\n@mixin dark-heading() {\n\tcolor: $body_text;\n}\n@mixin dark-border() {\n\tborder-color: $postbox_border;\n}\n@mixin dark-background() {\n\tbackground: $body_background;\n}\n@mixin darker-background() {\n\tbackground: darken($body_background, 5%);\n}\n@mixin dark-postbox() {\n\tbackground-color: $postbox_background;\n\tborder-color: $postbox_border;\n\tcolor: $body_text;\n}\n@mixin dark-postbox-block() {\n\tbackground-color: #2d3136;\n\tborder-color: $postbox_divider;\n}\n@mixin dark-divider() {\n\tborder-color: $postbox_divider;\n}\n@mixin dark-input() {\n\tbackground-color: $input_background;\n\tborder-color: $input_border;\n\tcolor: $input_text;\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n\n// acf-box\n.acf-box {\n\t@include dark-postbox();\n\n\t.title,\n\t.footer {\n\t\t@include dark-divider();\n\t}\n\n\th2 {\n\t\t@include dark-heading();\n\t}\n\n\ttable, tbody, tr {\n\t\tbackground: transparent !important;\n\t}\n}\n\n// thead\n.acf-thead {\n\t@include dark-heading();\n\t@include dark-border();\n}\n.acf-tfoot {\n\t@include dark-postbox-block();\n}\n\n// table clear\n.acf-table.-clear,\n.acf-table.-clear tr {\n\tbackground: transparent !important;\n}\n\n// loading overlay\n.acf-loading-overlay {\n\tbackground: rgba(0,0,0,0.5);\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Fields\n*\n*---------------------------------------------------------------------------------------------*/\n\n// fields\n.acf-fields {\n\n\t// field\n\t> .acf-field {\n\t\t@include dark-divider();\n\t}\n}\n\n// fields (left)\n.acf-fields.-left {\n\n\t> .acf-field {\n\t\t&:before {\n\t\t\tbackground: rgba(0,0,0,0.1);\n\t\t\t@include dark-divider();\n\t\t}\n\t}\n}\n\n// fields (border)\n.acf-fields.-border {\n\t@include dark-postbox();\n}\n\n// width\n.acf-field[data-width] + .acf-field[data-width] {\n\t@include dark-divider();\n}\n\n// text\n.acf-input-prepend,\n.acf-input-append {\n\t@include dark-postbox();\n}\n\n// tab\n.acf-tab-wrap {\n\n}\n\n.acf-fields > .acf-tab-wrap {\n\t@include dark-postbox();\n\n\t.acf-tab-group {\n\t\t@include dark-postbox-block();\n\n\t\tli {\n\t\t\ta {\n\t\t\t\t@include dark-postbox-block();\n\n\t\t\t\t&:hover {\n\t\t\t\t\t@include dark-postbox-block();\n\t\t\t\t\t@include dark-text();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.active a {\n\t\t\t\t@include dark-postbox();\n\t\t\t}\n\t\t}\n\t}\n}\n\n.acf-fields.-sidebar {\n\t&:before {\n\t\t@include dark-postbox-block();\n\t}\n}\n\n.acf-fields.-sidebar.-left {\n\t&:before {\n\t\t@include dark-postbox-block();\n\t\tbackground: $body_background;\n\t}\n\t> .acf-tab-wrap.-left {\n\t\t.acf-tab-group li a {\n\t\t\t@include dark-postbox-block();\n\t\t}\n\n\t\t.acf-tab-group li.active a {\n\t\t\t@include dark-postbox-block();\n\t\t}\n\t}\n}\n\n// file\n.acf-file-uploader {\n\n\t.show-if-value {\n\t\t@include dark-postbox();\n\n\t\t.file-icon {\n\t\t\t@include dark-postbox-block();\n\t\t}\n\t}\n}\n\n// acf-oembed\n.acf-oembed {\n\t@include dark-postbox-block();\n\n\t.title {\n\t\t@include dark-input();\n\t}\n}\n\n// gallery\n.acf-gallery {\n\t@include dark-postbox-block();\n\n\t.acf-gallery-main {\n\t\t@include dark-background();\n\t}\n\n\t.acf-gallery-attachment {\n\t\t.margin {\n\t\t\t@include dark-postbox-block();\n\t\t}\n\t}\n\n\t.acf-gallery-side {\n\t\t@include dark-postbox-block();\n\n\t\t.acf-gallery-side-info {\n\t\t\t@include dark-postbox-block();\n\t\t}\n\t}\n\n\t.acf-gallery-toolbar {\n\t\t@include dark-postbox-block();\n\t}\n}\n\n// button group\n.acf-button-group {\n\n\tlabel:not(.selected) {\n\t\t@include dark-postbox-block();\n\t}\n}\n\n// switch\n.acf-switch:not(.-on) {\n\t@include dark-postbox-block();\n\t.acf-switch-slider {\n\t\t@include dark-input();\n\t}\n}\n\n// link\n.acf-link .link-wrap {\n\t@include dark-postbox-block();\n}\n\n// relationship\n.acf-relationship {\n\t.filters {\n\t\t@include dark-postbox();\n\t}\n\t.selection {\n\t\t@include dark-postbox-block();\n\t\t.choices,\n\t\t.choices-list,\n\t\t.values {\n\t\t\t@include dark-postbox-block();\n\t\t}\n\t}\n}\n\n// checkbox\n.acf-taxonomy-field .categorychecklist-holder {\n\t@include dark-postbox-block();\n}\n\n// google map\n.acf-google-map {\n\t@include dark-postbox-block();\n\n\t.title {\n\t\t@include dark-input();\n\t}\n}\n\n// accordion\n.acf-accordion {\n\t@include dark-postbox();\n}\n.acf-field.acf-accordion .acf-accordion-content > .acf-fields {\n\t@include dark-border();\n}\n\n// flexible content\n.acf-flexible-content {\n\t.layout {\n\t\t@include dark-postbox();\n\n\t\t.acf-fc-layout-handle {\n\t\t\t@include dark-postbox-block();\n\n\t\t\t.acf-fc-layout-order {\n\t\t\t\t@include dark-postbox();\n\t\t\t}\n\t\t}\n\t}\n}\n\n// repeater\n#wpbody .acf-table {\n\t@include dark-postbox-block();\n\n\t> tbody,\n\t> thead {\n\t\t> tr {\n\t\t\tbackground: transparent;\n\n\t\t\t> td,\n\t\t\t> th {\n\t\t\t\t@include dark-border();\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Select\n.acf-field select {\n\toptgroup, optgroup:nth-child(2n) {\n\t\tbackground: $input_background;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field Group\n*\n*---------------------------------------------------------------------------------------------*/\n\n// fields\n#acf-field-group-fields {\n\n\t// field list\n\t.acf-field-list-wrap {\n\t\t@include dark-postbox();\n\t}\n\n\t.acf-field-list {\n\t\t.no-fields-message {\n\t\t\t@include dark-postbox();\n\t\t}\n\t}\n\n\t// field\n\t.acf-field-object {\n\t\t@include dark-postbox();\n\t\t@include dark-divider();\n\n\n\t\ttable, tbody, tr, td, th {\n\t\t\tbackground: transparent;\n\t\t\t@include dark-divider();\n\t\t}\n\n\t\t.acf-field {\n\t\t\t.acf-label {\n\t\t\t\t@include dark-postbox-block();\n\t\t\t}\n\t\t}\n\n\t\t// sortable\n\t\t&.ui-sortable-helper {\n\t\t\t@include dark-border();\n\t\t\tbox-shadow: none;\n\t\t}\n\n\t\t&.ui-sortable-placeholder {\n\t\t\t@include dark-postbox-block();\n\t\t\tbox-shadow: none;\n\t\t}\n\t}\n\n\t.acf-field-object + .acf-field-object-tab::before,\n\t.acf-field-object + .acf-field-object-accordion::before {\n\t\t@include dark-postbox-block();\n\t}\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin: Tools\n*\n*---------------------------------------------------------------------------------------------*/\n\n// tools\n.acf-meta-box-wrap {\n\n\t.acf-fields {\n\t\t@include dark-input();\n\t\tbackground: transparent;\n\t}\n}", "/*--------------------------------------------------------------------------------------------\n*\n*  Dark mode\n*\n*  WordPress plugin: https://en-au.wordpress.org/plugins/dark-mode/\n*  Github Documentation: https://github.com/danieltj27/Dark-Mode/wiki/Help:-Plugin-Compatibility-Guide\n*\n*--------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-box {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n.acf-box .title,\n.acf-box .footer {\n  border-color: #23282d;\n}\n.acf-box h2 {\n  color: #bbc8d4;\n}\n.acf-box table, .acf-box tbody, .acf-box tr {\n  background: transparent !important;\n}\n\n.acf-thead {\n  color: #bbc8d4;\n  border-color: #191f25;\n}\n\n.acf-tfoot {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-table.-clear,\n.acf-table.-clear tr {\n  background: transparent !important;\n}\n\n.acf-loading-overlay {\n  background: rgba(0, 0, 0, 0.5);\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Fields\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-fields > .acf-field {\n  border-color: #23282d;\n}\n\n.acf-fields.-left > .acf-field:before {\n  background: rgba(0, 0, 0, 0.1);\n  border-color: #23282d;\n}\n\n.acf-fields.-border {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n\n.acf-field[data-width] + .acf-field[data-width] {\n  border-color: #23282d;\n}\n\n.acf-input-prepend,\n.acf-input-append {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n\n.acf-fields > .acf-tab-wrap {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n.acf-fields > .acf-tab-wrap .acf-tab-group {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-fields > .acf-tab-wrap .acf-tab-group li a {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-fields > .acf-tab-wrap .acf-tab-group li a:hover {\n  background-color: #2d3136;\n  border-color: #23282d;\n  color: #bbc8d4;\n}\n.acf-fields > .acf-tab-wrap .acf-tab-group li.active a {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n\n.acf-fields.-sidebar:before {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-fields.-sidebar.-left:before {\n  background-color: #2d3136;\n  border-color: #23282d;\n  background: #23282d;\n}\n.acf-fields.-sidebar.-left > .acf-tab-wrap.-left .acf-tab-group li a {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-fields.-sidebar.-left > .acf-tab-wrap.-left .acf-tab-group li.active a {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-file-uploader .show-if-value {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n.acf-file-uploader .show-if-value .file-icon {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-oembed {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-oembed .title {\n  background-color: #50626f;\n  border-color: #191f25;\n  color: #fff;\n}\n\n.acf-gallery {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-gallery .acf-gallery-main {\n  background: #23282d;\n}\n.acf-gallery .acf-gallery-attachment .margin {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-gallery .acf-gallery-side {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-gallery .acf-gallery-side .acf-gallery-side-info {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-gallery .acf-gallery-toolbar {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-button-group label:not(.selected) {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-switch:not(.-on) {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-switch:not(.-on) .acf-switch-slider {\n  background-color: #50626f;\n  border-color: #191f25;\n  color: #fff;\n}\n\n.acf-link .link-wrap {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-relationship .filters {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n.acf-relationship .selection {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-relationship .selection .choices,\n.acf-relationship .selection .choices-list,\n.acf-relationship .selection .values {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-taxonomy-field .categorychecklist-holder {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n.acf-google-map {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-google-map .title {\n  background-color: #50626f;\n  border-color: #191f25;\n  color: #fff;\n}\n\n.acf-accordion {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n\n.acf-field.acf-accordion .acf-accordion-content > .acf-fields {\n  border-color: #191f25;\n}\n\n.acf-flexible-content .layout {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n.acf-flexible-content .layout .acf-fc-layout-handle {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n.acf-flexible-content .layout .acf-fc-layout-handle .acf-fc-layout-order {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n\n#wpbody .acf-table {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n#wpbody .acf-table > tbody > tr,\n#wpbody .acf-table > thead > tr {\n  background: transparent;\n}\n#wpbody .acf-table > tbody > tr > td,\n#wpbody .acf-table > tbody > tr > th,\n#wpbody .acf-table > thead > tr > td,\n#wpbody .acf-table > thead > tr > th {\n  border-color: #191f25;\n}\n\n.acf-field select optgroup, .acf-field select optgroup:nth-child(2n) {\n  background: #50626f;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field Group\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-field-group-fields .acf-field-list-wrap {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n#acf-field-group-fields .acf-field-list .no-fields-message {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n}\n#acf-field-group-fields .acf-field-object {\n  background-color: #32373c;\n  border-color: #191f25;\n  color: #bbc8d4;\n  border-color: #23282d;\n}\n#acf-field-group-fields .acf-field-object table, #acf-field-group-fields .acf-field-object tbody, #acf-field-group-fields .acf-field-object tr, #acf-field-group-fields .acf-field-object td, #acf-field-group-fields .acf-field-object th {\n  background: transparent;\n  border-color: #23282d;\n}\n#acf-field-group-fields .acf-field-object .acf-field .acf-label {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n#acf-field-group-fields .acf-field-object.ui-sortable-helper {\n  border-color: #191f25;\n  box-shadow: none;\n}\n#acf-field-group-fields .acf-field-object.ui-sortable-placeholder {\n  background-color: #2d3136;\n  border-color: #23282d;\n  box-shadow: none;\n}\n#acf-field-group-fields .acf-field-object + .acf-field-object-tab::before,\n#acf-field-group-fields .acf-field-object + .acf-field-object-accordion::before {\n  background-color: #2d3136;\n  border-color: #23282d;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin: Tools\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-meta-box-wrap .acf-fields {\n  background-color: #50626f;\n  border-color: #191f25;\n  color: #fff;\n  background: transparent;\n}"], "names": [], "sourceRoot": ""}