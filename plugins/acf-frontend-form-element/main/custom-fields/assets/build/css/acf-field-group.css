/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./src/advanced-custom-fields-pro/assets/src/sass/acf-field-group.scss ***!
  \**********************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/*--------------------------------------------------------------------------------------------
*
*	Vars
*
*--------------------------------------------------------------------------------------------*/
/* colors */
/* acf-field */
/* responsive */
/*--------------------------------------------------------------------------------------------
*
*	ACF 6 ↓
*
*--------------------------------------------------------------------------------------------*/
/*--------------------------------------------------------------------------------------------
*
*  Mixins
*
*--------------------------------------------------------------------------------------------*/
/*--------------------------------------------------------------------------------------------
*
*	Field Group
*
*--------------------------------------------------------------------------------------------*/
#acf-field-group-fields > .inside,
#acf-field-group-locations > .inside,
#acf-field-group-options > .inside {
  padding: 0;
  margin: 0;
}

.postbox .handle-order-higher,
.postbox .handle-order-lower {
  display: none;
}

/*----------------------------------------------------------------------------
*
*  Postbox: Publish
*
*----------------------------------------------------------------------------*/
#minor-publishing-actions,
#misc-publishing-actions #visibility,
#misc-publishing-actions .edit-timestamp {
  display: none;
}

#minor-publishing {
  border-bottom: 0 none;
}

#misc-pub-section {
  border-bottom: 0 none;
}

#misc-publishing-actions .misc-pub-section {
  border-bottom-color: #F5F5F5;
}

/*----------------------------------------------------------------------------
*
*  Postbox: Fields
*
*----------------------------------------------------------------------------*/
#acf-field-group-fields {
  border: 0 none;
  /* links */
  /* Field type */
  /* table header */
  /* show keys */
  /* hide tabs */
  /* fields */
}
#acf-field-group-fields .inside {
  border-top-width: 0;
  border-top-style: none;
}
#acf-field-group-fields a {
  text-decoration: none;
}
#acf-field-group-fields .li-field-type .field-type-icon {
  margin-right: 8px;
}
@media screen and (max-width: 600px) {
  #acf-field-group-fields .li-field-type .field-type-icon {
    display: none;
  }
}
#acf-field-group-fields .li-field-type .field-type-label {
  display: flex;
}
#acf-field-group-fields .li-field-type .acf-pro-label-field-type {
  position: relative;
  top: -3px;
  margin-left: 8px;
}
#acf-field-group-fields .li-field-type .acf-pro-label-field-type img {
  max-width: 34px;
}
#acf-field-group-fields .li-field-order {
  width: 64px;
  justify-content: center;
}
@media screen and (max-width: 880px) {
  #acf-field-group-fields .li-field-order {
    width: 32px;
  }
}
#acf-field-group-fields .li-field-label {
  width: calc(50% - 64px);
}
#acf-field-group-fields .li-field-name {
  width: 25%;
  word-break: break-word;
}
#acf-field-group-fields .li-field-key {
  display: none;
}
#acf-field-group-fields .li-field-type {
  width: 25%;
}
#acf-field-group-fields.show-field-keys .li-field-label {
  width: calc(35% - 64px);
}
#acf-field-group-fields.show-field-keys .li-field-name {
  width: 15%;
}
#acf-field-group-fields.show-field-keys .li-field-key {
  width: 25%;
  display: flex;
}
#acf-field-group-fields.show-field-keys .li-field-type {
  width: 25%;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-tab-bar {
  display: none;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-main {
  padding: 0;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-main.acf-field-settings-main-general {
  padding-top: 32px;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field {
  margin-bottom: 32px;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-setting-wrapper {
  padding-top: 0;
  border-top: none;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-settings-split .acf-field {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #EAECF0;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-setting-first_day {
  padding-top: 0;
  border-top: none;
}
#acf-field-group-fields.hide-tabs .acf-field-settings-footer {
  margin-top: 32px;
}
#acf-field-group-fields .acf-field-list-wrap {
  border: #ccd0d4 solid 1px;
}
#acf-field-group-fields .acf-field-list {
  background: #f5f5f5;
  margin-top: -1px;
  /* no fields */
  /* empty */
}
#acf-field-group-fields .acf-field-list .acf-tbody > .li-field-name,
#acf-field-group-fields .acf-field-list .acf-tbody > .li-field-key {
  align-items: flex-start;
}
#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported) {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}
#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported):hover:after {
  content: "";
  padding-left: 5px;
  display: inline-flex;
  width: 12px;
  height: 12px;
  background-color: #667085;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
  -webkit-mask-image: url("../../images/icons/icon-copy.svg");
  mask-image: url("../../images/icons/icon-copy.svg");
  background-size: cover;
}
#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).sub-label {
  padding-right: 22px;
}
#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).sub-label:hover {
  padding-right: 0;
}
#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).sub-label:hover:after {
  width: 14px;
  height: 14px;
  padding-left: 8px;
}
#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).copied:hover:after {
  -webkit-mask-image: url("../../images/icons/icon-check-circle-solid.svg");
  mask-image: url("../../images/icons/icon-check-circle-solid.svg");
  background-color: #49ad52;
}
#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported) {
  cursor: pointer;
  display: block;
  position: relative;
  align-items: center;
}
#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported) input {
  padding-right: 40px;
}
#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported) .acf-input-wrap:after {
  content: "";
  padding-left: 5px;
  right: 12px;
  top: 12px;
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: #98A2B3;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
  -webkit-mask-image: url("../../images/icons/icon-copy.svg");
  mask-image: url("../../images/icons/icon-copy.svg");
  background-size: cover;
}
#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported).copied .acf-input-wrap:after {
  -webkit-mask-image: url("../../images/icons/icon-check-circle-solid.svg");
  mask-image: url("../../images/icons/icon-check-circle-solid.svg");
  background-color: #49ad52;
}
#acf-field-group-fields .acf-field-list .no-fields-message {
  padding: 15px 15px;
  background: #fff;
  display: none;
}
#acf-field-group-fields .acf-field-list.-empty .no-fields-message {
  display: block;
}
.acf-admin-3-8 #acf-field-group-fields .acf-field-list-wrap {
  border-color: #dfdfdf;
}

.rtl #acf-field-group-fields .li-field-type .field-type-icon {
  margin-left: 8px;
  margin-right: 0;
}

/* field object */
.acf-field-object {
  border-top: #eeeeee solid 1px;
  background: #fff;
  /* sortable */
  /* meta */
  /* handle */
  /* open */
  /*
  	// debug
  	&[data-save="meta"] {
  		> .handle {
  			border-left: #ffb700 solid 5px !important;
  		}
  	}

  	&[data-save="settings"] {
  		> .handle {
  			border-left: #0ec563 solid 5px !important;
  		}
  	}
  */
  /* hover */
  /* settings */
  /* conditional logic */
}
.acf-field-object.ui-sortable-helper {
  overflow: hidden !important;
  border-width: 1px;
  border-style: solid;
  border-color: #A5D2E7 !important;
  border-radius: 8px;
  filter: drop-shadow(0px 10px 20px rgba(16, 24, 40, 0.14)) drop-shadow(0px 1px 3px rgba(16, 24, 40, 0.1));
}
.acf-field-object.ui-sortable-helper:before {
  display: none !important;
}
.acf-field-object.ui-sortable-placeholder {
  box-shadow: 0 -1px 0 0 #DFDFDF;
  visibility: visible !important;
  background: #F9F9F9;
  border-top-color: transparent;
  min-height: 54px;
}
.acf-field-object.ui-sortable-placeholder:after, .acf-field-object.ui-sortable-placeholder:before {
  visibility: hidden;
}
.acf-field-object > .meta {
  display: none;
}
.acf-field-object > .handle a {
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
}
.acf-field-object > .handle li {
  word-wrap: break-word;
}
.acf-field-object > .handle strong {
  display: block;
  padding-bottom: 0;
  font-size: 14px;
  line-height: 14px;
  min-height: 14px;
}
.acf-field-object > .handle .row-options {
  display: block;
  opacity: 0;
  margin-top: 5px;
}
@media screen and (max-width: 880px) {
  .acf-field-object > .handle .row-options {
    opacity: 1;
    margin-bottom: 0;
  }
}
.acf-field-object > .handle .row-options a {
  margin-right: 4px;
}
.acf-field-object > .handle .row-options a:hover {
  color: rgb(4.0632911392, 71.1075949367, 102.9367088608);
}
.acf-field-object > .handle .row-options a.delete-field {
  color: #a00;
}
.acf-field-object > .handle .row-options a.delete-field:hover {
  color: #f00;
}
.acf-field-object > .handle .row-options.active {
  visibility: visible;
}
.acf-field-object.open + .acf-field-object {
  border-top-color: #E1E1E1;
}
.acf-field-object.open > .handle {
  background: #2a9bd9;
  border: rgb(37.6669322709, 149.6764940239, 211.1330677291) solid 1px;
  text-shadow: #268FBB 0 1px 0;
  color: #fff;
  position: relative;
  margin: 0 -1px 0 -1px;
}
.acf-field-object.open > .handle a {
  color: #fff !important;
}
.acf-field-object.open > .handle a:hover {
  text-decoration: underline !important;
}
.acf-field-object:hover > .handle .row-options, .acf-field-object.-hover > .handle .row-options, .acf-field-object:focus-within > .handle .row-options {
  opacity: 1;
  margin-bottom: 0;
}
.acf-field-object > .settings {
  display: none;
  width: 100%;
}
.acf-field-object > .settings > .acf-table {
  border: none;
}
.acf-field-object .rule-groups {
  margin-top: 20px;
}

/*----------------------------------------------------------------------------
*
* Postbox: Locations
*
*----------------------------------------------------------------------------*/
.rule-groups h4 {
  margin: 3px 0;
}
.rule-groups .rule-group {
  margin: 0 0 5px;
}
.rule-groups .rule-group h4 {
  margin: 0 0 3px;
}
.rule-groups .rule-group td.param {
  width: 35%;
}
.rule-groups .rule-group td.operator {
  width: 20%;
}
.rule-groups .rule-group td.add {
  width: 40px;
}
.rule-groups .rule-group td.remove {
  width: 28px;
  vertical-align: middle;
}
.rule-groups .rule-group td.remove a {
  width: 22px;
  height: 22px;
  visibility: hidden;
}
.rule-groups .rule-group td.remove a:before {
  position: relative;
  top: -2px;
  font-size: 16px;
}
.rule-groups .rule-group tr:hover td.remove a {
  visibility: visible;
}
.rule-groups .rule-group select:empty {
  background: #f8f8f8;
}
.rule-groups:not(.rule-groups-multiple) .rule-group:first-child tr:first-child td.remove a {
  /* Don't allow user to delete the only rule group */
  visibility: hidden !important;
}

/*----------------------------------------------------------------------------
*
*	Options
*
*----------------------------------------------------------------------------*/
#acf-field-group-options tr[data-name=hide_on_screen] li {
  float: left;
  width: 33%;
}

@media (max-width: 1100px) {
  #acf-field-group-options tr[data-name=hide_on_screen] li {
    width: 50%;
  }
}
/*----------------------------------------------------------------------------
*
*	Conditional Logic
*
*----------------------------------------------------------------------------*/
table.conditional-logic-rules {
  background: transparent;
  border: 0 none;
  border-radius: 0;
}

table.conditional-logic-rules tbody td {
  background: transparent;
  border: 0 none !important;
  padding: 5px 2px !important;
}

/*----------------------------------------------------------------------------
*
*	Field: Tab
*
*----------------------------------------------------------------------------*/
.acf-field-object-tab .acf-field-setting-name,
.acf-field-object-tab .acf-field-setting-instructions,
.acf-field-object-tab .acf-field-setting-required,
.acf-field-object-tab .acf-field-setting-warning,
.acf-field-object-tab .acf-field-setting-wrapper {
  display: none;
}
.acf-field-object-tab .li-field-name {
  visibility: hidden;
}
.acf-field-object-tab p:first-child {
  margin: 0.5em 0;
}
.acf-field-object-tab li.acf-settings-type-presentation,
.acf-field-object-tab .acf-field-settings-main-presentation {
  display: none !important;
}

/*----------------------------------------------------------------------------
*
*	Field: Accordion
*
*----------------------------------------------------------------------------*/
.acf-field-object-accordion .acf-field-setting-name,
.acf-field-object-accordion .acf-field-setting-instructions,
.acf-field-object-accordion .acf-field-setting-required,
.acf-field-object-accordion .acf-field-setting-warning,
.acf-field-object-accordion .acf-field-setting-wrapper {
  display: none;
}
.acf-field-object-accordion .li-field-name {
  visibility: hidden;
}
.acf-field-object-accordion p:first-child {
  margin: 0.5em 0;
}
.acf-field-object-accordion .acf-field-setting-instructions {
  display: block;
}

/*----------------------------------------------------------------------------
*
*	Field: Message
*
*----------------------------------------------------------------------------*/
.acf-field-object-message tr[data-name=name],
.acf-field-object-message tr[data-name=instructions],
.acf-field-object-message tr[data-name=required] {
  display: none !important;
}

.acf-field-object-message .li-field-name {
  visibility: hidden;
}

.acf-field-object-message textarea {
  height: 175px !important;
}

/*----------------------------------------------------------------------------
*
*	Field: Separator
*
*----------------------------------------------------------------------------*/
.acf-field-object-separator tr[data-name=name],
.acf-field-object-separator tr[data-name=instructions],
.acf-field-object-separator tr[data-name=required] {
  display: none !important;
}

/*----------------------------------------------------------------------------
*
*	Field: Date Picker
*
*----------------------------------------------------------------------------*/
.acf-field-object-date-picker .acf-radio-list li,
.acf-field-object-time-picker .acf-radio-list li,
.acf-field-object-date-time-picker .acf-radio-list li {
  line-height: 25px;
}
.acf-field-object-date-picker .acf-radio-list span,
.acf-field-object-time-picker .acf-radio-list span,
.acf-field-object-date-time-picker .acf-radio-list span {
  display: inline-block;
  min-width: 10em;
}
.acf-field-object-date-picker .acf-radio-list input[type=text],
.acf-field-object-time-picker .acf-radio-list input[type=text],
.acf-field-object-date-time-picker .acf-radio-list input[type=text] {
  width: 100px;
}

.acf-field-object-date-time-picker .acf-radio-list span {
  min-width: 15em;
}
.acf-field-object-date-time-picker .acf-radio-list input[type=text] {
  width: 200px;
}

/*--------------------------------------------------------------------------------------------
*
*	Slug
*
*--------------------------------------------------------------------------------------------*/
#slugdiv .inside {
  padding: 12px;
  margin: 0;
}
#slugdiv input[type=text] {
  width: 100%;
  height: 28px;
  font-size: 14px;
}

/*--------------------------------------------------------------------------------------------
*
*	RTL
*
*--------------------------------------------------------------------------------------------*/
html[dir=rtl] .acf-field-object.open > .handle {
  margin: 0;
}

/*----------------------------------------------------------------------------
*
*  Device
*
*----------------------------------------------------------------------------*/
@media only screen and (max-width: 850px) {
  tr.acf-field,
  td.acf-label,
  td.acf-input {
    display: block !important;
    width: auto !important;
    border: 0 none !important;
  }
  tr.acf-field {
    border-top: #ededed solid 1px !important;
    margin-bottom: 0 !important;
  }
  td.acf-label {
    background: transparent !important;
    padding-bottom: 0 !important;
  }
}
/*----------------------------------------------------------------------------
*
*  Subtle background on accordion & tab fields to separate them from others
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group #acf-field-group-fields .acf-field-object-tab,
.post-type-acf-field-group #acf-field-group-fields .acf-field-object-accordion {
  background-color: #F9FAFB;
}

/*---------------------------------------------------------------------------------------------
*
*  Global
*
*---------------------------------------------------------------------------------------------*/
.acf-admin-page #wpcontent {
  line-height: 140%;
}

/*---------------------------------------------------------------------------------------------
*
*  Links
*
*---------------------------------------------------------------------------------------------*/
.acf-admin-page a {
  color: #0783BE;
}

/*---------------------------------------------------------------------------------------------
*
*  Headings
*
*---------------------------------------------------------------------------------------------*/
.acf-h1, .acf-admin-page h1,
.acf-headerbar h1 {
  font-size: 21px;
  font-weight: 400;
}

.acf-h2, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2, .acf-page-title, .acf-admin-page h2,
.acf-headerbar h2 {
  font-size: 18px;
  font-weight: 400;
}

.acf-h3, .post-type-acf-field-group .acf-field-settings-fc_head label, .acf-admin-page #acf-popup .acf-popup-box .title h1,
.acf-admin-page #acf-popup .acf-popup-box .title h2,
.acf-admin-page #acf-popup .acf-popup-box .title h3,
.acf-admin-page #acf-popup .acf-popup-box .title h4, .acf-admin-page h3,
.acf-headerbar h3 {
  font-size: 16px;
  font-weight: 400;
}

/*---------------------------------------------------------------------------------------------
*
*  Paragraphs
*
*---------------------------------------------------------------------------------------------*/
.acf-admin-page .p1 {
  font-size: 15px;
}
.acf-admin-page .p2, .acf-admin-page .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner .acf-admin-page p {
  font-size: 14px;
}
.acf-admin-page .p3 {
  font-size: 13.5px;
}
.acf-admin-page .p4, .acf-admin-page .acf-field-list .acf-sortable-handle, .acf-field-list .acf-admin-page .acf-sortable-handle, .acf-admin-page .post-type-acf-field-group .acf-field-object .handle li.li-field-label a.edit-field, .post-type-acf-field-group .acf-field-object .handle li.li-field-label .acf-admin-page a.edit-field, .acf-admin-page .post-type-acf-field-group .acf-field-object .handle li, .post-type-acf-field-group .acf-field-object .handle .acf-admin-page li, .acf-admin-page .post-type-acf-field-group .acf-thead li, .post-type-acf-field-group .acf-thead .acf-admin-page li, .acf-admin-page .acf-input .select2-container.-acf .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container.-acf .select2-selection__rendered, .acf-admin-page .button, .acf-admin-page input[type=text],
.acf-admin-page input[type=search],
.acf-admin-page input[type=number],
.acf-admin-page textarea,
.acf-admin-page select {
  font-size: 13px;
}
.acf-admin-page .p5, .acf-admin-page .acf-field-setting-display_format .acf-radio-list li label code, .acf-field-setting-display_format .acf-radio-list li label .acf-admin-page code,
.acf-admin-page .acf-field-setting-return_format .acf-radio-list li label code,
.acf-field-setting-return_format .acf-radio-list li label .acf-admin-page code, .acf-admin-page .acf-field-group-settings-footer .acf-created-on, .acf-field-group-settings-footer .acf-admin-page .acf-created-on, .acf-admin-page .acf-fields .acf-field-settings-tab-bar li a, .acf-fields .acf-field-settings-tab-bar li .acf-admin-page a,
.acf-admin-page .acf-fields .acf-tab-wrap .acf-tab-group li a,
.acf-fields .acf-tab-wrap .acf-tab-group li .acf-admin-page a,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a,
.acf-admin-page .acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li .acf-admin-page a,
.acf-admin-page .acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li .acf-admin-page a {
  font-size: 12.5px;
}
.acf-admin-page .p6, .acf-admin-page .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p.acf-small, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner .acf-admin-page p.acf-small, .acf-admin-page .post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options a, .post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options .acf-admin-page a, .acf-admin-page .acf-small {
  font-size: 12px;
}
.acf-admin-page .p7 {
  font-size: 11.5px;
}
.acf-admin-page .p8 {
  font-size: 11px;
}

/*---------------------------------------------------------------------------------------------
*
*  Page titles
*
*---------------------------------------------------------------------------------------------*/
.acf-page-title {
  color: #344054;
}

/*---------------------------------------------------------------------------------------------
*
*  Hide old / native WP titles from pages
*
*---------------------------------------------------------------------------------------------*/
.acf-admin-page .acf-settings-wrap h1 {
  display: none !important;
}
.acf-admin-page #acf-admin-tools h1:not(.acf-field-group-pro-features-title, .acf-field-group-pro-features-title-sm) {
  display: none !important;
}

/*---------------------------------------------------------------------------------------------
*
*  Small
*
*---------------------------------------------------------------------------------------------*/
/*---------------------------------------------------------------------------------------------
*
*  Link focus style
*
*---------------------------------------------------------------------------------------------*/
.acf-admin-page a:focus {
  box-shadow: none;
  outline: none;
}
.acf-admin-page a:focus-visible {
  box-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgba(79, 148, 212, 0.8);
  outline: 1px solid transparent;
}

.acf-admin-page {
  /*---------------------------------------------------------------------------------------------
  *
  *  All Inputs
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Read only text inputs
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Number fields
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Textarea
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Select
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Radio Button & Checkbox base styling
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Radio Buttons
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Checkboxes
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Radio Buttons & Checkbox lists
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  ACF Switch
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  File input button
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Action Buttons
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Edit field group header
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Select2 inputs
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  ACF label
  *
  *---------------------------------------------------------------------------------------------*/
  /*---------------------------------------------------------------------------------------------
  *
  *  Tooltip for field name field setting (result of a fix for keyboard navigation)
  *
  *---------------------------------------------------------------------------------------------*/
  /* Field Type Selection select2 */
  /*---------------------------------------------------------------------------------------------
  *
  *  RTL arrow position
  *
  *---------------------------------------------------------------------------------------------*/
}
.acf-admin-page input[type=text],
.acf-admin-page input[type=search],
.acf-admin-page input[type=number],
.acf-admin-page textarea,
.acf-admin-page select {
  box-sizing: border-box;
  height: 40px;
  padding-right: 12px;
  padding-left: 12px;
  background-color: #fff;
  border-color: #D0D5DD;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);
  border-radius: 6px;
  /* stylelint-disable-next-line scss/at-extend-no-missing-placeholder */
  color: #344054;
}
.acf-admin-page input[type=text]:focus,
.acf-admin-page input[type=search]:focus,
.acf-admin-page input[type=number]:focus,
.acf-admin-page textarea:focus,
.acf-admin-page select:focus {
  outline: 3px solid #EBF5FA;
  border-color: #399CCB;
}
.acf-admin-page input[type=text]:disabled,
.acf-admin-page input[type=search]:disabled,
.acf-admin-page input[type=number]:disabled,
.acf-admin-page textarea:disabled,
.acf-admin-page select:disabled {
  background-color: #F9FAFB;
  color: rgb(128.2255319149, 137.7574468085, 157.7744680851);
}
.acf-admin-page input[type=text]::placeholder,
.acf-admin-page input[type=search]::placeholder,
.acf-admin-page input[type=number]::placeholder,
.acf-admin-page textarea::placeholder,
.acf-admin-page select::placeholder {
  color: #98A2B3;
}
.acf-admin-page input[type=text]:read-only {
  background-color: #F9FAFB;
  color: #98A2B3;
}
.acf-admin-page .acf-field.acf-field-number .acf-label,
.acf-admin-page .acf-field.acf-field-number .acf-input input[type=number] {
  max-width: 180px;
}
.acf-admin-page textarea {
  box-sizing: border-box;
  padding-top: 10px;
  padding-bottom: 10px;
  height: 80px;
  min-height: 56px;
}
.acf-admin-page select {
  min-width: 160px;
  max-width: 100%;
  padding-right: 40px;
  padding-left: 12px;
  background-image: url("../../images/icons/icon-chevron-down.svg");
  background-position: right 10px top 50%;
  background-size: 20px;
}
.acf-admin-page select:hover, .acf-admin-page select:focus {
  color: #0783BE;
}
.acf-admin-page select::before {
  content: "";
  display: block;
  position: absolute;
  top: 5px;
  left: 5px;
  width: 20px;
  height: 20px;
}
.acf-admin-page.rtl select {
  padding-right: 12px;
  padding-left: 40px;
  background-position: left 10px top 50%;
}
.acf-admin-page input[type=radio],
.acf-admin-page input[type=checkbox] {
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  border-color: #98A2B3;
  background: #fff;
  box-shadow: none;
}
.acf-admin-page input[type=radio]:hover,
.acf-admin-page input[type=checkbox]:hover {
  background-color: #EBF5FA;
  border-color: #0783BE;
}
.acf-admin-page input[type=radio]:checked, .acf-admin-page input[type=radio]:focus-visible,
.acf-admin-page input[type=checkbox]:checked,
.acf-admin-page input[type=checkbox]:focus-visible {
  background-color: #EBF5FA;
  border-color: #0783BE;
}
.acf-admin-page input[type=radio]:checked:before, .acf-admin-page input[type=radio]:focus-visible:before,
.acf-admin-page input[type=checkbox]:checked:before,
.acf-admin-page input[type=checkbox]:focus-visible:before {
  content: "";
  position: relative;
  top: -1px;
  left: -1px;
  width: 16px;
  height: 16px;
  margin: 0;
  padding: 0;
  background-color: transparent;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.acf-admin-page input[type=radio]:active,
.acf-admin-page input[type=checkbox]:active {
  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);
}
.acf-admin-page input[type=radio]:disabled,
.acf-admin-page input[type=checkbox]:disabled {
  background-color: #F9FAFB;
  border-color: #D0D5DD;
}
.acf-admin-page.rtl input[type=radio]:checked:before, .acf-admin-page.rtl input[type=radio]:focus-visible:before,
.acf-admin-page.rtl input[type=checkbox]:checked:before,
.acf-admin-page.rtl input[type=checkbox]:focus-visible:before {
  left: 1px;
}
.acf-admin-page input[type=radio]:checked:before, .acf-admin-page input[type=radio]:focus:before {
  background-image: url("../../images/field-states/radio-active.svg");
}
.acf-admin-page input[type=checkbox]:checked:before, .acf-admin-page input[type=checkbox]:focus:before {
  background-image: url("../../images/field-states/checkbox-active.svg");
}
.acf-admin-page .acf-radio-list li input[type=radio],
.acf-admin-page .acf-radio-list li input[type=checkbox],
.acf-admin-page .acf-checkbox-list li input[type=radio],
.acf-admin-page .acf-checkbox-list li input[type=checkbox] {
  margin-right: 6px;
}
.acf-admin-page .acf-radio-list.acf-bl li,
.acf-admin-page .acf-checkbox-list.acf-bl li {
  margin-bottom: 8px;
}
.acf-admin-page .acf-radio-list.acf-bl li:last-of-type,
.acf-admin-page .acf-checkbox-list.acf-bl li:last-of-type {
  margin-bottom: 0;
}
.acf-admin-page .acf-radio-list label,
.acf-admin-page .acf-checkbox-list label {
  display: flex;
  align-items: center;
  align-content: center;
}
.acf-admin-page .acf-switch {
  width: 42px;
  height: 24px;
  border: none;
  background-color: #D0D5DD;
  border-radius: 12px;
}
.acf-admin-page .acf-switch:hover {
  background-color: #98A2B3;
}
.acf-admin-page .acf-switch:active {
  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);
}
.acf-admin-page .acf-switch.-on {
  background-color: #0783BE;
}
.acf-admin-page .acf-switch.-on:hover {
  background-color: #066998;
}
.acf-admin-page .acf-switch.-on .acf-switch-slider {
  left: 20px;
}
.acf-admin-page .acf-switch .acf-switch-off,
.acf-admin-page .acf-switch .acf-switch-on {
  visibility: hidden;
}
.acf-admin-page .acf-switch .acf-switch-slider {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 100px;
  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);
}
.acf-admin-page .acf-field-true-false {
  display: flex;
  align-items: flex-start;
}
.acf-admin-page .acf-field-true-false .acf-label {
  order: 2;
  display: block;
  align-items: center;
  max-width: 550px !important;
  margin-top: 2px;
  margin-bottom: 0;
  margin-left: 12px;
}
.acf-admin-page .acf-field-true-false .acf-label label {
  margin-bottom: 0;
}
.acf-admin-page .acf-field-true-false .acf-label .acf-tip {
  margin-left: 12px;
}
.acf-admin-page .acf-field-true-false .acf-label .description {
  display: block;
  margin-top: 2px;
  margin-left: 0;
}
.acf-admin-page.rtl .acf-field-true-false .acf-label {
  margin-right: 12px;
  margin-left: 0;
}
.acf-admin-page.rtl .acf-field-true-false .acf-tip {
  margin-right: 12px;
  margin-left: 0;
}
.acf-admin-page input::file-selector-button {
  box-sizing: border-box;
  min-height: 40px;
  margin-right: 16px;
  padding-top: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
  padding-left: 16px;
  background-color: transparent;
  color: #0783BE !important;
  border-radius: 6px;
  border-width: 1px;
  border-style: solid;
  border-color: #0783BE;
  text-decoration: none;
}
.acf-admin-page input::file-selector-button:hover {
  border-color: #066998;
  cursor: pointer;
  color: #066998 !important;
}
.acf-admin-page .button {
  display: inline-flex;
  align-items: center;
  height: 40px;
  padding-right: 16px;
  padding-left: 16px;
  background-color: transparent;
  border-width: 1px;
  border-style: solid;
  border-color: #0783BE;
  border-radius: 6px;
  color: #0783BE;
}
.acf-admin-page .button:hover {
  background-color: rgb(243.16, 249.08, 252.04);
  border-color: #0783BE;
  color: #0783BE;
}
.acf-admin-page .button:focus {
  background-color: rgb(243.16, 249.08, 252.04);
  outline: 3px solid #EBF5FA;
  color: #0783BE;
}
.acf-admin-page .edit-field-group-header {
  display: block !important;
}
.acf-admin-page .acf-input .select2-container.-acf .select2-selection,
.acf-admin-page .rule-groups .select2-container.-acf .select2-selection {
  border: none;
  line-height: 1;
}
.acf-admin-page .acf-input .select2-container.-acf .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container.-acf .select2-selection__rendered {
  box-sizing: border-box;
  padding-right: 0;
  padding-left: 0;
  background-color: #fff;
  border-width: 1px;
  border-style: solid;
  border-color: #D0D5DD;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);
  border-radius: 6px;
  /* stylelint-disable-next-line scss/at-extend-no-missing-placeholder */
  color: #344054;
}
.acf-admin-page .acf-input .acf-conditional-select-name,
.acf-admin-page .rule-groups .acf-conditional-select-name {
  min-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.acf-admin-page .acf-input .acf-conditional-select-id,
.acf-admin-page .rule-groups .acf-conditional-select-id {
  padding-right: 30px;
}
.acf-admin-page .acf-input .value .select2-container--focus,
.acf-admin-page .rule-groups .value .select2-container--focus {
  height: 40px;
}
.acf-admin-page .acf-input .value .select2-container--open .select2-selection__rendered,
.acf-admin-page .rule-groups .value .select2-container--open .select2-selection__rendered {
  border-color: #399CCB;
}
.acf-admin-page .acf-input .select2-container--focus,
.acf-admin-page .rule-groups .select2-container--focus {
  outline: 3px solid #EBF5FA;
  border-color: #399CCB;
  border-radius: 6px;
}
.acf-admin-page .acf-input .select2-container--focus .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--focus .select2-selection__rendered {
  border-color: #399CCB !important;
}
.acf-admin-page .acf-input .select2-container--focus.select2-container--below.select2-container--open .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--focus.select2-container--below.select2-container--open .select2-selection__rendered {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.acf-admin-page .acf-input .select2-container--focus.select2-container--above.select2-container--open .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--focus.select2-container--above.select2-container--open .select2-selection__rendered {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
}
.acf-admin-page .acf-input .select2-container .select2-search--inline .select2-search__field,
.acf-admin-page .rule-groups .select2-container .select2-search--inline .select2-search__field {
  margin: 0;
  padding-left: 6px;
}
.acf-admin-page .acf-input .select2-container .select2-search--inline .select2-search__field:focus,
.acf-admin-page .rule-groups .select2-container .select2-search--inline .select2-search__field:focus {
  outline: none;
  border: none;
}
.acf-admin-page .acf-input .select2-container--default .select2-selection--multiple .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  padding-top: 0;
  padding-right: 6px;
  padding-bottom: 0;
  padding-left: 6px;
}
.acf-admin-page .acf-input .select2-selection__clear,
.acf-admin-page .rule-groups .select2-selection__clear {
  width: 18px;
  height: 18px;
  margin-top: 12px;
  margin-right: 1px;
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  color: #fff;
}
.acf-admin-page .acf-input .select2-selection__clear:before,
.acf-admin-page .rule-groups .select2-selection__clear:before {
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  top: 0;
  left: 0;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  -webkit-mask-image: url("../../images/icons/icon-close.svg");
  mask-image: url("../../images/icons/icon-close.svg");
  background-color: #98A2B3;
}
.acf-admin-page .acf-input .select2-selection__clear:hover::before,
.acf-admin-page .rule-groups .select2-selection__clear:hover::before {
  background-color: #0783BE;
}
.acf-admin-page .acf-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.acf-admin-page .acf-label .acf-icon-help {
  width: 18px;
  height: 18px;
  background-color: #98A2B3;
}
.acf-admin-page .acf-label label {
  margin-bottom: 0;
}
.acf-admin-page .acf-label .description {
  margin-top: 2px;
}
.acf-admin-page .acf-field-setting-name .acf-tip {
  position: absolute;
  top: 0;
  left: 654px;
  color: #98A2B3;
}
.rtl.acf-admin-page .acf-field-setting-name .acf-tip {
  left: auto;
  right: 654px;
}

.acf-admin-page .acf-field-setting-name .acf-tip .acf-icon-help {
  width: 18px;
  height: 18px;
}
.acf-admin-page .acf-field-setting-type .select2-container.-acf,
.acf-admin-page .acf-field-permalink-rewrite .select2-container.-acf,
.acf-admin-page .acf-field-query-var .select2-container.-acf,
.acf-admin-page .acf-field-capability .select2-container.-acf,
.acf-admin-page .acf-field-parent-slug .select2-container.-acf,
.acf-admin-page .acf-field-data-storage .select2-container.-acf,
.acf-admin-page .acf-field-manage-terms .select2-container.-acf,
.acf-admin-page .acf-field-edit-terms .select2-container.-acf,
.acf-admin-page .acf-field-delete-terms .select2-container.-acf,
.acf-admin-page .acf-field-assign-terms .select2-container.-acf,
.acf-admin-page .acf-field-meta-box .select2-container.-acf,
.acf-admin-page .rule-groups .select2-container.-acf {
  min-height: 40px;
}
.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--default .select2-selection--single .select2-selection__rendered {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 800;
  min-height: 40px;
  padding-top: 0;
  padding-right: 12px;
  padding-bottom: 0;
  padding-left: 12px;
}
.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .field-type-icon,
.acf-admin-page .rule-groups .select2-container--default .select2-selection--single .field-type-icon {
  top: auto;
  width: 18px;
  height: 18px;
  margin-right: 2px;
}
.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .field-type-icon:before,
.acf-admin-page .rule-groups .select2-container--default .select2-selection--single .field-type-icon:before {
  width: 9px;
  height: 9px;
}
.acf-admin-page .acf-field-setting-type .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-permalink-rewrite .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-query-var .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-capability .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-parent-slug .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-data-storage .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-manage-terms .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-edit-terms .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-delete-terms .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-assign-terms .select2-container--open .select2-selection__rendered,
.acf-admin-page .acf-field-meta-box .select2-container--open .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--open .select2-selection__rendered {
  border-color: #6BB5D8 !important;
  border-bottom-color: #D0D5DD !important;
}
.acf-admin-page .acf-field-setting-type .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-permalink-rewrite .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-query-var .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-capability .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-parent-slug .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-data-storage .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-manage-terms .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-edit-terms .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-delete-terms .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-assign-terms .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .acf-field-meta-box .select2-container--open.select2-container--below .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--open.select2-container--below .select2-selection__rendered {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.acf-admin-page .acf-field-setting-type .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-permalink-rewrite .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-query-var .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-capability .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-parent-slug .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-data-storage .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-manage-terms .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-edit-terms .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-delete-terms .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-assign-terms .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .acf-field-meta-box .select2-container--open.select2-container--above .select2-selection__rendered,
.acf-admin-page .rule-groups .select2-container--open.select2-container--above .select2-selection__rendered {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
  border-bottom-color: #6BB5D8 !important;
  border-top-color: #D0D5DD !important;
}
.acf-admin-page .acf-field-setting-type .acf-selection.has-icon,
.acf-admin-page .acf-field-permalink-rewrite .acf-selection.has-icon,
.acf-admin-page .acf-field-query-var .acf-selection.has-icon,
.acf-admin-page .acf-field-capability .acf-selection.has-icon,
.acf-admin-page .acf-field-parent-slug .acf-selection.has-icon,
.acf-admin-page .acf-field-data-storage .acf-selection.has-icon,
.acf-admin-page .acf-field-manage-terms .acf-selection.has-icon,
.acf-admin-page .acf-field-edit-terms .acf-selection.has-icon,
.acf-admin-page .acf-field-delete-terms .acf-selection.has-icon,
.acf-admin-page .acf-field-assign-terms .acf-selection.has-icon,
.acf-admin-page .acf-field-meta-box .acf-selection.has-icon,
.acf-admin-page .rule-groups .acf-selection.has-icon {
  margin-left: 6px;
}
.rtl.acf-admin-page .acf-field-setting-type .acf-selection.has-icon, .acf-admin-page .acf-field-permalink-rewrite .acf-selection.has-icon, .acf-admin-page .acf-field-query-var .acf-selection.has-icon, .acf-admin-page .acf-field-capability .acf-selection.has-icon, .acf-admin-page .acf-field-parent-slug .acf-selection.has-icon, .acf-admin-page .acf-field-data-storage .acf-selection.has-icon, .acf-admin-page .acf-field-manage-terms .acf-selection.has-icon, .acf-admin-page .acf-field-edit-terms .acf-selection.has-icon, .acf-admin-page .acf-field-delete-terms .acf-selection.has-icon, .acf-admin-page .acf-field-assign-terms .acf-selection.has-icon, .acf-admin-page .acf-field-meta-box .acf-selection.has-icon, .acf-admin-page .rule-groups .acf-selection.has-icon {
  margin-right: 6px;
}

.acf-admin-page .acf-field-setting-type .select2-selection__arrow,
.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow,
.acf-admin-page .acf-field-query-var .select2-selection__arrow,
.acf-admin-page .acf-field-capability .select2-selection__arrow,
.acf-admin-page .acf-field-parent-slug .select2-selection__arrow,
.acf-admin-page .acf-field-data-storage .select2-selection__arrow,
.acf-admin-page .acf-field-manage-terms .select2-selection__arrow,
.acf-admin-page .acf-field-edit-terms .select2-selection__arrow,
.acf-admin-page .acf-field-delete-terms .select2-selection__arrow,
.acf-admin-page .acf-field-assign-terms .select2-selection__arrow,
.acf-admin-page .acf-field-meta-box .select2-selection__arrow,
.acf-admin-page .rule-groups .select2-selection__arrow {
  width: 20px;
  height: 20px;
  top: calc(50% - 10px);
  right: 12px;
  background-color: transparent;
}
.acf-admin-page .acf-field-setting-type .select2-selection__arrow:after,
.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow:after,
.acf-admin-page .acf-field-query-var .select2-selection__arrow:after,
.acf-admin-page .acf-field-capability .select2-selection__arrow:after,
.acf-admin-page .acf-field-parent-slug .select2-selection__arrow:after,
.acf-admin-page .acf-field-data-storage .select2-selection__arrow:after,
.acf-admin-page .acf-field-manage-terms .select2-selection__arrow:after,
.acf-admin-page .acf-field-edit-terms .select2-selection__arrow:after,
.acf-admin-page .acf-field-delete-terms .select2-selection__arrow:after,
.acf-admin-page .acf-field-assign-terms .select2-selection__arrow:after,
.acf-admin-page .acf-field-meta-box .select2-selection__arrow:after,
.acf-admin-page .rule-groups .select2-selection__arrow:after {
  content: "";
  display: block;
  position: absolute;
  z-index: 850;
  top: 1px;
  left: 0;
  width: 20px;
  height: 20px;
  -webkit-mask-image: url("../../images/icons/icon-chevron-down.svg");
  mask-image: url("../../images/icons/icon-chevron-down.svg");
  background-color: #667085;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
}
.acf-admin-page .acf-field-setting-type .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-query-var .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-capability .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-parent-slug .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-data-storage .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-manage-terms .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-edit-terms .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-delete-terms .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-assign-terms .select2-selection__arrow b[role=presentation],
.acf-admin-page .acf-field-meta-box .select2-selection__arrow b[role=presentation],
.acf-admin-page .rule-groups .select2-selection__arrow b[role=presentation] {
  display: none;
}
.acf-admin-page .acf-field-setting-type .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-permalink-rewrite .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-query-var .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-capability .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-parent-slug .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-data-storage .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-manage-terms .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-edit-terms .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-delete-terms .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-assign-terms .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .acf-field-meta-box .select2-container--open .select2-selection__arrow:after,
.acf-admin-page .rule-groups .select2-container--open .select2-selection__arrow:after {
  -webkit-mask-image: url("../../images/icons/icon-chevron-up.svg");
  mask-image: url("../../images/icons/icon-chevron-up.svg");
}
.acf-admin-page .acf-term-search-term-name {
  background-color: #F9FAFB;
  border-top: 1px solid #EAECF0;
  border-bottom: 1px solid #EAECF0;
  color: #98A2B3;
  padding: 5px 5px 5px 10px;
  width: 100%;
  margin: 0;
  display: block;
  font-weight: 300;
}
.acf-admin-page .field-type-select-results {
  position: relative;
  top: 4px;
  z-index: 1002;
  border-radius: 0 0 6px 6px;
  box-shadow: 0px 8px 24px 4px rgba(16, 24, 40, 0.12);
}
.acf-admin-page .field-type-select-results.select2-dropdown--above {
  display: flex;
  flex-direction: column-reverse;
  top: 0;
  border-radius: 6px 6px 0 0;
  z-index: 99999;
}
.select2-container.select2-container--open.acf-admin-page .field-type-select-results {
  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 8px 24px 4px rgba(16, 24, 40, 0.12);
}

.acf-admin-page .field-type-select-results .acf-selection.has-icon {
  margin-left: 6px;
}
.rtl.acf-admin-page .field-type-select-results .acf-selection.has-icon {
  margin-right: 6px;
}

.acf-admin-page .field-type-select-results .select2-search {
  position: relative;
  margin: 0;
  padding: 0;
}
.acf-admin-page .field-type-select-results .select2-search--dropdown:after {
  content: "";
  display: block;
  position: absolute;
  top: 12px;
  left: 13px;
  width: 16px;
  height: 16px;
  -webkit-mask-image: url("../../images/icons/icon-search.svg");
  mask-image: url("../../images/icons/icon-search.svg");
  background-color: #98A2B3;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
}
.rtl.acf-admin-page .field-type-select-results .select2-search--dropdown:after {
  right: 12px;
  left: auto;
}

.acf-admin-page .field-type-select-results .select2-search .select2-search__field {
  padding-left: 38px;
  border-right: 0;
  border-bottom: 0;
  border-left: 0;
  border-radius: 0;
}
.rtl.acf-admin-page .field-type-select-results .select2-search .select2-search__field {
  padding-right: 38px;
  padding-left: 0;
}

.acf-admin-page .field-type-select-results .select2-search .select2-search__field:focus {
  border-top-color: #D0D5DD;
  outline: 0;
}
.acf-admin-page .field-type-select-results .select2-results__options {
  max-height: 440px;
}
.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option--highlighted {
  background-color: #0783BE !important;
  color: #F9FAFB !important;
}
.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option {
  display: inline-flex;
  position: relative;
  width: calc(100% - 24px);
  min-height: 32px;
  padding-top: 0;
  padding-right: 12px;
  padding-bottom: 0;
  padding-left: 12px;
  align-items: center;
}
.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option .field-type-icon {
  top: auto;
  width: 18px;
  height: 18px;
  margin-right: 2px;
  box-shadow: 0 0 0 1px #F9FAFB;
}
.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option .field-type-icon:before {
  width: 9px;
  height: 9px;
}
.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true] {
  background-color: #EBF5FA !important;
  color: #344054 !important;
}
.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true]:after {
  content: "";
  right: 13px;
  position: absolute;
  width: 16px;
  height: 16px;
  -webkit-mask-image: url("../../images/icons/icon-check.svg");
  mask-image: url("../../images/icons/icon-check.svg");
  background-color: #0783BE;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
}
.rtl.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true]:after {
  left: 13px;
  right: auto;
}

.acf-admin-page .field-type-select-results .select2-results__group {
  display: inline-flex;
  align-items: center;
  width: calc(100% - 24px);
  min-height: 25px;
  background-color: #F9FAFB;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #EAECF0;
  color: #98A2B3;
  font-size: 11px;
  margin-bottom: 0;
  padding-top: 0;
  padding-right: 12px;
  padding-bottom: 0;
  padding-left: 12px;
  font-weight: normal;
}
.acf-admin-page.rtl .acf-field-setting-type .select2-selection__arrow:after,
.acf-admin-page.rtl .acf-field-permalink-rewrite .select2-selection__arrow:after,
.acf-admin-page.rtl .acf-field-query-var .select2-selection__arrow:after {
  right: auto;
  left: 10px;
}

.rtl.post-type-acf-field-group .acf-field-setting-name .acf-tip,
.rtl.acf-internal-post-type .acf-field-setting-name .acf-tip {
  left: auto;
  right: 654px;
}

/*----------------------------------------------------------------------------
*
*  Container sizes
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group .metabox-holder.columns-1 #acf-field-group-fields,
.post-type-acf-field-group .metabox-holder.columns-1 #acf-field-group-options,
.post-type-acf-field-group .metabox-holder.columns-1 .meta-box-sortables.ui-sortable,
.post-type-acf-field-group .metabox-holder.columns-1 .notice {
  max-width: 1440px;
}

/*----------------------------------------------------------------------------
*
*  Max width for notices in 1 column edit field group layout
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group.columns-1 .notice {
  max-width: 1440px;
}

/*----------------------------------------------------------------------------
*
*  Widen edit field group headerbar for 2 column layout
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group.columns-2 .acf-headerbar .acf-headerbar-inner {
  max-width: 100%;
}

/*----------------------------------------------------------------------------
*
*  Post stuff
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group #poststuff {
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 0;
  margin-left: 0;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 0;
}

/*----------------------------------------------------------------------------
*
*  Table
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap {
  overflow: hidden;
  border: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty {
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .acf-thead,
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .acf-tfoot {
  display: none;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .no-fields-message {
  min-height: 280px;
}

/*----------------------------------------------------------------------------
*
*  Table header
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group .acf-thead {
  background-color: #F9FAFB;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #EAECF0;
}
.post-type-acf-field-group .acf-thead li {
  display: flex;
  align-items: center;
  min-height: 48px;
  padding-top: 0;
  padding-bottom: 0;
  color: #344054;
  font-weight: 500;
}

/*----------------------------------------------------------------------------
*
*  Table body
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group .acf-field-object {
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
.post-type-acf-field-group .acf-field-object:hover .acf-sortable-handle:before {
  display: inline-flex;
}
.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint:before {
  display: block;
  content: "";
  height: 2px;
  width: 100%;
  background: #D0D5DD;
  margin-top: -1px;
}
.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint.acf-field-object-accordion:before {
  display: none;
}
.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint.acf-field-object-accordion:after {
  display: block;
  content: "";
  height: 2px;
  width: 100%;
  background: #D0D5DD;
  z-index: 500;
}
.post-type-acf-field-group .acf-field-object:hover {
  background-color: rgb(247.24, 251.12, 253.06);
}
.post-type-acf-field-group .acf-field-object.open {
  background-color: #fff;
  border-top-color: #A5D2E7;
}
.post-type-acf-field-group .acf-field-object.open .handle {
  background-color: #D8EBF5;
  border: none;
  text-shadow: none;
}
.post-type-acf-field-group .acf-field-object.open .handle a {
  color: #0783BE !important;
}
.post-type-acf-field-group .acf-field-object.open .handle a.delete-field {
  color: #a00 !important;
}
.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl {
  margin: 0;
}
.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl li {
  width: auto;
}
.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl li:first-child {
  flex-grow: 1;
  margin-left: -10px;
}
.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl li:nth-child(2) {
  padding-right: 0;
}
.post-type-acf-field-group .acf-field-object ul.acf-hl {
  display: flex;
  align-items: stretch;
}
.post-type-acf-field-group .acf-field-object .handle li {
  display: flex;
  align-items: top;
  flex-wrap: wrap;
  min-height: 60px;
  color: #344054;
}
.post-type-acf-field-group .acf-field-object .handle li.li-field-label {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
  width: auto;
}
.post-type-acf-field-group .acf-field-object .handle li.li-field-label strong {
  font-weight: 500;
}
.post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options {
  width: 100%;
}
/*----------------------------------------------------------------------------
*
*  Table footer
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group .acf-tfoot {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-height: 80px;
  box-sizing: border-box;
  padding-top: 8px;
  padding-right: 24px;
  padding-bottom: 8px;
  padding-left: 24px;
  background-color: #fff;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
.post-type-acf-field-group .acf-tfoot .acf-fr {
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 0;
  margin-left: 0;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 0;
}

/*----------------------------------------------------------------------------
*
*  Edit field settings
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group .acf-field-object .settings {
  box-sizing: border-box;
  padding-top: 0;
  padding-bottom: 0;
  background-color: #fff;
  border-left-width: 4px;
  border-left-style: solid;
  border-left-color: #6BB5D8;
}

/*----------------------------------------------------------------------------
*
*  Main field settings container
*
*----------------------------------------------------------------------------*/
.acf-field-settings-main {
  padding-top: 32px;
  padding-right: 0;
  padding-bottom: 32px;
  padding-left: 0;
}
.acf-field-settings-main .acf-field:last-of-type,
.acf-field-settings-main .acf-field.acf-last-visible {
  margin-bottom: 0;
}

/*----------------------------------------------------------------------------
*
*  Field label
*
*----------------------------------------------------------------------------*/
.acf-field-settings .acf-label {
  display: block;
  justify-content: space-between;
  align-items: center;
  align-content: center;
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 6px;
  margin-left: 0;
}

/*----------------------------------------------------------------------------
*
*  Single field
*
*----------------------------------------------------------------------------*/
.acf-field-settings .acf-field {
  box-sizing: border-box;
  width: 100%;
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 32px;
  margin-left: 0;
  padding-top: 0;
  padding-right: 72px;
  padding-bottom: 0;
  padding-left: 72px;
}
@media screen and (max-width: 600px) {
  .acf-field-settings .acf-field {
    padding-right: 12px;
    padding-left: 12px;
  }
}
.acf-field-settings .acf-field .acf-label,
.acf-field-settings .acf-field .acf-input {
  max-width: 600px;
}
.acf-field-settings .acf-field .acf-label.acf-input-sub,
.acf-field-settings .acf-field .acf-input.acf-input-sub {
  max-width: 100%;
}
.acf-field-settings .acf-field .acf-label .acf-btn:disabled,
.acf-field-settings .acf-field .acf-input .acf-btn:disabled {
  background-color: #F2F4F7;
  color: #98A2B3 !important;
  border: 1px #D0D5DD solid;
  cursor: default;
}
.acf-field-settings .acf-field .acf-input-wrap {
  overflow: visible;
}

/*----------------------------------------------------------------------------
*
*  Field separators
*
*----------------------------------------------------------------------------*/
.acf-field-settings .acf-field.acf-field-setting-label,
.acf-field-settings .acf-field-setting-wrapper {
  padding-top: 24px;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}

.acf-field-settings .acf-field-setting-wrapper {
  margin-top: 24px;
}

/*----------------------------------------------------------------------------
*
*  Informational Notes for specific fields
*
*----------------------------------------------------------------------------*/
.acf-field-setting-bidirectional_notes .acf-label {
  display: none;
}
.acf-field-setting-bidirectional_notes .acf-feature-notice {
  background-color: #F9FAFB;
  border: 1px solid #EAECF0;
  border-radius: 6px;
  padding: 16px;
  color: #344054;
  position: relative;
}
.acf-field-setting-bidirectional_notes .acf-feature-notice.with-warning-icon {
  padding-left: 45px;
}
.acf-field-setting-bidirectional_notes .acf-feature-notice.with-warning-icon::before {
  content: "";
  display: block;
  position: absolute;
  top: 17px;
  left: 18px;
  z-index: 600;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-color: #667085;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  -webkit-mask-image: url("../../images/icons/icon-info.svg");
  mask-image: url("../../images/icons/icon-info.svg");
}

/*----------------------------------------------------------------------------
*
*  Edit fields footer
*
*----------------------------------------------------------------------------*/
.acf-field-settings .acf-field-settings-footer {
  display: flex;
  align-items: center;
  min-height: 72px;
  box-sizing: border-box;
  width: 100%;
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 0;
  margin-left: 0;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 72px;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
@media screen and (max-width: 600px) {
  .acf-field-settings .acf-field-settings-footer {
    padding-left: 12px;
  }
}

.rtl .acf-field-settings .acf-field-settings-footer {
  padding-top: 0;
  padding-right: 72px;
  padding-bottom: 0;
  padding-left: 0;
}

/*----------------------------------------------------------------------------
*
*  Tabs
*
*----------------------------------------------------------------------------*/
.acf-fields .acf-tab-wrap,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap,
.acf-browse-fields-modal-wrap .acf-tab-wrap {
  background: #F9FAFB;
  border-bottom-color: #1D2939;
}
.acf-fields .acf-tab-wrap .acf-tab-group,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group {
  padding-right: 24px;
  padding-left: 24px;
  border-top-width: 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #EAECF0;
}
.acf-fields .acf-field-settings-tab-bar,
.acf-fields .acf-tab-wrap .acf-tab-group,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group {
  display: flex;
  align-items: stretch;
  min-height: 48px;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 24px;
  margin-top: 0;
  margin-bottom: 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #EAECF0;
}
.acf-fields .acf-field-settings-tab-bar li,
.acf-fields .acf-tab-wrap .acf-tab-group li,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li {
  display: flex;
  margin-top: 0;
  margin-right: 24px;
  margin-bottom: 0;
  margin-left: 0;
  padding: 0;
}
.acf-fields .acf-field-settings-tab-bar li a,
.acf-fields .acf-tab-wrap .acf-tab-group li a,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a {
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  height: 100%;
  padding-top: 3px;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 0;
  background: none;
  border-top: none;
  border-right: none;
  border-bottom-width: 3px;
  border-bottom-style: solid;
  border-bottom-color: transparent;
  border-left: none;
  color: #667085;
  font-weight: normal;
}
.acf-fields .acf-field-settings-tab-bar li a:focus-visible,
.acf-fields .acf-tab-wrap .acf-tab-group li a:focus-visible,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a:focus-visible,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a:focus-visible,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a:focus-visible,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a:focus-visible {
  border: 1px solid #5897fb;
}
.acf-fields .acf-field-settings-tab-bar li a:hover,
.acf-fields .acf-tab-wrap .acf-tab-group li a:hover,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a:hover,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a:hover,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a:hover,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a:hover {
  color: #1D2939;
}
.acf-fields .acf-field-settings-tab-bar li a:hover,
.acf-fields .acf-tab-wrap .acf-tab-group li a:hover,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a:hover,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a:hover,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a:hover,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a:hover {
  background-color: transparent;
}
.acf-fields .acf-field-settings-tab-bar li.active a,
.acf-fields .acf-tab-wrap .acf-tab-group li.active a,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li.active a,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li.active a,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li.active a,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li.active a {
  background: none;
  border-bottom-color: #0783BE;
  color: #0783BE;
}
.acf-fields .acf-field-settings-tab-bar li.active a:focus-visible,
.acf-fields .acf-tab-wrap .acf-tab-group li.active a:focus-visible,
.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li.active a:focus-visible,
.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li.active a:focus-visible,
.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li.active a:focus-visible,
.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li.active a:focus-visible {
  border-bottom-color: #0783BE;
  border-bottom-width: 3px;
}

.acf-admin-page.acf-internal-post-type .acf-field-editor .acf-field-settings-tab-bar {
  padding-left: 72px;
}
@media screen and (max-width: 600px) {
  .acf-admin-page.acf-internal-post-type .acf-field-editor .acf-field-settings-tab-bar {
    padding-left: 12px;
  }
}

/*----------------------------------------------------------------------------
*
*  Field group settings
*
*----------------------------------------------------------------------------*/
#acf-field-group-options .field-group-settings-tab {
  padding-top: 24px;
  padding-right: 24px;
  padding-bottom: 24px;
  padding-left: 24px;
}
#acf-field-group-options .field-group-settings-tab .acf-field:last-of-type {
  padding: 0;
}
#acf-field-group-options .acf-field {
  border: none;
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 0;
  margin-left: 0;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 24px;
  padding-left: 0;
}
#acf-field-group-options .field-group-setting-split-container {
  display: flex;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 0;
}
#acf-field-group-options .field-group-setting-split-container .field-group-setting-split {
  box-sizing: border-box;
  padding-top: 24px;
  padding-right: 24px;
  padding-bottom: 24px;
  padding-left: 24px;
}
#acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(1) {
  flex: 1 0 auto;
}
#acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(2n) {
  flex: 1 0 auto;
  max-width: 320px;
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 0;
  margin-left: 32px;
  padding-right: 32px;
  padding-left: 32px;
  border-left-width: 1px;
  border-left-style: solid;
  border-left-color: #EAECF0;
}
#acf-field-group-options .acf-field[data-name=description] {
  max-width: 600px;
}
#acf-field-group-options .acf-button-group {
  display: inline-flex;
}

.rtl #acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(2n) {
  margin-right: 32px;
  margin-left: 0;
  border-left: none;
  border-right-width: 1px;
  border-right-style: solid;
  border-right-color: #EAECF0;
}

/*----------------------------------------------------------------------------
*
*  Reorder handles
*
*----------------------------------------------------------------------------*/
.acf-field-list .li-field-order {
  padding: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: stretch;
  align-items: stretch;
  background-color: transparent;
}
.acf-field-list .acf-sortable-handle {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 11px;
  padding-bottom: 8px;
  background-color: transparent;
  border: none;
  border-radius: 0;
}
.acf-field-list .acf-sortable-handle:hover {
  cursor: grab;
}
.acf-field-list .acf-sortable-handle:before {
  content: "";
  display: none;
  position: absolute;
  top: 16px;
  left: 8px;
  width: 16px;
  height: 16px;
  width: 12px;
  height: 12px;
  background-color: #98A2B3;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
  -webkit-mask-image: url("../../images/icons/icon-draggable.svg");
  mask-image: url("../../images/icons/icon-draggable.svg");
}

.rtl .acf-field-list .acf-sortable-handle:before {
  left: 0;
  right: 8px;
}

/*----------------------------------------------------------------------------
*
*  Expand / collapse field icon
*
*----------------------------------------------------------------------------*/
.acf-field-object .li-field-label {
  position: relative;
  padding-left: 40px;
}
.acf-field-object .li-field-label:before {
  content: "";
  display: block;
  position: absolute;
  left: 6px;
  display: inline-flex;
  width: 18px;
  height: 18px;
  margin-top: -2px;
  background-color: #667085;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
  -webkit-mask-image: url("../../images/icons/icon-chevron-down.svg");
  mask-image: url("../../images/icons/icon-chevron-down.svg");
}
.acf-field-object .li-field-label:hover:before {
  cursor: pointer;
}

.rtl .acf-field-object .li-field-label {
  padding-left: 0;
  padding-right: 40px;
}
.rtl .acf-field-object .li-field-label:before {
  left: 0;
  right: 6px;
  -webkit-mask-image: url("../../images/icons/icon-chevron-down.svg");
  mask-image: url("../../images/icons/icon-chevron-down.svg");
}
.rtl .acf-field-object.open .li-field-label:before {
  -webkit-mask-image: url("../../images/icons/icon-chevron-down.svg");
  mask-image: url("../../images/icons/icon-chevron-down.svg");
}
.rtl .acf-field-object.open .acf-input-sub .li-field-label:before {
  -webkit-mask-image: url("../../images/icons/icon-chevron-right.svg");
  mask-image: url("../../images/icons/icon-chevron-right.svg");
}
.rtl .acf-field-object.open .acf-input-sub .acf-field-object.open .li-field-label:before {
  -webkit-mask-image: url("../../images/icons/icon-chevron-down.svg");
  mask-image: url("../../images/icons/icon-chevron-down.svg");
}

.acf-thead .li-field-label {
  padding-left: 40px;
}
.rtl .acf-thead .li-field-label {
  padding-left: 0;
  padding-right: 40px;
}

/*----------------------------------------------------------------------------
*
*  Conditional logic layout
*
*----------------------------------------------------------------------------*/
.acf-field-settings-main-conditional-logic .acf-conditional-toggle {
  display: flex;
  padding-right: 72px;
  padding-left: 72px;
}
@media screen and (max-width: 600px) {
  .acf-field-settings-main-conditional-logic .acf-conditional-toggle {
    padding-left: 12px;
  }
}
.acf-field-settings-main-conditional-logic .acf-field {
  flex-wrap: wrap;
  margin-bottom: 0;
  padding-right: 0;
  padding-left: 0;
}
.acf-field-settings-main-conditional-logic .acf-field .rule-groups {
  flex: 0 1 100%;
  order: 3;
  margin-top: 32px;
  padding-top: 32px;
  padding-right: 72px;
  padding-left: 72px;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
@media screen and (max-width: 600px) {
  .acf-field-settings-main-conditional-logic .acf-field .rule-groups {
    padding-left: 12px;
  }
  .acf-field-settings-main-conditional-logic .acf-field .rule-groups table.acf-table tbody tr {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    align-items: flex-start;
  }
  .acf-field-settings-main-conditional-logic .acf-field .rule-groups table.acf-table tbody tr td {
    flex: 1 1 100%;
  }
}

.acf-taxonomy-select-id,
.acf-relationship-select-id,
.acf-post_object-select-id,
.acf-page_link-select-id,
.acf-user-select-id {
  color: #98A2B3;
  padding-left: 10px;
}

.acf-taxonomy-select-sub-item {
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 5px;
}

.acf-taxonomy-select-name {
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*----------------------------------------------------------------------------
*
*  Prefix & append styling
*
*----------------------------------------------------------------------------*/
.acf-input .acf-input-prepend,
.acf-input .acf-input-append {
  display: inline-flex;
  align-items: center;
  height: 100%;
  min-height: 40px;
  padding-right: 12px;
  padding-left: 12px;
  background-color: #F9FAFB;
  border-color: #D0D5DD;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);
  color: #667085;
}
.acf-input .acf-input-prepend {
  border-radius: 6px 0 0 6px;
}
.acf-input .acf-input-append {
  border-radius: 0 6px 6px 0;
}

/*----------------------------------------------------------------------------
*
*  ACF input wrap
*
*----------------------------------------------------------------------------*/
.acf-input-wrap {
  display: flex;
}

.acf-field-settings-main-presentation .acf-input-wrap {
  display: flex;
}

/*----------------------------------------------------------------------------
*
*  Empty state
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message {
  display: flex;
  justify-content: center;
  padding-top: 48px;
  padding-bottom: 48px;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: center;
  align-items: flex-start;
  text-align: center;
  max-width: 400px;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner img,
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2,
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p {
  flex: 1 0 100%;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2 {
  margin-top: 32px;
  margin-bottom: 0;
  padding: 0;
  color: #344054;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p {
  margin-top: 12px;
  margin-bottom: 0;
  padding: 0;
  color: #667085;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p.acf-small {
  margin-top: 32px;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner img {
  max-width: 284px;
  margin-bottom: 0;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner .acf-btn {
  margin-top: 32px;
}

/*----------------------------------------------------------------------------
*
*  Hide add title prompt label
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group .acf-headerbar #title-prompt-text {
  display: none;
}

/*----------------------------------------------------------------------------
*
*  Modal styling
*
*----------------------------------------------------------------------------*/
.acf-admin-page #acf-popup .acf-popup-box {
  min-width: 480px;
}
.acf-admin-page #acf-popup .acf-popup-box .title {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: space-between;
  min-height: 64px;
  box-sizing: border-box;
  margin: 0;
  padding-right: 24px;
  padding-left: 24px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #EAECF0;
}
.acf-admin-page #acf-popup .acf-popup-box .title h1,
.acf-admin-page #acf-popup .acf-popup-box .title h2,
.acf-admin-page #acf-popup .acf-popup-box .title h3,
.acf-admin-page #acf-popup .acf-popup-box .title h4 {
  padding-left: 0;
  color: #344054;
}
.acf-admin-page #acf-popup .acf-popup-box .title .acf-icon {
  display: block;
  position: relative;
  top: auto;
  right: auto;
  width: 22px;
  height: 22px;
  background-color: transparent;
  color: transparent;
}
.acf-admin-page #acf-popup .acf-popup-box .title .acf-icon:before {
  display: inline-flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 22px;
  height: 22px;
  background-color: #667085;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  text-indent: 500%;
  white-space: nowrap;
  overflow: hidden;
  -webkit-mask-image: url("../../images/icons/icon-close-circle.svg");
  mask-image: url("../../images/icons/icon-close-circle.svg");
}
.acf-admin-page #acf-popup .acf-popup-box .title .acf-icon:hover:before {
  background-color: #0783BE;
}
.acf-admin-page #acf-popup .acf-popup-box .inner {
  box-sizing: border-box;
  margin: 0;
  padding-top: 24px;
  padding-right: 24px;
  padding-bottom: 24px;
  padding-left: 24px;
  border-top: none;
}
.acf-admin-page #acf-popup .acf-popup-box .inner p {
  margin-top: 0;
  margin-bottom: 0;
}
.acf-admin-page #acf-popup .acf-popup-box #acf-move-field-form .acf-field-select,
.acf-admin-page #acf-popup .acf-popup-box #acf-link-field-groups-form .acf-field-select {
  margin-top: 0;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .title h3,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .title h3 {
  color: #1D2939;
  font-weight: 500;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .title h3:before,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .title h3:before {
  content: "";
  width: 18px;
  height: 18px;
  background: #98A2B3;
  margin-right: 9px;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner {
  padding: 0 !important;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-field-select,
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-link-successful,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field-select,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-link-successful {
  padding: 32px 24px;
  margin-bottom: 0;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-field-select .description,
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-link-successful .description,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field-select .description,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-link-successful .description {
  margin-top: 6px !important;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-actions,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions {
  background: #F9FAFB;
  border-top: 1px solid #EAECF0;
  padding-top: 20px;
  padding-left: 24px;
  padding-bottom: 20px;
  padding-right: 24px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-actions .acf-btn,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions .acf-btn {
  display: inline-block;
  margin-left: 8px;
}
.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-actions .acf-btn.acf-btn-primary,
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions .acf-btn.acf-btn-primary {
  width: 120px;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-error-message.-success {
  display: none;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .-dismiss {
  margin: 24px 32px !important;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field {
  padding: 24px 32px 0 32px;
  margin: 0;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field.acf-error .acf-input-wrap {
  overflow: inherit;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field.acf-error .acf-input-wrap input[type=text] {
  border: 1px rgba(209, 55, 55, 0.5) solid !important;
  box-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.12), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;
  background-image: url(../../images/icons/icon-info-red.svg);
  background-position: right 10px top 50%;
  background-size: 14px;
  background-repeat: no-repeat;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field .acf-options-page-modal-error p {
  font-size: 12px;
  color: #D13737;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions {
  margin-top: 32px;
}
.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions .acf-btn:disabled {
  background-color: #0783BE;
}

/*----------------------------------------------------------------------------
*
*  Hide original #post-body-content from edit field group page
*
*----------------------------------------------------------------------------*/
.acf-admin-single-field-group #post-body-content {
  display: none;
}

/*----------------------------------------------------------------------------
*
*  Settings section footer
*
*----------------------------------------------------------------------------*/
.acf-field-group-settings-footer {
  display: flex;
  justify-content: space-between;
  align-content: stretch;
  align-items: center;
  position: relative;
  min-height: 88px;
  margin-right: -24px;
  margin-left: -24px;
  margin-bottom: -24px;
  padding-right: 24px;
  padding-left: 24px;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
.acf-field-group-settings-footer .acf-created-on {
  display: inline-flex;
  justify-content: flex-start;
  align-content: stretch;
  align-items: center;
  color: #667085;
}
.acf-field-group-settings-footer .acf-created-on:before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-color: #98A2B3;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
  -webkit-mask-image: url("../../images/icons/icon-time.svg");
  mask-image: url("../../images/icons/icon-time.svg");
}

/*----------------------------------------------------------------------------
*
*  Conditional logic enabled badge
*
*----------------------------------------------------------------------------*/
.conditional-logic-badge {
  display: none;
}
.conditional-logic-badge.is-enabled {
  display: inline-block;
  width: 6px;
  height: 6px;
  overflow: hidden;
  margin-left: 8px;
  background-color: rgba(82, 170, 89, 0.4);
  border-width: 1px;
  border-style: solid;
  border-color: #52AA59;
  border-radius: 100px;
  text-indent: 100%;
  white-space: nowrap;
}

/*----------------------------------------------------------------------------
*
*  Field settings container
*
*----------------------------------------------------------------------------*/
.acf-field-type-settings {
  container-name: settings;
  container-type: inline-size;
}

/*----------------------------------------------------------------------------
*
*  Split field settings
*
*----------------------------------------------------------------------------*/
.acf-field-settings-split {
  display: flex;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
.acf-field-settings-split .acf-field {
  margin: 0;
  padding-top: 32px;
  padding-bottom: 32px;
}
.acf-field-settings-split .acf-field:nth-child(2n) {
  border-left-width: 1px;
  border-left-style: solid;
  border-left-color: #EAECF0;
}

@container settings (max-width: 1170px) {
  .acf-field-settings-split {
    border: none;
    flex-direction: column;
  }
  .acf-field {
    border-top-width: 1px;
    border-top-style: solid;
    border-top-color: #EAECF0;
  }
}
/*----------------------------------------------------------------------------
*
*  Display & return format
*
*----------------------------------------------------------------------------*/
.acf-field-setting-display_format .acf-label,
.acf-field-setting-return_format .acf-label {
  margin-bottom: 6px;
}
.acf-field-setting-display_format .acf-radio-list li,
.acf-field-setting-return_format .acf-radio-list li {
  display: flex;
}
.acf-field-setting-display_format .acf-radio-list li label,
.acf-field-setting-return_format .acf-radio-list li label {
  display: inline-flex;
  width: 100%;
}
.acf-field-setting-display_format .acf-radio-list li label span,
.acf-field-setting-return_format .acf-radio-list li label span {
  flex: 1 1 auto;
}
.acf-field-setting-display_format .acf-radio-list li label code,
.acf-field-setting-return_format .acf-radio-list li label code {
  padding-right: 8px;
  padding-left: 8px;
  background-color: #F2F4F7;
  border-radius: 4px;
  color: #475467;
}
.acf-field-setting-display_format .acf-radio-list li input[type=text],
.acf-field-setting-return_format .acf-radio-list li input[type=text] {
  height: 32px;
}

.acf-field-settings .acf-field-setting-first_day {
  padding-top: 32px;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}

/*----------------------------------------------------------------------------
*
*  Image and Gallery fields
*
*----------------------------------------------------------------------------*/
.acf-field-object-image .acf-hl[data-cols="3"] > li,
.acf-field-object-gallery .acf-hl[data-cols="3"] > li {
  width: auto;
}

/*----------------------------------------------------------------------------
*
* Appended fields fields
*
*----------------------------------------------------------------------------*/
.acf-field-settings .acf-field-appended {
  overflow: auto;
}
.acf-field-settings .acf-field-appended .acf-input {
  float: left;
}

/*----------------------------------------------------------------------------
*
*  Flexible widths for image minimum / maximum size fields
*
*----------------------------------------------------------------------------*/
.acf-field-settings .acf-field.acf-field-setting-min_width .acf-input,
.acf-field-settings .acf-field.acf-field-setting-max_width .acf-input {
  max-width: none;
}
.acf-field-settings .acf-field.acf-field-setting-min_width .acf-input-wrap input[type=text],
.acf-field-settings .acf-field.acf-field-setting-max_width .acf-input-wrap input[type=text] {
  max-width: 81px;
}

/*----------------------------------------------------------------------------
*
*  Temporary fix to hide pagination setting for repeaters used as subfields.
*
*----------------------------------------------------------------------------*/
.post-type-acf-field-group .acf-field-object-flexible-content .acf-field-setting-pagination {
  display: none;
}
.post-type-acf-field-group .acf-field-object-repeater .acf-field-object-repeater .acf-field-setting-pagination {
  display: none;
}

/*----------------------------------------------------------------------------
*
*  Flexible content field width
*
*----------------------------------------------------------------------------*/
.acf-admin-single-field-group .acf-field-object-flexible-content .acf-is-subfields .acf-field-object .acf-label,
.acf-admin-single-field-group .acf-field-object-flexible-content .acf-is-subfields .acf-field-object .acf-input {
  max-width: 600px;
}

/*----------------------------------------------------------------------------
*
*  Fix default value checkbox focus state
*
*----------------------------------------------------------------------------*/
.acf-admin-single-field-group .acf-field.acf-field-true-false.acf-field-setting-default_value .acf-true-false {
  border: none;
}
.acf-admin-single-field-group .acf-field.acf-field-true-false.acf-field-setting-default_value .acf-true-false input[type=checkbox] {
  margin-right: 0;
}

/*----------------------------------------------------------------------------
*
*  With front field extra spacing
*
*----------------------------------------------------------------------------*/
.acf-field.acf-field-with-front {
  margin-top: 32px;
}

/*---------------------------------------------------------------------------------------------
*
*  Sub-fields layout
*
*---------------------------------------------------------------------------------------------*/
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub {
  max-width: 100%;
  overflow: hidden;
  border-radius: 8px;
  border-width: 1px;
  border-style: solid;
  border-color: rgb(219.125, 222.5416666667, 229.375);
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-sub-field-list-header {
  display: flex;
  justify-content: space-between;
  align-content: stretch;
  align-items: center;
  min-height: 64px;
  padding-right: 24px;
  padding-left: 24px;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-list-wrap {
  box-shadow: none;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-hl.acf-tfoot {
  min-height: 64px;
  align-items: center;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input.acf-input-sub {
  max-width: 100%;
  margin-right: 0;
  margin-left: 0;
}

.post-type-acf-field-group .acf-input-sub .acf-field-object .acf-sortable-handle {
  width: 100%;
  height: 100%;
}

.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-sortable-handle:before {
  display: none;
}

.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-field-list .acf-field-object:hover .acf-sortable-handle:before {
  display: block;
}

.post-type-acf-field-group .acf-field-object .acf-is-subfields .acf-thead .li-field-label:before {
  display: none;
}

.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open {
  border-top-color: rgb(219.125, 222.5416666667, 229.375);
}

/*---------------------------------------------------------------------------------------------
*
*  Flexible content field
*
*---------------------------------------------------------------------------------------------*/
.post-type-acf-field-group i.acf-icon.-duplicate.duplicate-layout {
  margin: 0 auto !important;
  background-color: #667085;
  color: #667085;
}
.post-type-acf-field-group i.acf-icon.acf-icon-trash.delete-layout {
  margin: 0 auto !important;
  background-color: #667085;
  color: #667085;
}
.post-type-acf-field-group button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-duplicate, .post-type-acf-field-group button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-delete {
  background-color: #ffffff !important;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);
  border-radius: 6px;
  width: 32px;
  height: 32px !important;
  min-height: 32px;
  padding: 0;
}
.post-type-acf-field-group button.add-layout.acf-btn.acf-btn-primary.add-field,
.post-type-acf-field-group .acf-sub-field-list-header a.acf-btn.acf-btn-secondary.add-field,
.post-type-acf-field-group .acf-field-list-wrap.acf-is-subfields a.acf-btn.acf-btn-secondary.add-field {
  height: 32px !important;
  min-height: 32px;
  margin-left: 5px;
}
.post-type-acf-field-group .acf-field.acf-field-setting-fc_layout {
  background-color: #ffffff;
  margin-bottom: 16px;
}
.post-type-acf-field-group .acf-field-setting-fc_layout {
  width: calc(100% - 144px);
  margin-right: 72px;
  margin-left: 72px;
  padding-right: 0;
  padding-left: 0;
  border-width: 1px;
  border-style: solid;
  border-color: rgb(219.125, 222.5416666667, 229.375);
  border-radius: 8px;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);
}
.post-type-acf-field-group .acf-field-setting-fc_layout .acf-field-layout-settings.open {
  background-color: #ffffff;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #EAECF0;
}
@media screen and (max-width: 768px) {
  .post-type-acf-field-group .acf-field-setting-fc_layout {
    width: calc(100% - 16px);
    margin-right: 8px;
    margin-left: 8px;
  }
}
.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input-sub {
  max-width: 100%;
  margin-right: 0;
  margin-left: 0;
}
.post-type-acf-field-group .acf-field-setting-fc_layout .acf-label,
.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input {
  max-width: 100% !important;
}
.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input-sub {
  margin-right: 32px;
  margin-bottom: 32px;
  margin-left: 32px;
}
.post-type-acf-field-group .acf-field-setting-fc_layout .acf-fc-meta {
  max-width: 100%;
  padding-top: 24px;
  padding-right: 32px;
  padding-left: 32px;
}
.post-type-acf-field-group .acf-field-settings-fc_head {
  display: flex;
  align-items: center;
  justify-content: left;
  background-color: #F9FAFB;
  border-radius: 8px;
  min-height: 64px;
  margin-bottom: 0px;
  padding-right: 24px;
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc_draggable {
  min-height: 64px;
  padding-left: 24px;
  display: flex;
  white-space: nowrap;
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name {
  min-width: 0;
  color: #98A2B3;
  padding-left: 8px;
  font-size: 16px;
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name.copyable:not(.input-copyable, .copy-unsupported):hover:after {
  width: 14px !important;
  height: 14px !important;
}
@media screen and (max-width: 880px) {
  .post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name {
    display: none !important;
  }
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.post-type-acf-field-group .acf-field-settings-fc_head span.toggle-indicator {
  pointer-events: none;
  margin-top: 7px;
}
.post-type-acf-field-group .acf-field-settings-fc_head label {
  display: inline-flex;
  align-items: center;
}
.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-name {
  margin-left: 1rem;
}
@media screen and (max-width: 880px) {
  .post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-name {
    display: none !important;
  }
}
.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-name span.acf-fc-layout-name {
  text-overflow: ellipsis;
  overflow: hidden;
  height: 22px;
  white-space: nowrap;
}
.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-label:before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-color: #98A2B3;
  border: none;
  border-radius: 0;
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
}
.rtl.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-label:before {
  padding-right: 10px;
}

.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions {
  display: flex;
  align-items: center;
  white-space: nowrap;
  margin-left: auto;
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions .acf-fc-add-layout {
  margin-left: 10px;
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions .acf-fc-add-layout .add-field {
  margin-left: 0px !important;
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions li {
  margin-right: 4px;
}
.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions li:last-of-type {
  margin-right: 0;
}
.post-type-acf-field-group .acf-field-settings-fc_head.open {
  border-radius: 8px 8px 0px 0px;
}

/*---------------------------------------------------------------------------------------------
*
*  Field open / closed icon state
*
*---------------------------------------------------------------------------------------------*/
.post-type-acf-field-group .acf-field-object.open > .handle > .acf-tbody > .li-field-label::before {
  -webkit-mask-image: url("../../images/icons/icon-chevron-up.svg");
  mask-image: url("../../images/icons/icon-chevron-up.svg");
}

/*---------------------------------------------------------------------------------------------
*
*  Different coloured levels (current 5 supported)
*
*---------------------------------------------------------------------------------------------*/
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .handle {
  background-color: transparent;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .handle:hover {
  background-color: rgb(248.6, 242, 251);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open .handle {
  background-color: rgb(244.76, 234.2, 248.6);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .settings {
  border-left-color: #BF7DD7;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .handle {
  background-color: transparent;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {
  background-color: rgb(234.7348066298, 247.2651933702, 244.1712707182);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object.open .handle {
  background-color: rgb(227.3524861878, 244.4475138122, 240.226519337);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .settings {
  border-left-color: #7CCDB9;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle {
  background-color: transparent;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {
  background-color: rgb(252.2544378698, 244.8698224852, 241.7455621302);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object.open .handle {
  background-color: rgb(250.5041420118, 238.4118343195, 233.2958579882);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .settings {
  border-left-color: #E29473;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle {
  background-color: transparent;
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {
  background-color: rgb(249.8888888889, 250.6666666667, 251.1111111111);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object.open .handle {
  background-color: rgb(244.0962962963, 245.7555555556, 246.7037037037);
}
.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .settings {
  border-left-color: #A3B1B9;
}

/*# sourceMappingURL=acf-field-group.css.map*/