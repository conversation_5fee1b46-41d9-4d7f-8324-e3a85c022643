{"version": 3, "file": "acf-field-group.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;;;;8FAAA;AAMA;AAOA;AAQA;AAgBA;;;;8FAAA;ACrCA;;;;8FAAA;ACAA;;;;8FAAA;AAOA;;;EAGC;EACA;AHkBD;;AGbC;;EAEC;AHgBF;;AGZA;;;;8EAAA;AAKA;;;EAGC;AHeD;;AGZA;EACC;AHeD;;AGZA;EACC;AHeD;;AGZA;EACC;AHeD;;AGXA;;;;8EAAA;AAKA;EACC;EASA;EAKA;EA8BA;EAeA;EAUA;EAyCA;AH1FD;AGlBC;EAEE;EACA;AHmBH;AGdC;EACC;AHgBF;AGVE;EAEE;AHWJ;AGRG;EALD;IAME;EHWF;AACF;AGPE;EACC;AHSH;AGNE;EACC;EACA;EACA;AHQH;AGNG;EACC;AHQJ;AGDC;EACC;EACA;AHGF;AGDE;EAJD;IAKE;EHID;AACF;AGDC;EAAkB;AHInB;AGHC;EAAiB;EAAY;AHO9B;AGNC;EAAgB;AHSjB;AGRC;EAAiB;AHWlB;AGNE;EAAkB;AHSpB;AGRE;EAAiB;AHWnB;AGVE;EAAgB;EAAa;AHc/B;AGbE;EAAiB;AHgBnB;AGVE;EACC;AHYH;AGTE;EACC;AHWH;AGTG;EACC;AHWJ;AGRG;EACC;AHUJ;AGPG;EACC;EACA;AHSJ;AGNG;EAEE;EACA;EACA,4BFrGM;AD4GX;AGHG;EACC;EACA;AHKJ;AGDE;EACC;AHGH;AGEC;EACC;AHAF;AGGC;EACC;EACA;EA2GA;EAOA;AHjHF;AGGG;;EAEC;AHDJ;AGME;EACC;EACA;EACA;AHJH;AGMG;EACC;EACA;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ,yBF/IO;EEgJP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHLJ;AGQG;EACC;AHNJ;AGQI;EACC;AHNL;AGQK;EAEC,WADY;EAEZ,YAFY;EAGZ;AHPN;AGYG;EACC;EACA;EACA,yBFzNU;AD+Md;AGcE;EACC;EACA;EACA;EACA;AHZH;AGcG;EACC;AHZJ;AGeG;EACC;EACA;EAEA;EACA;EACA;EACA,WAJY;EAKZ,YALY;EAMZ,yBF1MO;EE2MP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHdJ;AGiBG;EACC;EACA;EACA,yBFpQU;ADqPd;AGsBE;EACC;EACA;EACA;AHpBH;AGyBG;EACC;AHvBJ;AG8BE;EACC,qBFrQkB;ADyOrB;;AGoCE;EAEE;EACA;AHlCJ;;AGwCA;AACA;EACC;EACA;EAEA;EA+BA;EAMA;EA0DA;EA2BA;;;;;;;;;;;;;GAAA;EAgBA;EAcA;EAWA;AHrLD;AGmBC;EACC;EAEC;EACA;EACA;EAED,kBFhPU;EEiPV;AHnBF;AGqBE;EACC;AHnBH;AGwBC;EACC;EACA;EACA;EACA;EACA;AHtBF;AGyBE;EACC;AHvBH;AG6BC;EACC;AH3BF;AGkCE;EACC;EACA;EACA;EACA;AHhCH;AGmCE;EACC;AHjCH;AGoCE;EACC;EACA;EACA;EACA;EACA;AHlCH;AGqCE;EACC;EACA;EAEC;AHpCJ;AGuCG;EAPD;IAQE;IAEC;EHrCH;AACF;AGwCG;EACC;AHtCJ;AGwCI;EACC;AHtCL;AG2CG;EACC;AHzCJ;AG2CI;EAAU;AHxCd;AG2CG;EACC;AHzCJ;AGkDE;EACC;AHhDH;AGmDE;EACC,mBF5ZQ;EE6ZR;EACA;EACA;EACA;EACA;AHjDH;AGmDG;EACC;AHjDJ;AGmDI;EACC;AHjDL;AG8EG;EACC;EACA;AH5EJ;AGoFC;EACC;EACA;AHlFF;AGoFE;EACC;AHlFH;AGwFC;EACC;AHtFF;;AG4FA;;;;8EAAA;AAQC;EACC;AH5FF;AG+FC;EACC;AH7FF;AG+FE;EACC;AH7FH;AGgGE;EACC;AH9FH;AGiGE;EACC;AH/FH;AGkGE;EACC;AHhGH;AGmGE;EACC;EACA;AHjGH;AGmGG;EACC;EACA;EACA;AHjGJ;AGmGI;EACC;EACA;EACA;AHjGL;AGuGE;EACC;AHrGH;AGyGE;EACC;AHvGH;AG8GG;EACC;EACA;AH5GJ;;AGmHA;;;;8EAAA;AAMA;EACC;EACA;AHjHD;;AGoHA;EAEC;IACC;EHlHA;AACF;AGuHA;;;;8EAAA;AAMA;EACC;EACA;EACA;AHtHD;;AGyHA;EACC;EACA;EACA;AHtHD;;AG0HA;;;;8EAAA;AASC;;;;;EAKC;AH3HF;AG+HC;EACC;AH7HF;AGgIC;EACC;AH9HF;AGkIC;;EAEC;AHhIF;;AGoIA;;;;8EAAA;AASC;;;;;EAKC;AHrIF;AGyIC;EACC;AHvIF;AG0IC;EACC;AHxIF;AG4IC;EACC;AH1IF;;AGgJA;;;;8EAAA;AAMA;;;EAGC;AH9ID;;AGiJA;EACC;AH9ID;;AGiJA;EACC;AH9ID;;AGkJA;;;;8EAAA;AAMA;;;EAGC;AHhJD;;AGoJA;;;;8EAAA;AAYE;;;EACC;AHtJH;AGyJE;;;EACC;EACA;AHrJH;AGwJE;;;EACC;AHpJH;;AG8JE;EACC;AH3JH;AG8JE;EACC;AH5JH;;AGmKA;;;;8FAAA;AAQC;EACC;EACA;AHnKF;AGsKC;EACC;EACA;EACA;AHpKF;;AGyKA;;;;8FAAA;AAMA;EACC;AHvKD;;AG0KA;;;;8EAAA;AAMA;EAEC;;;IAGC;IACA;IACA;EHzKA;EG4KD;IACC;IACA;EH1KA;EG6KD;IACC;IACA;EH3KA;AACF;AGgLA;;;;8EAAA;AASE;;EAEC,yBFjwBQ;AD+kBX;;AI3nBA;;;;+FAAA;AAMC;EACC;AJ6nBF;;AIznBA;;;;+FAAA;AAOC;EACC,cH0CS;ADglBX;;AIrnBA;;;;+FAAA;AAMA;;EACC;EACA;AJwnBD;;AIrnBA;;EACC;EACA;AJynBD;;AItnBA;;;;;EACC;EACA;AJ6nBD;;AIzmBA;;;;+FAAA;AAQC;EACC;AJymBF;AItmBC;EACC;AJwmBF;AIrmBC;EACC;AJumBF;AIpmBC;;;;;;EACC;AJ2mBF;AIxmBC;;;;;;;;;;;EACC;AJonBF;AIjnBC;EACC;AJmnBF;AIhnBC;EACC;AJknBF;AI/mBC;EACC;AJinBF;;AI5mBA;;;;+FAAA;AAKA;EAEC,cH5DU;AD0qBX;;AI3mBA;;;;+FAAA;AAOC;EACC;AJ4mBF;AIzmBC;EACC;AJ2mBF;;AItmBA;;;;+FAAA;AASA;;;;+FAAA;AAMC;EACC;EACA;AJomBF;AIjmBC;EACC;EACA;AJmmBF;;AK5vBA;EAEC;;;;iGAAA;EAwCA;;;;iGAAA;EAcA;;;;iGAAA;EAcA;;;;iGAAA;EAeA;;;;iGAAA;EA6CA;;;;iGAAA;EAyEA;;;;iGAAA;EAkBA;;;;iGAAA;EAkBA;;;;iGAAA;EAqCA;;;;iGAAA;EA0GA;;;;iGAAA;EAqCA;;;;iGAAA;EAmCA;;;;iGAAA;EASA;;;;iGAAA;EA6IA;;;;iGAAA;EA+BA;;;;iGAAA;EAsBA;EAiVA;;;;iGAAA;AL7ID;AK90BC;;;;;EAKC;EACA;EAEC;EACA;EAED;EACA,qBJ4BS;EI3BT,6CJoEa;EInEb,kBJ8DU;EI7DV;EAEA,cJ2BS;ADkzBX;AK30BE;;;;;EACC,0BJgEO;EI/DP,qBJgCQ;ADizBX;AK90BE;;;;;EACC,yBJYQ;EIXR;ALo1BH;AKj1BE;;;;;EACC,cJWQ;AD40BX;AK30BE;EACC,yBJNQ;EIOR,cJHQ;ADg1BX;AKj0BE;;EAEC;ALm0BH;AKzzBC;EACC;EAEC;EACA;EAED;EACA;ALyzBF;AKjzBC;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;ALizBF;AK9yBE;EAEC,cJ3CQ;AD01BX;AK5yBE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AL8yBH;AKvyBE;EAEE;EACA;EAED;ALuyBH;AK9xBC;;EAEC;EACA;EACA;EACA;EAEC;EACA;EACA,qBJhGQ;EIkGT;EACA;AL8xBF;AK5xBE;;EACC,yBJ9FQ;EI+FR,qBJ1FQ;ADy3BX;AK5xBE;;;EAEC,yBJpGQ;EIqGR,qBJhGQ;AD+3BX;AK7xBG;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALiyBJ;AK5xBE;;EACC;AL+xBH;AK5xBE;;EACC,yBJzIQ;EI0IR,qBJvIQ;ADs6BX;AKlxBI;;;EACC;ALsxBL;AKrwBG;EACC;ALuwBJ;AKtvBG;EACC;ALwvBJ;AKzuBE;;;;EAGE;AL4uBJ;AKxuBE;;EAEE;AL0uBJ;AKvuBG;;EAEE;ALyuBL;AKluBE;;EACC;EACA;EACA;ALquBH;AK3tBC;EACC;EACA;EACA;EACA,yBJ9OS;EI+OT;AL6tBF;AK3tBE;EACC,yBJjPQ;AD88BX;AK1tBE;EACC;AL4tBH;AKztBE;EACC,yBJ5OQ;ADu8BX;AKztBG;EACC,yBJ9OO;ADy8BX;AKxtBG;EACC;AL0tBJ;AKrtBE;;EAEC;ALutBH;AKptBE;EACC;EACA;EACA;EACA;EACA;ALstBH;AKjtBC;EACC;EACA;ALmtBF;AKjtBE;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;ALktBJ;AK/sBG;EAEE;ALgtBL;AK5sBG;EAEE;AL6sBL;AKzsBG;EACC;EAEC;EACA;AL0sBL;AK/rBG;EAEE;EACA;ALgsBL;AK5rBG;EAEE;EACA;AL6rBL;AKjrBC;EACC;EACA;EAEC;EAGA;EACA;EACA;EACA;EAED;EACA;EACA,kBJ/TU;EIiUT;EACA;EACA,qBJzVQ;EI2VT;AL6qBF;AK3qBE;EACC,qBJ7VQ;EI8VR;EACA;AL6qBH;AKlqBC;EACC;EACA;EACA;EAEC;EACA;EAED;EACA;EACA;EACA,qBJtXS;EIuXT,kBJjWU;EImWV,cJzXS;AD0hCX;AK/pBE;EACC;EACA,qBJ7XQ;EI8XR,cJ9XQ;AD+hCX;AK9pBE;EACC;EACA,0BJrWO;EIsWP,cJpYQ;ADoiCX;AKtpBC;EACC;ALwpBF;AK7oBE;;EACC;EACA;ALgpBH;AK7oBE;;EACC;EAEC;EACA;EAED;EAEC;EACA;EACA,qBJvbO;EIybR,6CJhZY;EIiZZ,kBJtZS;EIuZT;EAEA,cJzbQ;ADokCX;AKxoBE;;EACC;EACA;EACA;EACA;AL2oBH;AKxoBE;;EACC;AL2oBH;AKxoBE;;EACC;AL2oBH;AKxoBE;;EACC,qBJncQ;AD8kCX;AKxoBE;;EACC,0BJxaO;EIyaP,qBJxcQ;EIycR,kBJlbS;AD6jCZ;AKzoBG;;EACC;AL4oBJ;AKvoBI;;EACC;EACA;AL0oBL;AKnoBI;;EACC;EACA;ALsoBL;AK/nBE;;EACC;EAEC;ALioBJ;AK9nBG;;EACC;EACA;ALioBJ;AK5nBE;;EAEE;EACA;EACA;EACA;AL8nBJ;AK1nBE;;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;EACA;AL2nBH;AKznBG;;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBJniBO;AD8pCX;AKxnBG;;EACC,yBJ1hBO;ADqpCX;AKjnBC;EACC;EACA;EACA;ALmnBF;AKjnBE;EAEC,WADY;EAEZ,YAFY;EAGZ,yBJ1jBQ;AD4qCX;AK/mBE;EAEE;ALgnBJ;AK5mBE;EAEE;AL6mBJ;AKlmBC;EACC;EACA;EACA;EACA;ALomBF;AKlmBW;EACR;EACA;ALomBH;;AKjmBE;EACC;EACA;ALomBH;AKllBE;;;;;;;;;;;;EACC;AL+lBH;AK1lBG;;;;;;;;;;;;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;ALsmBL;AKlmBG;;;;;;;;;;;;EACC;EACA;EACA;EAEC;AL8mBL;AK3mBI;;;;;;;;;;;;EACC;EACA;ALwnBL;AKlnBE;;;;;;;;;;;;EACC;EACA;AL+nBH;AK5nBE;;;;;;;;;;;;EACC;EACA;ALyoBH;AKtoBE;;;;;;;;;;;;EACC;EACA;EACA;EACA;ALmpBH;AK/oBE;;;;;;;;;;;;EACC;AL4pBH;AK1pBY;EACR;AL4pBJ;;AKvpBE;;;;;;;;;;;;EACC;EACA;EACA;EACA;EACA;ALqqBH;AKnqBG;;;;;;;;;;;;EACC;EAEA;EACA;EACA;EACA;EACA;EACA,WANY;EAOZ,YAPY;EAQZ;EACA;EACA,yBJhsBO;EIisBP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AL+qBJ;AK5qBG;;;;;;;;;;;;EACC;ALyrBJ;AKhrBG;;;;;;;;;;;;EACC;EACA;AL6rBJ;AKtrBC;EACC,yBJvuBS;EIwuBT;EACA;EACA,cJtuBS;EIuuBT;EACA;EACA;EACA;EACA;ALwrBF;AKrrBC;EACC;EACA;EACA;EACA;EACA;ALurBF;AKrrBE;EACC;EACA;EACA;EACA;EACA;ALurBH;AKprBW;EAER;ALqrBH;;AKjrBE;EACC;ALorBH;AKlrBY;EACR;ALorBJ;;AK/qBE;EACC;EACA;EACA;ALkrBH;AK9qBI;EACC;EAEA;EACA;EACA;EACA;EACA,WALY;EAMZ,YANY;EAOZ;EACA;EACA,yBJ9xBM;EI+xBN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AL+qBL;AK7qBc;EACR;EACA;AL+qBN;;AK1qBG;EACC;EAEA;EACA;EACA;EACA;AL4qBJ;AK1qBa;EACR;EACA;AL4qBL;;AKzqBI;EACC,yBJj0BM;EIk0BN;AL4qBL;AKtqBE;EACC;ALwqBH;AKnqBG;EACC;EACA;ALqqBJ;AKhqBE;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;ALgqBH;AK9pBG;EACC;EACA;EACA;EAEC;EAED;AL8pBJ;AK5pBI;EACC;EACA;AL8pBL;AKxpBE;EACC;EACA;AL0pBH;AKxpBG;EACC;EAEA;EACA;EACA,WAHY;EAIZ,YAJY;EAKZ;EACA;EACA,yBJl3BO;EIm3BP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALypBJ;AKvpBa;EACR;EACA;ALypBL;;AKppBE;EACC;EACA;EACA;EACA;EACA,yBJ55BQ;EI85BP;EACA;EACA,yBJ95BO;EIi6BP;EACA;EACA,4BJn6BO;EIq6BR,cJn6BQ;EIo6BR;EAEC;EAGA;EACA;EACA;EACA;EAED;AL+oBH;AKhoBG;;;EACC;EACA;ALooBJ;;AK3nBC;;EACC;EACA;AL+nBF;;AMznDA;;;;8EAAA;AAMC;;;;EAIC,iBLuFU;ADoiDZ;;AMvnDA;;;;8EAAA;AAMC;EACC,iBL4EU;AD6iDZ;;AMrnDA;;;;8EAAA;AAMC;EACC;ANunDF;;AMnnDA;;;;8EAAA;AAMC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;ANknDH;;AM7mDA;;;;8EAAA;AAMC;EACC;EACA;EACA;EACA,6CLoBa;AD2lDf;AM7mDE;EAEE;EACA;EACA,yBL5BO;AD0oDX;AM3mDG;;EAEC;AN6mDJ;AM1mDG;EACC;AN4mDJ;;AMtmDA;;;;8EAAA;AAMC;EACC,yBLpDS;EKsDR;EACA;EACA,yBLtDQ;EKyDR;EACA;EACA,4BL3DQ;ADgqDX;AMlmDE;EACC;EACA;EACA;EAEC;EACA;EAGD,cLlEQ;EKmER;ANimDH;;AM5lDA;;;;8EAAA;AAMC;EAEE;EACA;EACA,yBLvFQ;ADorDX;AMzlDG;EACC;AN2lDJ;AMrlDG;EACC;EACA;EACA;EACA;EACA,mBLtGO;EKuGP;ANulDJ;AMnlDI;EACC;ANqlDL;AMllDI;EACC;EACA;EACA;EACA;EACA,mBLpHM;EKqHN;ANolDL;AM/kDE;EACC;ANilDH;AM9kDE;EACC;EACA,yBLrHQ;ADqsDX;AM7kDE;EACC,yBL1HQ;EK2HR;EACA;AN+kDH;AM7kDG;EACC;AN+kDJ;AM7kDI;EACC;AN+kDL;AM1kDE;EACC;AN4kDH;AM1kDG;EACC;AN4kDJ;AM1kDI;EACC;EACA;AN4kDL;AMzkDI;EACC;AN2kDL;AMtkDE;EACC;EACA;ANwkDH;AMrkDE;EACC;EACA;EACA;EACA;EAEA,cLzKQ;AD+uDX;AMpkDG;EACC;EACA;EACA;EACA;EACA;EACA;ANskDJ;AMhkDI;EACC;ANkkDL;AM/jDI;EACC;ANikDL;AMtjDA;;;;8EAAA;AAMC;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;EAEC;EACA;EACA,yBLlOQ;ADsxDX;AMjjDE;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;ANgjDJ;;AM1iDA;;;;8EAAA;AAKA;EACC;EAEC;EACA;EAED;EAEC;EACA;EACA,0BLxPS;ADkyDX;;AMtiDA;;;;8EAAA;AAKA;EAEE;EACA;EACA;EACA;ANwiDF;AMriDC;;EAGE;ANsiDH;;AMjiDA;;;;8EAAA;AAKA;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;ANmiDF;;AM/hDA;;;;8EAAA;AAKA;EACC;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;AN+hDF;AM5hDC;EAhBD;IAkBG;IACA;EN8hDD;AACF;AM3hDC;;EAEC;AN6hDF;AM3hDE;;EACC;AN8hDH;AM1hDG;;EACC,yBLvVO;EKwVP;EACA;EACA;AN6hDJ;AMxhDC;EACC;AN0hDF;;AMthDA;;;;8EAAA;AAMA;;EAGE;EAGA;EACA;EACA,yBLjXS;ADs4DX;;AMjhDA;EAEE;ANmhDF;;AM/gDA;;;;8EAAA;AAMC;EACC;ANihDF;AM9gDC;EACC,yBLxYS;EKyYT;EACA;EACA;EACA,cLrYS;EKsYT;ANghDF;AM9gDE;EACC;ANghDH;AM/gDG;EACC;EAEA;EACA;EACA;EACA;EACA;EACA,WANY;EAOZ,YAPY;EASX;EAED,yBLzZO;EK0ZP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN8gDJ;;AMxgDA;;;;8EAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA,yBLtcS;AD48DX;AMngDC;EAxBD;IA0BG;ENqgDD;AACF;;AMjgDA;EAEE;EACA;EACA;EACA;ANmgDF;;AM//CA;;;;8EAAA;AAQC;;;EACC,mBLpeS;EKseR,4BL9dQ;AD89DX;AM7/CE;;;EAEE;EACA;EAGA;EAGA;EACA;EACA,4BLlfO;AD8+DX;AMv/CC;;;;;;EAEC;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA,4BLzgBQ;ADigEX;AMt/CE;;;;;;EACC;EAEC;EACA;EACA;EACA;EAED;AN2/CH;AMz/CG;;;;;;EAKC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;EACA;EACA;EAEC;EACA;EACA;EAED;EACA,cL1iBO;EK4iBP;ANu/CJ;AMhhDI;;;;;;EACC;ANuhDL;AM7/CI;;;;;;EACC,cL5iBM;ADgjEX;AMjgDI;;;;;;EACC;ANwgDL;AMpgDG;;;;;;EACC;EAEC,4BL9iBM;EKgjBP,cLhjBO;ADyjEX;AMvgDI;;;;;;EAEE,4BLpjBK;EKqjBL;AN6gDN;;AMrgDA;EAIE;ANqgDF;AMlgDC;EAPD;IASG;ENogDD;AACF;;AMhgDA;;;;8EAAA;AAMC;EAEE;EACA;EACA;EACA;ANigDH;AM9/CE;EACC;ANggDH;AM5/CC;EACC;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;AN2/CH;AMt/CC;EACC;EAEC;EACA;EACA;EACA;ANu/CH;AMp/CE;EACC;EAEC;EACA;EACA;EACA;ANq/CJ;AMj/CE;EACC;ANm/CH;AMh/CE;EACC;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA,0BLrqBO;ADkpEX;AMv+CC;EACC;ANy+CF;AMr+CC;EACC;ANu+CF;;AMj+CE;EAEE;EACA;EAED;EAEC;EACA;EACA,2BLhsBO;ADiqEX;;AM39CA;;;;8EAAA;AAMC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN69CF;AM19CC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EAGD;EACA;EACA;ANy9CF;AMv9CE;EACC;ANy9CH;AMt9CE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,WADY;EAEZ,YAFY;EAGZ,yBLvvBQ;EKwvBR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANu9CH;;AMh9CE;EACC;EACA;ANm9CH;;AM98CA;;;;8EAAA;AAMC;EACC;EAEC;AN+8CH;AM58CE;EACC;EACA;EACA;EACA;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBLzyBQ;EK0yBR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN28CH;AMx8CE;EACC;AN08CH;;AMn8CE;EAEE;EACA;ANq8CJ;AMl8CG;EACC;EACA;EACA;EACA;ANo8CJ;AM97CG;EACC;EACA;ANg8CJ;AM77CG;EACC;EACA;AN+7CJ;AM57CG;EACC;EACA;AN87CJ;;AMv7CC;EAEE;ANy7CH;AMr7CE;EAEE;EACA;ANs7CJ;;AMh7CA;;;;8EAAA;AAOC;EACC;EAEC;EACA;ANg7CH;AM76CE;EAPD;IASG;EN+6CF;AACF;AM36CC;EACC;EAEC;EAGA;EACA;AN06CH;AMv6CE;EACC;EACA;EAEC;EAGA;EACA;EACA;EAGA;EACA;EACA,yBLn6BO;ADu0EX;AMj6CG;EAjBD;IAmBG;ENm6CH;EMh6CE;IACC;IACA;IACA;IACA;IACA;ENk6CH;EMh6CG;IACC;ENk6CJ;AACF;;AM35CA;;;;;EAKC,cL97BU;EK+7BV;AN85CD;;AM35CA;EACC;EACA;EACA;EACA;EACA;AN85CD;;AM35CA;EACC;EACA;EACA;EACA;AN85CD;;AM35CA;;;;8EAAA;AAMC;;EAEC;EACA;EACA;EACA;EAEC;EACA;EAED,yBLr+BS;EKs+BT,qBLn+BS;EKo+BT,6CL37Ba;EK47Bb,cLn+BS;AD83EX;AMx5CC;EACC;AN05CF;AMv5CC;EACC;ANy5CF;;AMr5CA;;;;8EAAA;AAKA;EACC;ANw5CD;;AMr5CA;EACC;ANw5CD;;AMr5CA;;;;8EAAA;AAKA;EAIC;EACA;EAEC;EACA;ANo5CF;AMj5CC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;ANm5CF;AMj5CE;;;EAGC;ANm5CH;AMh5CE;EAGE;EACA;EAED;EACA,cLhiCQ;AD+6EX;AM54CE;EAGE;EACA;EAED;EACA,cL5iCQ;ADu7EX;AMz4CG;EAGE;ANy4CL;AMp4CE;EACC;EAEC;ANq4CJ;AMj4CE;EAEE;ANk4CJ;;AM53CA;;;;8EAAA;AAOE;EACC;AN63CH;;AMx3CA;;;;8EAAA;AAMC;EACC;AN03CF;AMx3CE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EAGA;EACA;EACA,4BL7mCO;ADo+EX;AMp3CG;;;;EAME;EAED,cLnnCO;ADs+EX;AMh3CG;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANk3CJ;AMh3CI;EAEC;EACA;EACA;EACA;EACA,WALY;EAMZ,YANY;EAOZ,yBL1oCM;EK2oCN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANi3CL;AM92CI;EACC,yBL/oCM;AD+/EX;AM32CE;EACC;EACA;EAEC;EACA;EACA;EACA;EAED;AN22CH;AMz2CG;EAEE;EACA;AN02CL;AMl2CG;;EAEE;ANo2CL;AM31CE;;EACC,cL/rCQ;EKgsCR;AN81CH;AM51CG;;EACC;EACA;EACA;EACA,mBL1sCO;EK2sCP;AN+1CJ;AM31CE;;EACC;AN81CH;AM51CG;;;;EAEC;EACA;ANg2CJ;AM91CI;;;;EACC;ANm2CL;AM/1CG;;EACC,mBLjuCO;EKkuCP;EAEC;EACA;EACA;EACA;EAED;EACA;ANg2CJ;AM91CI;;EACC;EACA;ANi2CL;AM/1CK;;EACC;ANk2CN;AMz1CG;EACC;AN21CJ;AMx1CG;EACC;AN01CJ;AMv1CG;EACC;EACA;ANy1CJ;AMt1CK;EACC;ANw1CN;AMt1CM;EACC;EACA,mGACC;EAED;EACA;EACA;EACA;ANs1CP;AMj1CI;EACC;EACA,cL9vCU;ADilFf;AM/0CG;EACC;ANi1CJ;AM/0CI;EACC,yBLhxCM;ADimFX;;AM10CA;;;;8EAAA;AAMC;EACC;AN40CF;;AMx0CA;;;;8EAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA,yBLz0CS;AD+oFX;AMn0CC;EACC;EACA;EACA;EACA;EAEA,cL/0CS;ADmpFX;AMl0CE;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBL31CQ;EK41CR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANi0CH;;AM5zCA;;;;8EAAA;AAKA;EACC;AN+zCD;AM7zCC;EACC;EACA;EACA;EACA;EAEC;EAED;EAEC;EACA;EACA,qBLz2Ca;EK22Cd;EACA;EACA;AN2zCF;;AMvzCA;;;;8EAAA;AAKA;EACC;EACA;AN0zCD;;AMvzCA;;;;8EAAA;AAKA;EACC;EAEC;EACA;EACA,yBL55CS;ADqtFX;AMvzCC;EACC;EAEC;EACA;ANwzCH;AMrzCE;EAEE;EACA;EACA,0BLz6CO;AD+tFX;;AMhzCA;EACC;IACC;IACA;ENmzCA;EMjzCD;IACC;IACA;IACA,yBLv7CS;ED0uFT;AACF;AMhzCA;;;;8EAAA;AAOC;;EAEE;ANgzCH;AM3yCE;;EACC;AN8yCH;AM5yCG;;EACC;EACA;AN+yCJ;AM7yCI;;EACC;ANgzCL;AM7yCI;;EAEE;EACA;EAED,yBL19CM;EK29CN;EAEA,cLx9CM;ADqwFX;AMzyCG;;EACC;AN4yCJ;;AMtyCA;EAEE;EAGA;EACA;EACA,yBL9+CS;ADoxFX;;AMlyCA;;;;8EAAA;AAOC;;EACC;ANoyCF;;AMhyCA;;;;8EAAA;AAKA;EACC;ANmyCD;AMjyCC;EACC;ANmyCF;;AM/xCA;;;;8EAAA;AAOC;;EACC;ANiyCF;AM9xCC;;EACC;ANiyCF;;AM7xCA;;;;8EAAA;AAOE;EACC;AN8xCH;AMxxCG;EACC;AN0xCJ;;AMpxCA;;;;8EAAA;AAUC;;EAEC;ANkxCF;;AM9wCA;;;;8EAAA;AAOC;EAEC;AN8wCF;AM5wCE;EACC;AN8wCH;;AMzwCA;;;;8EAAA;AAKA;EAEE;AN2wCF;;AOj5FA;;;;+FAAA;AAKA;EACC;EACA;EACA,kBN4EW;EM1EV;EACA;EACA;EAED,6CN0Ec;ADw0Ff;AO/4FC;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;APg5FH;AO34FC;EACC;AP64FF;AOz4FC;EACC;EACA;AP24FF;AOv4FC;EACC;EAEC;EACA;APw4FH;;AOl4FA;EACC;EACA;APq4FD;;AOl4FA;EACC;APq4FD;;AOl4FA;EACC;APq4FD;;AOl4FA;EACC;APq4FD;;AOl4FA;EACC;APq4FD;;AOl4FA;;;;+FAAA;AAOC;EACC;EACA,yBNhCS;EMiCT,cNjCS;ADo6FX;AOj4FC;EACC;EACA,yBNrCS;EMsCT,cNtCS;ADy6FX;AOh4FC;EACC;EACA,6CNJa;EMKb;EACA;EACA;EACA;EACA;APk4FF;AO/3FC;;;EAGC;EACA;EACA;APi4FF;AO93FC;EACC;EACA;APg4FF;AO73FC;EAUC;EAEC;EACA;EAGA;EACA;EAGA;EACA;EACA;EAED,kBNrDU;EMsDV,6CNlDa;ADk6Ff;AOx4FE;EACC;EAEC;EACA;EACA,yBNzEO;ADk9FX;AOp3FE;EA3BD;IA4BE;IAEC;IACA;EPs3FF;AACF;AOl3FE;EACC;EAEC;EACA;APm3FJ;AO/2FE;;EAEC;APi3FH;AO92FE;EAEE;EACA;EACA;AP+2FJ;AO32FE;EACC;EAEC;EACA;EACA;AP42FJ;AOt2FC;EAEC;EACA;EACA;EAEA,yBN/IS;EMgJT;EACA;EAEC;EAGA;APm2FH;AOh2FE;EACC;EACA;EACA;EACA;APk2FH;AO/1FE;EACC;EACA,cN9JQ;EM+JR;EACA;APi2FH;AO/1FG;EACC;EACA;APi2FJ;AO91FG;EAXD;IAYE;EPi2FF;AACF;AO/1FG;EACC;EACA;EACA;APi2FJ;AO71FE;EACC;EACA;AP+1FH;AO51FE;EACC;EACA;AP81FH;AO31FG;EACC;AP61FJ;AO31FI;EAHD;IAIE;EP81FH;AACF;AO51FI;EACC;EACA;EACA;EACA;AP81FL;AO11FG;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBNpNO;EMqNP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;APy1FJ;AOv1Fa;EACR;APy1FL;;AOl1FE;EACC;EACA;EACA;EACA;APq1FH;AOn1FG;EACC;APq1FJ;AOl1FG;EACC;APo1FJ;AOj1FG;EAEE;APk1FL;AO/0FI;EAEE;APg1FN;AOt0FC;EACC;APw0FF;;AOn0FA;;;;+FAAA;AAMA;EACC;EACA;APq0FD;;AOl0FA;;;;+FAAA;AAWC;EAA4B;APg0F7B;AOh0F4D;EAAU;APm0FtE;AOj0FC;EAAiC;APo0FlC;AOl0FC;EAA6C,0BAN9B;AP20FhB;AO/zFE;EAA4B;APk0F9B;AOl0F6D;EAAU;APq0FvE;AOn0FE;EAAiC;APs0FnC;AOp0FE;EAA6C,0BAN9B;AP60FjB;AOj0FG;EAA4B;APo0F/B;AOp0F8D;EAAU;APu0FxE;AOr0FG;EAAiC;APw0FpC;AOt0FG;EAA6C,0BAN9B;AP+0FlB;AOn0FI;EAA4B;APs0FhC;AOt0F+D;EAAU;APy0FzE;AOv0FI;EAAiC;AP00FrC;AOx0FI;EAA6C,0BAN9B;APi1FnB,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/acf-field-group.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_variables.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_mixins.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_field-group.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_typography.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_admin-inputs.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_edit-field-group.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_sub-field-groups.scss"], "sourcesContent": ["@charset \"UTF-8\";\n/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n/* colors */\n/* acf-field */\n/* responsive */\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*\tField Group\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-fields > .inside,\n#acf-field-group-locations > .inside,\n#acf-field-group-options > .inside {\n  padding: 0;\n  margin: 0;\n}\n\n.postbox .handle-order-higher,\n.postbox .handle-order-lower {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Publish\n*\n*----------------------------------------------------------------------------*/\n#minor-publishing-actions,\n#misc-publishing-actions #visibility,\n#misc-publishing-actions .edit-timestamp {\n  display: none;\n}\n\n#minor-publishing {\n  border-bottom: 0 none;\n}\n\n#misc-pub-section {\n  border-bottom: 0 none;\n}\n\n#misc-publishing-actions .misc-pub-section {\n  border-bottom-color: #F5F5F5;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Fields\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-fields {\n  border: 0 none;\n  /* links */\n  /* Field type */\n  /* table header */\n  /* show keys */\n  /* hide tabs */\n  /* fields */\n}\n#acf-field-group-fields .inside {\n  border-top-width: 0;\n  border-top-style: none;\n}\n#acf-field-group-fields a {\n  text-decoration: none;\n}\n#acf-field-group-fields .li-field-type .field-type-icon {\n  margin-right: 8px;\n}\n@media screen and (max-width: 600px) {\n  #acf-field-group-fields .li-field-type .field-type-icon {\n    display: none;\n  }\n}\n#acf-field-group-fields .li-field-type .field-type-label {\n  display: flex;\n}\n#acf-field-group-fields .li-field-type .acf-pro-label-field-type {\n  position: relative;\n  top: -3px;\n  margin-left: 8px;\n}\n#acf-field-group-fields .li-field-type .acf-pro-label-field-type img {\n  max-width: 34px;\n}\n#acf-field-group-fields .li-field-order {\n  width: 64px;\n  justify-content: center;\n}\n@media screen and (max-width: 880px) {\n  #acf-field-group-fields .li-field-order {\n    width: 32px;\n  }\n}\n#acf-field-group-fields .li-field-label {\n  width: calc(50% - 64px);\n}\n#acf-field-group-fields .li-field-name {\n  width: 25%;\n  word-break: break-word;\n}\n#acf-field-group-fields .li-field-key {\n  display: none;\n}\n#acf-field-group-fields .li-field-type {\n  width: 25%;\n}\n#acf-field-group-fields.show-field-keys .li-field-label {\n  width: calc(35% - 64px);\n}\n#acf-field-group-fields.show-field-keys .li-field-name {\n  width: 15%;\n}\n#acf-field-group-fields.show-field-keys .li-field-key {\n  width: 25%;\n  display: flex;\n}\n#acf-field-group-fields.show-field-keys .li-field-type {\n  width: 25%;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-tab-bar {\n  display: none;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main {\n  padding: 0;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main.acf-field-settings-main-general {\n  padding-top: 32px;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field {\n  margin-bottom: 32px;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-setting-wrapper {\n  padding-top: 0;\n  border-top: none;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-settings-split .acf-field {\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-setting-first_day {\n  padding-top: 0;\n  border-top: none;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-footer {\n  margin-top: 32px;\n}\n#acf-field-group-fields .acf-field-list-wrap {\n  border: #ccd0d4 solid 1px;\n}\n#acf-field-group-fields .acf-field-list {\n  background: #f5f5f5;\n  margin-top: -1px;\n  /* no fields */\n  /* empty */\n}\n#acf-field-group-fields .acf-field-list .acf-tbody > .li-field-name,\n#acf-field-group-fields .acf-field-list .acf-tbody > .li-field-key {\n  align-items: flex-start;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported) {\n  cursor: pointer;\n  display: inline-flex;\n  align-items: center;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported):hover:after {\n  content: \"\";\n  padding-left: 5px;\n  display: inline-flex;\n  width: 12px;\n  height: 12px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-copy.svg\");\n  mask-image: url(\"../../images/icons/icon-copy.svg\");\n  background-size: cover;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).sub-label {\n  padding-right: 22px;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).sub-label:hover {\n  padding-right: 0;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).sub-label:hover:after {\n  width: 14px;\n  height: 14px;\n  padding-left: 8px;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.input-copyable, .copy-unsupported).copied:hover:after {\n  -webkit-mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  background-color: #49ad52;\n}\n#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported) {\n  cursor: pointer;\n  display: block;\n  position: relative;\n  align-items: center;\n}\n#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported) input {\n  padding-right: 40px;\n}\n#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported) .acf-input-wrap:after {\n  content: \"\";\n  padding-left: 5px;\n  right: 12px;\n  top: 12px;\n  position: absolute;\n  width: 16px;\n  height: 16px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-copy.svg\");\n  mask-image: url(\"../../images/icons/icon-copy.svg\");\n  background-size: cover;\n}\n#acf-field-group-fields .acf-field-list .copyable.input-copyable:not(.copy-unsupported).copied .acf-input-wrap:after {\n  -webkit-mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  background-color: #49ad52;\n}\n#acf-field-group-fields .acf-field-list .no-fields-message {\n  padding: 15px 15px;\n  background: #fff;\n  display: none;\n}\n#acf-field-group-fields .acf-field-list.-empty .no-fields-message {\n  display: block;\n}\n.acf-admin-3-8 #acf-field-group-fields .acf-field-list-wrap {\n  border-color: #dfdfdf;\n}\n\n.rtl #acf-field-group-fields .li-field-type .field-type-icon {\n  margin-left: 8px;\n  margin-right: 0;\n}\n\n/* field object */\n.acf-field-object {\n  border-top: #eeeeee solid 1px;\n  background: #fff;\n  /* sortable */\n  /* meta */\n  /* handle */\n  /* open */\n  /*\n  \t// debug\n  \t&[data-save=\"meta\"] {\n  \t\t> .handle {\n  \t\t\tborder-left: #ffb700 solid 5px !important;\n  \t\t}\n  \t}\n\n  \t&[data-save=\"settings\"] {\n  \t\t> .handle {\n  \t\t\tborder-left: #0ec563 solid 5px !important;\n  \t\t}\n  \t}\n  */\n  /* hover */\n  /* settings */\n  /* conditional logic */\n}\n.acf-field-object.ui-sortable-helper {\n  overflow: hidden !important;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #A5D2E7 !important;\n  border-radius: 8px;\n  filter: drop-shadow(0px 10px 20px rgba(16, 24, 40, 0.14)) drop-shadow(0px 1px 3px rgba(16, 24, 40, 0.1));\n}\n.acf-field-object.ui-sortable-helper:before {\n  display: none !important;\n}\n.acf-field-object.ui-sortable-placeholder {\n  box-shadow: 0 -1px 0 0 #DFDFDF;\n  visibility: visible !important;\n  background: #F9F9F9;\n  border-top-color: transparent;\n  min-height: 54px;\n}\n.acf-field-object.ui-sortable-placeholder:after, .acf-field-object.ui-sortable-placeholder:before {\n  visibility: hidden;\n}\n.acf-field-object > .meta {\n  display: none;\n}\n.acf-field-object > .handle a {\n  -webkit-transition: none;\n  -moz-transition: none;\n  -o-transition: none;\n  transition: none;\n}\n.acf-field-object > .handle li {\n  word-wrap: break-word;\n}\n.acf-field-object > .handle strong {\n  display: block;\n  padding-bottom: 0;\n  font-size: 14px;\n  line-height: 14px;\n  min-height: 14px;\n}\n.acf-field-object > .handle .row-options {\n  display: block;\n  opacity: 0;\n  margin-top: 5px;\n}\n@media screen and (max-width: 880px) {\n  .acf-field-object > .handle .row-options {\n    opacity: 1;\n    margin-bottom: 0;\n  }\n}\n.acf-field-object > .handle .row-options a {\n  margin-right: 4px;\n}\n.acf-field-object > .handle .row-options a:hover {\n  color: rgb(4.0632911392, 71.1075949367, 102.9367088608);\n}\n.acf-field-object > .handle .row-options a.delete-field {\n  color: #a00;\n}\n.acf-field-object > .handle .row-options a.delete-field:hover {\n  color: #f00;\n}\n.acf-field-object > .handle .row-options.active {\n  visibility: visible;\n}\n.acf-field-object.open + .acf-field-object {\n  border-top-color: #E1E1E1;\n}\n.acf-field-object.open > .handle {\n  background: #2a9bd9;\n  border: rgb(37.6669322709, 149.6764940239, 211.1330677291) solid 1px;\n  text-shadow: #268FBB 0 1px 0;\n  color: #fff;\n  position: relative;\n  margin: 0 -1px 0 -1px;\n}\n.acf-field-object.open > .handle a {\n  color: #fff !important;\n}\n.acf-field-object.open > .handle a:hover {\n  text-decoration: underline !important;\n}\n.acf-field-object:hover > .handle .row-options, .acf-field-object.-hover > .handle .row-options, .acf-field-object:focus-within > .handle .row-options {\n  opacity: 1;\n  margin-bottom: 0;\n}\n.acf-field-object > .settings {\n  display: none;\n  width: 100%;\n}\n.acf-field-object > .settings > .acf-table {\n  border: none;\n}\n.acf-field-object .rule-groups {\n  margin-top: 20px;\n}\n\n/*----------------------------------------------------------------------------\n*\n* Postbox: Locations\n*\n*----------------------------------------------------------------------------*/\n.rule-groups h4 {\n  margin: 3px 0;\n}\n.rule-groups .rule-group {\n  margin: 0 0 5px;\n}\n.rule-groups .rule-group h4 {\n  margin: 0 0 3px;\n}\n.rule-groups .rule-group td.param {\n  width: 35%;\n}\n.rule-groups .rule-group td.operator {\n  width: 20%;\n}\n.rule-groups .rule-group td.add {\n  width: 40px;\n}\n.rule-groups .rule-group td.remove {\n  width: 28px;\n  vertical-align: middle;\n}\n.rule-groups .rule-group td.remove a {\n  width: 22px;\n  height: 22px;\n  visibility: hidden;\n}\n.rule-groups .rule-group td.remove a:before {\n  position: relative;\n  top: -2px;\n  font-size: 16px;\n}\n.rule-groups .rule-group tr:hover td.remove a {\n  visibility: visible;\n}\n.rule-groups .rule-group select:empty {\n  background: #f8f8f8;\n}\n.rule-groups:not(.rule-groups-multiple) .rule-group:first-child tr:first-child td.remove a {\n  /* Don't allow user to delete the only rule group */\n  visibility: hidden !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tOptions\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-options tr[data-name=hide_on_screen] li {\n  float: left;\n  width: 33%;\n}\n\n@media (max-width: 1100px) {\n  #acf-field-group-options tr[data-name=hide_on_screen] li {\n    width: 50%;\n  }\n}\n/*----------------------------------------------------------------------------\n*\n*\tConditional Logic\n*\n*----------------------------------------------------------------------------*/\ntable.conditional-logic-rules {\n  background: transparent;\n  border: 0 none;\n  border-radius: 0;\n}\n\ntable.conditional-logic-rules tbody td {\n  background: transparent;\n  border: 0 none !important;\n  padding: 5px 2px !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Tab\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-tab .acf-field-setting-name,\n.acf-field-object-tab .acf-field-setting-instructions,\n.acf-field-object-tab .acf-field-setting-required,\n.acf-field-object-tab .acf-field-setting-warning,\n.acf-field-object-tab .acf-field-setting-wrapper {\n  display: none;\n}\n.acf-field-object-tab .li-field-name {\n  visibility: hidden;\n}\n.acf-field-object-tab p:first-child {\n  margin: 0.5em 0;\n}\n.acf-field-object-tab li.acf-settings-type-presentation,\n.acf-field-object-tab .acf-field-settings-main-presentation {\n  display: none !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Accordion\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-accordion .acf-field-setting-name,\n.acf-field-object-accordion .acf-field-setting-instructions,\n.acf-field-object-accordion .acf-field-setting-required,\n.acf-field-object-accordion .acf-field-setting-warning,\n.acf-field-object-accordion .acf-field-setting-wrapper {\n  display: none;\n}\n.acf-field-object-accordion .li-field-name {\n  visibility: hidden;\n}\n.acf-field-object-accordion p:first-child {\n  margin: 0.5em 0;\n}\n.acf-field-object-accordion .acf-field-setting-instructions {\n  display: block;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Message\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-message tr[data-name=name],\n.acf-field-object-message tr[data-name=instructions],\n.acf-field-object-message tr[data-name=required] {\n  display: none !important;\n}\n\n.acf-field-object-message .li-field-name {\n  visibility: hidden;\n}\n\n.acf-field-object-message textarea {\n  height: 175px !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Separator\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-separator tr[data-name=name],\n.acf-field-object-separator tr[data-name=instructions],\n.acf-field-object-separator tr[data-name=required] {\n  display: none !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Date Picker\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-date-picker .acf-radio-list li,\n.acf-field-object-time-picker .acf-radio-list li,\n.acf-field-object-date-time-picker .acf-radio-list li {\n  line-height: 25px;\n}\n.acf-field-object-date-picker .acf-radio-list span,\n.acf-field-object-time-picker .acf-radio-list span,\n.acf-field-object-date-time-picker .acf-radio-list span {\n  display: inline-block;\n  min-width: 10em;\n}\n.acf-field-object-date-picker .acf-radio-list input[type=text],\n.acf-field-object-time-picker .acf-radio-list input[type=text],\n.acf-field-object-date-time-picker .acf-radio-list input[type=text] {\n  width: 100px;\n}\n\n.acf-field-object-date-time-picker .acf-radio-list span {\n  min-width: 15em;\n}\n.acf-field-object-date-time-picker .acf-radio-list input[type=text] {\n  width: 200px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSlug\n*\n*--------------------------------------------------------------------------------------------*/\n#slugdiv .inside {\n  padding: 12px;\n  margin: 0;\n}\n#slugdiv input[type=text] {\n  width: 100%;\n  height: 28px;\n  font-size: 14px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\nhtml[dir=rtl] .acf-field-object.open > .handle {\n  margin: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Device\n*\n*----------------------------------------------------------------------------*/\n@media only screen and (max-width: 850px) {\n  tr.acf-field,\n  td.acf-label,\n  td.acf-input {\n    display: block !important;\n    width: auto !important;\n    border: 0 none !important;\n  }\n  tr.acf-field {\n    border-top: #ededed solid 1px !important;\n    margin-bottom: 0 !important;\n  }\n  td.acf-label {\n    background: transparent !important;\n    padding-bottom: 0 !important;\n  }\n}\n/*----------------------------------------------------------------------------\n*\n*  Subtle background on accordion & tab fields to separate them from others\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-object-tab,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-object-accordion {\n  background-color: #F9FAFB;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page #wpcontent {\n  line-height: 140%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page a {\n  color: #0783BE;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-h1, .acf-admin-page h1,\n.acf-headerbar h1 {\n  font-size: 21px;\n  font-weight: 400;\n}\n\n.acf-h2, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2, .acf-page-title, .acf-admin-page h2,\n.acf-headerbar h2 {\n  font-size: 18px;\n  font-weight: 400;\n}\n\n.acf-h3, .post-type-acf-field-group .acf-field-settings-fc_head label, .acf-admin-page #acf-popup .acf-popup-box .title h1,\n.acf-admin-page #acf-popup .acf-popup-box .title h2,\n.acf-admin-page #acf-popup .acf-popup-box .title h3,\n.acf-admin-page #acf-popup .acf-popup-box .title h4, .acf-admin-page h3,\n.acf-headerbar h3 {\n  font-size: 16px;\n  font-weight: 400;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page .p1 {\n  font-size: 15px;\n}\n.acf-admin-page .p2, .acf-admin-page .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner .acf-admin-page p {\n  font-size: 14px;\n}\n.acf-admin-page .p3 {\n  font-size: 13.5px;\n}\n.acf-admin-page .p4, .acf-admin-page .acf-field-list .acf-sortable-handle, .acf-field-list .acf-admin-page .acf-sortable-handle, .acf-admin-page .post-type-acf-field-group .acf-field-object .handle li.li-field-label a.edit-field, .post-type-acf-field-group .acf-field-object .handle li.li-field-label .acf-admin-page a.edit-field, .acf-admin-page .post-type-acf-field-group .acf-field-object .handle li, .post-type-acf-field-group .acf-field-object .handle .acf-admin-page li, .acf-admin-page .post-type-acf-field-group .acf-thead li, .post-type-acf-field-group .acf-thead .acf-admin-page li, .acf-admin-page .acf-input .select2-container.-acf .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container.-acf .select2-selection__rendered, .acf-admin-page .button, .acf-admin-page input[type=text],\n.acf-admin-page input[type=search],\n.acf-admin-page input[type=number],\n.acf-admin-page textarea,\n.acf-admin-page select {\n  font-size: 13px;\n}\n.acf-admin-page .p5, .acf-admin-page .acf-field-setting-display_format .acf-radio-list li label code, .acf-field-setting-display_format .acf-radio-list li label .acf-admin-page code,\n.acf-admin-page .acf-field-setting-return_format .acf-radio-list li label code,\n.acf-field-setting-return_format .acf-radio-list li label .acf-admin-page code, .acf-admin-page .acf-field-group-settings-footer .acf-created-on, .acf-field-group-settings-footer .acf-admin-page .acf-created-on, .acf-admin-page .acf-fields .acf-field-settings-tab-bar li a, .acf-fields .acf-field-settings-tab-bar li .acf-admin-page a,\n.acf-admin-page .acf-fields .acf-tab-wrap .acf-tab-group li a,\n.acf-fields .acf-tab-wrap .acf-tab-group li .acf-admin-page a,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a,\n.acf-admin-page .acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li .acf-admin-page a,\n.acf-admin-page .acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li .acf-admin-page a {\n  font-size: 12.5px;\n}\n.acf-admin-page .p6, .acf-admin-page .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p.acf-small, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner .acf-admin-page p.acf-small, .acf-admin-page .post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options a, .post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options .acf-admin-page a, .acf-admin-page .acf-small {\n  font-size: 12px;\n}\n.acf-admin-page .p7 {\n  font-size: 11.5px;\n}\n.acf-admin-page .p8 {\n  font-size: 11px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n  color: #344054;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page .acf-settings-wrap h1 {\n  display: none !important;\n}\n.acf-admin-page #acf-admin-tools h1:not(.acf-field-group-pro-features-title, .acf-field-group-pro-features-title-sm) {\n  display: none !important;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page a:focus {\n  box-shadow: none;\n  outline: none;\n}\n.acf-admin-page a:focus-visible {\n  box-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgba(79, 148, 212, 0.8);\n  outline: 1px solid transparent;\n}\n\n.acf-admin-page {\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  All Inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Read only text inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Number fields\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Textarea\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Button & Checkbox base styling\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Checkboxes\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons & Checkbox lists\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF Switch\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  File input button\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Action Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Edit field group header\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select2 inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF label\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Tooltip for field name field setting (result of a fix for keyboard navigation)\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /* Field Type Selection select2 */\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  RTL arrow position\n  *\n  *---------------------------------------------------------------------------------------------*/\n}\n.acf-admin-page input[type=text],\n.acf-admin-page input[type=search],\n.acf-admin-page input[type=number],\n.acf-admin-page textarea,\n.acf-admin-page select {\n  box-sizing: border-box;\n  height: 40px;\n  padding-right: 12px;\n  padding-left: 12px;\n  background-color: #fff;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  /* stylelint-disable-next-line scss/at-extend-no-missing-placeholder */\n  color: #344054;\n}\n.acf-admin-page input[type=text]:focus,\n.acf-admin-page input[type=search]:focus,\n.acf-admin-page input[type=number]:focus,\n.acf-admin-page textarea:focus,\n.acf-admin-page select:focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n}\n.acf-admin-page input[type=text]:disabled,\n.acf-admin-page input[type=search]:disabled,\n.acf-admin-page input[type=number]:disabled,\n.acf-admin-page textarea:disabled,\n.acf-admin-page select:disabled {\n  background-color: #F9FAFB;\n  color: rgb(128.2255319149, 137.7574468085, 157.7744680851);\n}\n.acf-admin-page input[type=text]::placeholder,\n.acf-admin-page input[type=search]::placeholder,\n.acf-admin-page input[type=number]::placeholder,\n.acf-admin-page textarea::placeholder,\n.acf-admin-page select::placeholder {\n  color: #98A2B3;\n}\n.acf-admin-page input[type=text]:read-only {\n  background-color: #F9FAFB;\n  color: #98A2B3;\n}\n.acf-admin-page .acf-field.acf-field-number .acf-label,\n.acf-admin-page .acf-field.acf-field-number .acf-input input[type=number] {\n  max-width: 180px;\n}\n.acf-admin-page textarea {\n  box-sizing: border-box;\n  padding-top: 10px;\n  padding-bottom: 10px;\n  height: 80px;\n  min-height: 56px;\n}\n.acf-admin-page select {\n  min-width: 160px;\n  max-width: 100%;\n  padding-right: 40px;\n  padding-left: 12px;\n  background-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  background-position: right 10px top 50%;\n  background-size: 20px;\n}\n.acf-admin-page select:hover, .acf-admin-page select:focus {\n  color: #0783BE;\n}\n.acf-admin-page select::before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  width: 20px;\n  height: 20px;\n}\n.acf-admin-page.rtl select {\n  padding-right: 12px;\n  padding-left: 40px;\n  background-position: left 10px top 50%;\n}\n.acf-admin-page input[type=radio],\n.acf-admin-page input[type=checkbox] {\n  box-sizing: border-box;\n  width: 16px;\n  height: 16px;\n  padding: 0;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #98A2B3;\n  background: #fff;\n  box-shadow: none;\n}\n.acf-admin-page input[type=radio]:hover,\n.acf-admin-page input[type=checkbox]:hover {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.acf-admin-page input[type=radio]:checked, .acf-admin-page input[type=radio]:focus-visible,\n.acf-admin-page input[type=checkbox]:checked,\n.acf-admin-page input[type=checkbox]:focus-visible {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.acf-admin-page input[type=radio]:checked:before, .acf-admin-page input[type=radio]:focus-visible:before,\n.acf-admin-page input[type=checkbox]:checked:before,\n.acf-admin-page input[type=checkbox]:focus-visible:before {\n  content: \"\";\n  position: relative;\n  top: -1px;\n  left: -1px;\n  width: 16px;\n  height: 16px;\n  margin: 0;\n  padding: 0;\n  background-color: transparent;\n  background-size: cover;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n.acf-admin-page input[type=radio]:active,\n.acf-admin-page input[type=checkbox]:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.acf-admin-page input[type=radio]:disabled,\n.acf-admin-page input[type=checkbox]:disabled {\n  background-color: #F9FAFB;\n  border-color: #D0D5DD;\n}\n.acf-admin-page.rtl input[type=radio]:checked:before, .acf-admin-page.rtl input[type=radio]:focus-visible:before,\n.acf-admin-page.rtl input[type=checkbox]:checked:before,\n.acf-admin-page.rtl input[type=checkbox]:focus-visible:before {\n  left: 1px;\n}\n.acf-admin-page input[type=radio]:checked:before, .acf-admin-page input[type=radio]:focus:before {\n  background-image: url(\"../../images/field-states/radio-active.svg\");\n}\n.acf-admin-page input[type=checkbox]:checked:before, .acf-admin-page input[type=checkbox]:focus:before {\n  background-image: url(\"../../images/field-states/checkbox-active.svg\");\n}\n.acf-admin-page .acf-radio-list li input[type=radio],\n.acf-admin-page .acf-radio-list li input[type=checkbox],\n.acf-admin-page .acf-checkbox-list li input[type=radio],\n.acf-admin-page .acf-checkbox-list li input[type=checkbox] {\n  margin-right: 6px;\n}\n.acf-admin-page .acf-radio-list.acf-bl li,\n.acf-admin-page .acf-checkbox-list.acf-bl li {\n  margin-bottom: 8px;\n}\n.acf-admin-page .acf-radio-list.acf-bl li:last-of-type,\n.acf-admin-page .acf-checkbox-list.acf-bl li:last-of-type {\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-radio-list label,\n.acf-admin-page .acf-checkbox-list label {\n  display: flex;\n  align-items: center;\n  align-content: center;\n}\n.acf-admin-page .acf-switch {\n  width: 42px;\n  height: 24px;\n  border: none;\n  background-color: #D0D5DD;\n  border-radius: 12px;\n}\n.acf-admin-page .acf-switch:hover {\n  background-color: #98A2B3;\n}\n.acf-admin-page .acf-switch:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.acf-admin-page .acf-switch.-on {\n  background-color: #0783BE;\n}\n.acf-admin-page .acf-switch.-on:hover {\n  background-color: #066998;\n}\n.acf-admin-page .acf-switch.-on .acf-switch-slider {\n  left: 20px;\n}\n.acf-admin-page .acf-switch .acf-switch-off,\n.acf-admin-page .acf-switch .acf-switch-on {\n  visibility: hidden;\n}\n.acf-admin-page .acf-switch .acf-switch-slider {\n  width: 20px;\n  height: 20px;\n  border: none;\n  border-radius: 100px;\n  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n}\n.acf-admin-page .acf-field-true-false {\n  display: flex;\n  align-items: flex-start;\n}\n.acf-admin-page .acf-field-true-false .acf-label {\n  order: 2;\n  display: block;\n  align-items: center;\n  max-width: 550px !important;\n  margin-top: 2px;\n  margin-bottom: 0;\n  margin-left: 12px;\n}\n.acf-admin-page .acf-field-true-false .acf-label label {\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-field-true-false .acf-label .acf-tip {\n  margin-left: 12px;\n}\n.acf-admin-page .acf-field-true-false .acf-label .description {\n  display: block;\n  margin-top: 2px;\n  margin-left: 0;\n}\n.acf-admin-page.rtl .acf-field-true-false .acf-label {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.acf-admin-page.rtl .acf-field-true-false .acf-tip {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.acf-admin-page input::file-selector-button {\n  box-sizing: border-box;\n  min-height: 40px;\n  margin-right: 16px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  background-color: transparent;\n  color: #0783BE !important;\n  border-radius: 6px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  text-decoration: none;\n}\n.acf-admin-page input::file-selector-button:hover {\n  border-color: #066998;\n  cursor: pointer;\n  color: #066998 !important;\n}\n.acf-admin-page .button {\n  display: inline-flex;\n  align-items: center;\n  height: 40px;\n  padding-right: 16px;\n  padding-left: 16px;\n  background-color: transparent;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  border-radius: 6px;\n  color: #0783BE;\n}\n.acf-admin-page .button:hover {\n  background-color: rgb(243.16, 249.08, 252.04);\n  border-color: #0783BE;\n  color: #0783BE;\n}\n.acf-admin-page .button:focus {\n  background-color: rgb(243.16, 249.08, 252.04);\n  outline: 3px solid #EBF5FA;\n  color: #0783BE;\n}\n.acf-admin-page .edit-field-group-header {\n  display: block !important;\n}\n.acf-admin-page .acf-input .select2-container.-acf .select2-selection,\n.acf-admin-page .rule-groups .select2-container.-acf .select2-selection {\n  border: none;\n  line-height: 1;\n}\n.acf-admin-page .acf-input .select2-container.-acf .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container.-acf .select2-selection__rendered {\n  box-sizing: border-box;\n  padding-right: 0;\n  padding-left: 0;\n  background-color: #fff;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  /* stylelint-disable-next-line scss/at-extend-no-missing-placeholder */\n  color: #344054;\n}\n.acf-admin-page .acf-input .acf-conditional-select-name,\n.acf-admin-page .rule-groups .acf-conditional-select-name {\n  min-width: 180px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.acf-admin-page .acf-input .acf-conditional-select-id,\n.acf-admin-page .rule-groups .acf-conditional-select-id {\n  padding-right: 30px;\n}\n.acf-admin-page .acf-input .value .select2-container--focus,\n.acf-admin-page .rule-groups .value .select2-container--focus {\n  height: 40px;\n}\n.acf-admin-page .acf-input .value .select2-container--open .select2-selection__rendered,\n.acf-admin-page .rule-groups .value .select2-container--open .select2-selection__rendered {\n  border-color: #399CCB;\n}\n.acf-admin-page .acf-input .select2-container--focus,\n.acf-admin-page .rule-groups .select2-container--focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n  border-radius: 6px;\n}\n.acf-admin-page .acf-input .select2-container--focus .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--focus .select2-selection__rendered {\n  border-color: #399CCB !important;\n}\n.acf-admin-page .acf-input .select2-container--focus.select2-container--below.select2-container--open .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--focus.select2-container--below.select2-container--open .select2-selection__rendered {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n.acf-admin-page .acf-input .select2-container--focus.select2-container--above.select2-container--open .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--focus.select2-container--above.select2-container--open .select2-selection__rendered {\n  border-top-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n}\n.acf-admin-page .acf-input .select2-container .select2-search--inline .select2-search__field,\n.acf-admin-page .rule-groups .select2-container .select2-search--inline .select2-search__field {\n  margin: 0;\n  padding-left: 6px;\n}\n.acf-admin-page .acf-input .select2-container .select2-search--inline .select2-search__field:focus,\n.acf-admin-page .rule-groups .select2-container .select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: none;\n}\n.acf-admin-page .acf-input .select2-container--default .select2-selection--multiple .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding-top: 0;\n  padding-right: 6px;\n  padding-bottom: 0;\n  padding-left: 6px;\n}\n.acf-admin-page .acf-input .select2-selection__clear,\n.acf-admin-page .rule-groups .select2-selection__clear {\n  width: 18px;\n  height: 18px;\n  margin-top: 12px;\n  margin-right: 1px;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  color: #fff;\n}\n.acf-admin-page .acf-input .select2-selection__clear:before,\n.acf-admin-page .rule-groups .select2-selection__clear:before {\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  top: 0;\n  left: 0;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n  mask-image: url(\"../../images/icons/icon-close.svg\");\n  background-color: #98A2B3;\n}\n.acf-admin-page .acf-input .select2-selection__clear:hover::before,\n.acf-admin-page .rule-groups .select2-selection__clear:hover::before {\n  background-color: #0783BE;\n}\n.acf-admin-page .acf-label {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.acf-admin-page .acf-label .acf-icon-help {\n  width: 18px;\n  height: 18px;\n  background-color: #98A2B3;\n}\n.acf-admin-page .acf-label label {\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-label .description {\n  margin-top: 2px;\n}\n.acf-admin-page .acf-field-setting-name .acf-tip {\n  position: absolute;\n  top: 0;\n  left: 654px;\n  color: #98A2B3;\n}\n.rtl.acf-admin-page .acf-field-setting-name .acf-tip {\n  left: auto;\n  right: 654px;\n}\n\n.acf-admin-page .acf-field-setting-name .acf-tip .acf-icon-help {\n  width: 18px;\n  height: 18px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container.-acf,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container.-acf,\n.acf-admin-page .acf-field-query-var .select2-container.-acf,\n.acf-admin-page .acf-field-capability .select2-container.-acf,\n.acf-admin-page .acf-field-parent-slug .select2-container.-acf,\n.acf-admin-page .acf-field-data-storage .select2-container.-acf,\n.acf-admin-page .acf-field-manage-terms .select2-container.-acf,\n.acf-admin-page .acf-field-edit-terms .select2-container.-acf,\n.acf-admin-page .acf-field-delete-terms .select2-container.-acf,\n.acf-admin-page .acf-field-assign-terms .select2-container.-acf,\n.acf-admin-page .acf-field-meta-box .select2-container.-acf,\n.acf-admin-page .rule-groups .select2-container.-acf {\n  min-height: 40px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--default .select2-selection--single .select2-selection__rendered {\n  display: flex;\n  align-items: center;\n  position: relative;\n  z-index: 800;\n  min-height: 40px;\n  padding-top: 0;\n  padding-right: 12px;\n  padding-bottom: 0;\n  padding-left: 12px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .field-type-icon,\n.acf-admin-page .rule-groups .select2-container--default .select2-selection--single .field-type-icon {\n  top: auto;\n  width: 18px;\n  height: 18px;\n  margin-right: 2px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-query-var .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-capability .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-parent-slug .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-data-storage .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-manage-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-edit-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-delete-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-assign-terms .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .acf-field-meta-box .select2-container--default .select2-selection--single .field-type-icon:before,\n.acf-admin-page .rule-groups .select2-container--default .select2-selection--single .field-type-icon:before {\n  width: 9px;\n  height: 9px;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--open .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--open .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--open .select2-selection__rendered {\n  border-color: #6BB5D8 !important;\n  border-bottom-color: #D0D5DD !important;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--open.select2-container--below .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--open.select2-container--below .select2-selection__rendered {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-query-var .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-capability .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-parent-slug .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-data-storage .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-manage-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-edit-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-delete-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-assign-terms .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .acf-field-meta-box .select2-container--open.select2-container--above .select2-selection__rendered,\n.acf-admin-page .rule-groups .select2-container--open.select2-container--above .select2-selection__rendered {\n  border-top-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n  border-bottom-color: #6BB5D8 !important;\n  border-top-color: #D0D5DD !important;\n}\n.acf-admin-page .acf-field-setting-type .acf-selection.has-icon,\n.acf-admin-page .acf-field-permalink-rewrite .acf-selection.has-icon,\n.acf-admin-page .acf-field-query-var .acf-selection.has-icon,\n.acf-admin-page .acf-field-capability .acf-selection.has-icon,\n.acf-admin-page .acf-field-parent-slug .acf-selection.has-icon,\n.acf-admin-page .acf-field-data-storage .acf-selection.has-icon,\n.acf-admin-page .acf-field-manage-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-edit-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-delete-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-assign-terms .acf-selection.has-icon,\n.acf-admin-page .acf-field-meta-box .acf-selection.has-icon,\n.acf-admin-page .rule-groups .acf-selection.has-icon {\n  margin-left: 6px;\n}\n.rtl.acf-admin-page .acf-field-setting-type .acf-selection.has-icon, .acf-admin-page .acf-field-permalink-rewrite .acf-selection.has-icon, .acf-admin-page .acf-field-query-var .acf-selection.has-icon, .acf-admin-page .acf-field-capability .acf-selection.has-icon, .acf-admin-page .acf-field-parent-slug .acf-selection.has-icon, .acf-admin-page .acf-field-data-storage .acf-selection.has-icon, .acf-admin-page .acf-field-manage-terms .acf-selection.has-icon, .acf-admin-page .acf-field-edit-terms .acf-selection.has-icon, .acf-admin-page .acf-field-delete-terms .acf-selection.has-icon, .acf-admin-page .acf-field-assign-terms .acf-selection.has-icon, .acf-admin-page .acf-field-meta-box .acf-selection.has-icon, .acf-admin-page .rule-groups .acf-selection.has-icon {\n  margin-right: 6px;\n}\n\n.acf-admin-page .acf-field-setting-type .select2-selection__arrow,\n.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow,\n.acf-admin-page .acf-field-query-var .select2-selection__arrow,\n.acf-admin-page .acf-field-capability .select2-selection__arrow,\n.acf-admin-page .acf-field-parent-slug .select2-selection__arrow,\n.acf-admin-page .acf-field-data-storage .select2-selection__arrow,\n.acf-admin-page .acf-field-manage-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-edit-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-delete-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-assign-terms .select2-selection__arrow,\n.acf-admin-page .acf-field-meta-box .select2-selection__arrow,\n.acf-admin-page .rule-groups .select2-selection__arrow {\n  width: 20px;\n  height: 20px;\n  top: calc(50% - 10px);\n  right: 12px;\n  background-color: transparent;\n}\n.acf-admin-page .acf-field-setting-type .select2-selection__arrow:after,\n.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow:after,\n.acf-admin-page .acf-field-query-var .select2-selection__arrow:after,\n.acf-admin-page .acf-field-capability .select2-selection__arrow:after,\n.acf-admin-page .acf-field-parent-slug .select2-selection__arrow:after,\n.acf-admin-page .acf-field-data-storage .select2-selection__arrow:after,\n.acf-admin-page .acf-field-manage-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-edit-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-delete-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-assign-terms .select2-selection__arrow:after,\n.acf-admin-page .acf-field-meta-box .select2-selection__arrow:after,\n.acf-admin-page .rule-groups .select2-selection__arrow:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  z-index: 850;\n  top: 1px;\n  left: 0;\n  width: 20px;\n  height: 20px;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.acf-admin-page .acf-field-setting-type .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-permalink-rewrite .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-query-var .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-capability .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-parent-slug .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-data-storage .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-manage-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-edit-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-delete-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-assign-terms .select2-selection__arrow b[role=presentation],\n.acf-admin-page .acf-field-meta-box .select2-selection__arrow b[role=presentation],\n.acf-admin-page .rule-groups .select2-selection__arrow b[role=presentation] {\n  display: none;\n}\n.acf-admin-page .acf-field-setting-type .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-permalink-rewrite .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-query-var .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-capability .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-parent-slug .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-data-storage .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-manage-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-edit-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-delete-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-assign-terms .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .acf-field-meta-box .select2-container--open .select2-selection__arrow:after,\n.acf-admin-page .rule-groups .select2-container--open .select2-selection__arrow:after {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n.acf-admin-page .acf-term-search-term-name {\n  background-color: #F9FAFB;\n  border-top: 1px solid #EAECF0;\n  border-bottom: 1px solid #EAECF0;\n  color: #98A2B3;\n  padding: 5px 5px 5px 10px;\n  width: 100%;\n  margin: 0;\n  display: block;\n  font-weight: 300;\n}\n.acf-admin-page .field-type-select-results {\n  position: relative;\n  top: 4px;\n  z-index: 1002;\n  border-radius: 0 0 6px 6px;\n  box-shadow: 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n}\n.acf-admin-page .field-type-select-results.select2-dropdown--above {\n  display: flex;\n  flex-direction: column-reverse;\n  top: 0;\n  border-radius: 6px 6px 0 0;\n  z-index: 99999;\n}\n.select2-container.select2-container--open.acf-admin-page .field-type-select-results {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n}\n\n.acf-admin-page .field-type-select-results .acf-selection.has-icon {\n  margin-left: 6px;\n}\n.rtl.acf-admin-page .field-type-select-results .acf-selection.has-icon {\n  margin-right: 6px;\n}\n\n.acf-admin-page .field-type-select-results .select2-search {\n  position: relative;\n  margin: 0;\n  padding: 0;\n}\n.acf-admin-page .field-type-select-results .select2-search--dropdown:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 12px;\n  left: 13px;\n  width: 16px;\n  height: 16px;\n  -webkit-mask-image: url(\"../../images/icons/icon-search.svg\");\n  mask-image: url(\"../../images/icons/icon-search.svg\");\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.rtl.acf-admin-page .field-type-select-results .select2-search--dropdown:after {\n  right: 12px;\n  left: auto;\n}\n\n.acf-admin-page .field-type-select-results .select2-search .select2-search__field {\n  padding-left: 38px;\n  border-right: 0;\n  border-bottom: 0;\n  border-left: 0;\n  border-radius: 0;\n}\n.rtl.acf-admin-page .field-type-select-results .select2-search .select2-search__field {\n  padding-right: 38px;\n  padding-left: 0;\n}\n\n.acf-admin-page .field-type-select-results .select2-search .select2-search__field:focus {\n  border-top-color: #D0D5DD;\n  outline: 0;\n}\n.acf-admin-page .field-type-select-results .select2-results__options {\n  max-height: 440px;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option--highlighted {\n  background-color: #0783BE !important;\n  color: #F9FAFB !important;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option {\n  display: inline-flex;\n  position: relative;\n  width: calc(100% - 24px);\n  min-height: 32px;\n  padding-top: 0;\n  padding-right: 12px;\n  padding-bottom: 0;\n  padding-left: 12px;\n  align-items: center;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option .field-type-icon {\n  top: auto;\n  width: 18px;\n  height: 18px;\n  margin-right: 2px;\n  box-shadow: 0 0 0 1px #F9FAFB;\n}\n.acf-admin-page .field-type-select-results .select2-results__option .select2-results__option .field-type-icon:before {\n  width: 9px;\n  height: 9px;\n}\n.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true] {\n  background-color: #EBF5FA !important;\n  color: #344054 !important;\n}\n.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true]:after {\n  content: \"\";\n  right: 13px;\n  position: absolute;\n  width: 16px;\n  height: 16px;\n  -webkit-mask-image: url(\"../../images/icons/icon-check.svg\");\n  mask-image: url(\"../../images/icons/icon-check.svg\");\n  background-color: #0783BE;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.rtl.acf-admin-page .field-type-select-results .select2-results__option[aria-selected=true]:after {\n  left: 13px;\n  right: auto;\n}\n\n.acf-admin-page .field-type-select-results .select2-results__group {\n  display: inline-flex;\n  align-items: center;\n  width: calc(100% - 24px);\n  min-height: 25px;\n  background-color: #F9FAFB;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n  color: #98A2B3;\n  font-size: 11px;\n  margin-bottom: 0;\n  padding-top: 0;\n  padding-right: 12px;\n  padding-bottom: 0;\n  padding-left: 12px;\n  font-weight: normal;\n}\n.acf-admin-page.rtl .acf-field-setting-type .select2-selection__arrow:after,\n.acf-admin-page.rtl .acf-field-permalink-rewrite .select2-selection__arrow:after,\n.acf-admin-page.rtl .acf-field-query-var .select2-selection__arrow:after {\n  right: auto;\n  left: 10px;\n}\n\n.rtl.post-type-acf-field-group .acf-field-setting-name .acf-tip,\n.rtl.acf-internal-post-type .acf-field-setting-name .acf-tip {\n  left: auto;\n  right: 654px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Container sizes\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .metabox-holder.columns-1 #acf-field-group-fields,\n.post-type-acf-field-group .metabox-holder.columns-1 #acf-field-group-options,\n.post-type-acf-field-group .metabox-holder.columns-1 .meta-box-sortables.ui-sortable,\n.post-type-acf-field-group .metabox-holder.columns-1 .notice {\n  max-width: 1440px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Max width for notices in 1 column edit field group layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-1 .notice {\n  max-width: 1440px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Widen edit field group headerbar for 2 column layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-2 .acf-headerbar .acf-headerbar-inner {\n  max-width: 100%;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Post stuff\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #poststuff {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap {\n  overflow: hidden;\n  border: none;\n  border-radius: 0 0 8px 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty {\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .acf-thead,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .acf-tfoot {\n  display: none;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .no-fields-message {\n  min-height: 280px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table header\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-thead {\n  background-color: #F9FAFB;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.post-type-acf-field-group .acf-thead li {\n  display: flex;\n  align-items: center;\n  min-height: 48px;\n  padding-top: 0;\n  padding-bottom: 0;\n  color: #344054;\n  font-weight: 500;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table body\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object {\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.post-type-acf-field-group .acf-field-object:hover .acf-sortable-handle:before {\n  display: inline-flex;\n}\n.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint:before {\n  display: block;\n  content: \"\";\n  height: 2px;\n  width: 100%;\n  background: #D0D5DD;\n  margin-top: -1px;\n}\n.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint.acf-field-object-accordion:before {\n  display: none;\n}\n.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint.acf-field-object-accordion:after {\n  display: block;\n  content: \"\";\n  height: 2px;\n  width: 100%;\n  background: #D0D5DD;\n  z-index: 500;\n}\n.post-type-acf-field-group .acf-field-object:hover {\n  background-color: rgb(247.24, 251.12, 253.06);\n}\n.post-type-acf-field-group .acf-field-object.open {\n  background-color: #fff;\n  border-top-color: #A5D2E7;\n}\n.post-type-acf-field-group .acf-field-object.open .handle {\n  background-color: #D8EBF5;\n  border: none;\n  text-shadow: none;\n}\n.post-type-acf-field-group .acf-field-object.open .handle a {\n  color: #0783BE !important;\n}\n.post-type-acf-field-group .acf-field-object.open .handle a.delete-field {\n  color: #a00 !important;\n}\n.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl {\n  margin: 0;\n}\n.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl li {\n  width: auto;\n}\n.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl li:first-child {\n  flex-grow: 1;\n  margin-left: -10px;\n}\n.post-type-acf-field-group .acf-field-object .acf-field-setting-type .acf-hl li:nth-child(2) {\n  padding-right: 0;\n}\n.post-type-acf-field-group .acf-field-object ul.acf-hl {\n  display: flex;\n  align-items: stretch;\n}\n.post-type-acf-field-group .acf-field-object .handle li {\n  display: flex;\n  align-items: top;\n  flex-wrap: wrap;\n  min-height: 60px;\n  color: #344054;\n}\n.post-type-acf-field-group .acf-field-object .handle li.li-field-label {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n  width: auto;\n}\n.post-type-acf-field-group .acf-field-object .handle li.li-field-label strong {\n  font-weight: 500;\n}\n.post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options {\n  width: 100%;\n}\n/*----------------------------------------------------------------------------\n*\n*  Table footer\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-tfoot {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  min-height: 80px;\n  box-sizing: border-box;\n  padding-top: 8px;\n  padding-right: 24px;\n  padding-bottom: 8px;\n  padding-left: 24px;\n  background-color: #fff;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.post-type-acf-field-group .acf-tfoot .acf-fr {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit field settings\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object .settings {\n  box-sizing: border-box;\n  padding-top: 0;\n  padding-bottom: 0;\n  background-color: #fff;\n  border-left-width: 4px;\n  border-left-style: solid;\n  border-left-color: #6BB5D8;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Main field settings container\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main {\n  padding-top: 32px;\n  padding-right: 0;\n  padding-bottom: 32px;\n  padding-left: 0;\n}\n.acf-field-settings-main .acf-field:last-of-type,\n.acf-field-settings-main .acf-field.acf-last-visible {\n  margin-bottom: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field label\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-label {\n  display: block;\n  justify-content: space-between;\n  align-items: center;\n  align-content: center;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 6px;\n  margin-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Single field\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field {\n  box-sizing: border-box;\n  width: 100%;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 32px;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 72px;\n  padding-bottom: 0;\n  padding-left: 72px;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings .acf-field {\n    padding-right: 12px;\n    padding-left: 12px;\n  }\n}\n.acf-field-settings .acf-field .acf-label,\n.acf-field-settings .acf-field .acf-input {\n  max-width: 600px;\n}\n.acf-field-settings .acf-field .acf-label.acf-input-sub,\n.acf-field-settings .acf-field .acf-input.acf-input-sub {\n  max-width: 100%;\n}\n.acf-field-settings .acf-field .acf-label .acf-btn:disabled,\n.acf-field-settings .acf-field .acf-input .acf-btn:disabled {\n  background-color: #F2F4F7;\n  color: #98A2B3 !important;\n  border: 1px #D0D5DD solid;\n  cursor: default;\n}\n.acf-field-settings .acf-field .acf-input-wrap {\n  overflow: visible;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field separators\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field.acf-field-setting-label,\n.acf-field-settings .acf-field-setting-wrapper {\n  padding-top: 24px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n\n.acf-field-settings .acf-field-setting-wrapper {\n  margin-top: 24px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Informational Notes for specific fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-setting-bidirectional_notes .acf-label {\n  display: none;\n}\n.acf-field-setting-bidirectional_notes .acf-feature-notice {\n  background-color: #F9FAFB;\n  border: 1px solid #EAECF0;\n  border-radius: 6px;\n  padding: 16px;\n  color: #344054;\n  position: relative;\n}\n.acf-field-setting-bidirectional_notes .acf-feature-notice.with-warning-icon {\n  padding-left: 45px;\n}\n.acf-field-setting-bidirectional_notes .acf-feature-notice.with-warning-icon::before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 17px;\n  left: 18px;\n  z-index: 600;\n  width: 18px;\n  height: 18px;\n  margin-right: 8px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-info.svg\");\n  mask-image: url(\"../../images/icons/icon-info.svg\");\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit fields footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-settings-footer {\n  display: flex;\n  align-items: center;\n  min-height: 72px;\n  box-sizing: border-box;\n  width: 100%;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 72px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings .acf-field-settings-footer {\n    padding-left: 12px;\n  }\n}\n\n.rtl .acf-field-settings .acf-field-settings-footer {\n  padding-top: 0;\n  padding-right: 72px;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Tabs\n*\n*----------------------------------------------------------------------------*/\n.acf-fields .acf-tab-wrap,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap,\n.acf-browse-fields-modal-wrap .acf-tab-wrap {\n  background: #F9FAFB;\n  border-bottom-color: #1D2939;\n}\n.acf-fields .acf-tab-wrap .acf-tab-group,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group {\n  padding-right: 24px;\n  padding-left: 24px;\n  border-top-width: 0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-fields .acf-field-settings-tab-bar,\n.acf-fields .acf-tab-wrap .acf-tab-group,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group {\n  display: flex;\n  align-items: stretch;\n  min-height: 48px;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 24px;\n  margin-top: 0;\n  margin-bottom: 0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-fields .acf-field-settings-tab-bar li,\n.acf-fields .acf-tab-wrap .acf-tab-group li,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li {\n  display: flex;\n  margin-top: 0;\n  margin-right: 24px;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding: 0;\n}\n.acf-fields .acf-field-settings-tab-bar li a,\n.acf-fields .acf-tab-wrap .acf-tab-group li a,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a {\n  box-sizing: border-box;\n  display: inline-flex;\n  align-items: center;\n  height: 100%;\n  padding-top: 3px;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  background: none;\n  border-top: none;\n  border-right: none;\n  border-bottom-width: 3px;\n  border-bottom-style: solid;\n  border-bottom-color: transparent;\n  border-left: none;\n  color: #667085;\n  font-weight: normal;\n}\n.acf-fields .acf-field-settings-tab-bar li a:focus-visible,\n.acf-fields .acf-tab-wrap .acf-tab-group li a:focus-visible,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a:focus-visible,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a:focus-visible,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a:focus-visible,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a:focus-visible {\n  border: 1px solid #5897fb;\n}\n.acf-fields .acf-field-settings-tab-bar li a:hover,\n.acf-fields .acf-tab-wrap .acf-tab-group li a:hover,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a:hover,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a:hover,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a:hover,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a:hover {\n  color: #1D2939;\n}\n.acf-fields .acf-field-settings-tab-bar li a:hover,\n.acf-fields .acf-tab-wrap .acf-tab-group li a:hover,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li a:hover,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li a:hover,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li a:hover,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li a:hover {\n  background-color: transparent;\n}\n.acf-fields .acf-field-settings-tab-bar li.active a,\n.acf-fields .acf-tab-wrap .acf-tab-group li.active a,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li.active a,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li.active a,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li.active a,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li.active a {\n  background: none;\n  border-bottom-color: #0783BE;\n  color: #0783BE;\n}\n.acf-fields .acf-field-settings-tab-bar li.active a:focus-visible,\n.acf-fields .acf-tab-wrap .acf-tab-group li.active a:focus-visible,\n.acf-admin-page.acf-internal-post-type .acf-field-settings-tab-bar li.active a:focus-visible,\n.acf-admin-page.acf-internal-post-type .acf-tab-wrap .acf-tab-group li.active a:focus-visible,\n.acf-browse-fields-modal-wrap .acf-field-settings-tab-bar li.active a:focus-visible,\n.acf-browse-fields-modal-wrap .acf-tab-wrap .acf-tab-group li.active a:focus-visible {\n  border-bottom-color: #0783BE;\n  border-bottom-width: 3px;\n}\n\n.acf-admin-page.acf-internal-post-type .acf-field-editor .acf-field-settings-tab-bar {\n  padding-left: 72px;\n}\n@media screen and (max-width: 600px) {\n  .acf-admin-page.acf-internal-post-type .acf-field-editor .acf-field-settings-tab-bar {\n    padding-left: 12px;\n  }\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field group settings\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-options .field-group-settings-tab {\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n#acf-field-group-options .field-group-settings-tab .acf-field:last-of-type {\n  padding: 0;\n}\n#acf-field-group-options .acf-field {\n  border: none;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 24px;\n  padding-left: 0;\n}\n#acf-field-group-options .field-group-setting-split-container {\n  display: flex;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n#acf-field-group-options .field-group-setting-split-container .field-group-setting-split {\n  box-sizing: border-box;\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n#acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(1) {\n  flex: 1 0 auto;\n}\n#acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(2n) {\n  flex: 1 0 auto;\n  max-width: 320px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 32px;\n  padding-right: 32px;\n  padding-left: 32px;\n  border-left-width: 1px;\n  border-left-style: solid;\n  border-left-color: #EAECF0;\n}\n#acf-field-group-options .acf-field[data-name=description] {\n  max-width: 600px;\n}\n#acf-field-group-options .acf-button-group {\n  display: inline-flex;\n}\n\n.rtl #acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(2n) {\n  margin-right: 32px;\n  margin-left: 0;\n  border-left: none;\n  border-right-width: 1px;\n  border-right-style: solid;\n  border-right-color: #EAECF0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Reorder handles\n*\n*----------------------------------------------------------------------------*/\n.acf-field-list .li-field-order {\n  padding: 0;\n  display: flex;\n  flex-direction: row;\n  flex-wrap: nowrap;\n  justify-content: center;\n  align-content: stretch;\n  align-items: stretch;\n  background-color: transparent;\n}\n.acf-field-list .acf-sortable-handle {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: nowrap;\n  justify-content: center;\n  align-content: flex-start;\n  align-items: flex-start;\n  width: 100%;\n  height: 100%;\n  position: relative;\n  padding-top: 11px;\n  padding-bottom: 8px;\n  background-color: transparent;\n  border: none;\n  border-radius: 0;\n}\n.acf-field-list .acf-sortable-handle:hover {\n  cursor: grab;\n}\n.acf-field-list .acf-sortable-handle:before {\n  content: \"\";\n  display: none;\n  position: absolute;\n  top: 16px;\n  left: 8px;\n  width: 16px;\n  height: 16px;\n  width: 12px;\n  height: 12px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-draggable.svg\");\n  mask-image: url(\"../../images/icons/icon-draggable.svg\");\n}\n\n.rtl .acf-field-list .acf-sortable-handle:before {\n  left: 0;\n  right: 8px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Expand / collapse field icon\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object .li-field-label {\n  position: relative;\n  padding-left: 40px;\n}\n.acf-field-object .li-field-label:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  left: 6px;\n  display: inline-flex;\n  width: 18px;\n  height: 18px;\n  margin-top: -2px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n.acf-field-object .li-field-label:hover:before {\n  cursor: pointer;\n}\n\n.rtl .acf-field-object .li-field-label {\n  padding-left: 0;\n  padding-right: 40px;\n}\n.rtl .acf-field-object .li-field-label:before {\n  left: 0;\n  right: 6px;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n.rtl .acf-field-object.open .li-field-label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n.rtl .acf-field-object.open .acf-input-sub .li-field-label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n}\n.rtl .acf-field-object.open .acf-input-sub .acf-field-object.open .li-field-label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n\n.acf-thead .li-field-label {\n  padding-left: 40px;\n}\n.rtl .acf-thead .li-field-label {\n  padding-left: 0;\n  padding-right: 40px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic layout\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main-conditional-logic .acf-conditional-toggle {\n  display: flex;\n  padding-right: 72px;\n  padding-left: 72px;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings-main-conditional-logic .acf-conditional-toggle {\n    padding-left: 12px;\n  }\n}\n.acf-field-settings-main-conditional-logic .acf-field {\n  flex-wrap: wrap;\n  margin-bottom: 0;\n  padding-right: 0;\n  padding-left: 0;\n}\n.acf-field-settings-main-conditional-logic .acf-field .rule-groups {\n  flex: 0 1 100%;\n  order: 3;\n  margin-top: 32px;\n  padding-top: 32px;\n  padding-right: 72px;\n  padding-left: 72px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings-main-conditional-logic .acf-field .rule-groups {\n    padding-left: 12px;\n  }\n  .acf-field-settings-main-conditional-logic .acf-field .rule-groups table.acf-table tbody tr {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n  }\n  .acf-field-settings-main-conditional-logic .acf-field .rule-groups table.acf-table tbody tr td {\n    flex: 1 1 100%;\n  }\n}\n\n.acf-taxonomy-select-id,\n.acf-relationship-select-id,\n.acf-post_object-select-id,\n.acf-page_link-select-id,\n.acf-user-select-id {\n  color: #98A2B3;\n  padding-left: 10px;\n}\n\n.acf-taxonomy-select-sub-item {\n  max-width: 180px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-left: 5px;\n}\n\n.acf-taxonomy-select-name {\n  max-width: 180px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Prefix & append styling\n*\n*----------------------------------------------------------------------------*/\n.acf-input .acf-input-prepend,\n.acf-input .acf-input-append {\n  display: inline-flex;\n  align-items: center;\n  height: 100%;\n  min-height: 40px;\n  padding-right: 12px;\n  padding-left: 12px;\n  background-color: #F9FAFB;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  color: #667085;\n}\n.acf-input .acf-input-prepend {\n  border-radius: 6px 0 0 6px;\n}\n.acf-input .acf-input-append {\n  border-radius: 0 6px 6px 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  ACF input wrap\n*\n*----------------------------------------------------------------------------*/\n.acf-input-wrap {\n  display: flex;\n}\n\n.acf-field-settings-main-presentation .acf-input-wrap {\n  display: flex;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Empty state\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message {\n  display: flex;\n  justify-content: center;\n  padding-top: 48px;\n  padding-bottom: 48px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-content: center;\n  align-items: flex-start;\n  text-align: center;\n  max-width: 400px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner img,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p {\n  flex: 1 0 100%;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2 {\n  margin-top: 32px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #344054;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p {\n  margin-top: 12px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #667085;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p.acf-small {\n  margin-top: 32px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner img {\n  max-width: 284px;\n  margin-bottom: 0;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner .acf-btn {\n  margin-top: 32px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide add title prompt label\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-headerbar #title-prompt-text {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Modal styling\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-page #acf-popup .acf-popup-box {\n  min-width: 480px;\n}\n.acf-admin-page #acf-popup .acf-popup-box .title {\n  display: flex;\n  align-items: center;\n  align-content: center;\n  justify-content: space-between;\n  min-height: 64px;\n  box-sizing: border-box;\n  margin: 0;\n  padding-right: 24px;\n  padding-left: 24px;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-admin-page #acf-popup .acf-popup-box .title h1,\n.acf-admin-page #acf-popup .acf-popup-box .title h2,\n.acf-admin-page #acf-popup .acf-popup-box .title h3,\n.acf-admin-page #acf-popup .acf-popup-box .title h4 {\n  padding-left: 0;\n  color: #344054;\n}\n.acf-admin-page #acf-popup .acf-popup-box .title .acf-icon {\n  display: block;\n  position: relative;\n  top: auto;\n  right: auto;\n  width: 22px;\n  height: 22px;\n  background-color: transparent;\n  color: transparent;\n}\n.acf-admin-page #acf-popup .acf-popup-box .title .acf-icon:before {\n  display: inline-flex;\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 22px;\n  height: 22px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-close-circle.svg\");\n  mask-image: url(\"../../images/icons/icon-close-circle.svg\");\n}\n.acf-admin-page #acf-popup .acf-popup-box .title .acf-icon:hover:before {\n  background-color: #0783BE;\n}\n.acf-admin-page #acf-popup .acf-popup-box .inner {\n  box-sizing: border-box;\n  margin: 0;\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n  border-top: none;\n}\n.acf-admin-page #acf-popup .acf-popup-box .inner p {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.acf-admin-page #acf-popup .acf-popup-box #acf-move-field-form .acf-field-select,\n.acf-admin-page #acf-popup .acf-popup-box #acf-link-field-groups-form .acf-field-select {\n  margin-top: 0;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .title h3,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .title h3 {\n  color: #1D2939;\n  font-weight: 500;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .title h3:before,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .title h3:before {\n  content: \"\";\n  width: 18px;\n  height: 18px;\n  background: #98A2B3;\n  margin-right: 9px;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner {\n  padding: 0 !important;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-field-select,\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-link-successful,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field-select,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-link-successful {\n  padding: 32px 24px;\n  margin-bottom: 0;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-field-select .description,\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-link-successful .description,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field-select .description,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-link-successful .description {\n  margin-top: 6px !important;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-actions,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions {\n  background: #F9FAFB;\n  border-top: 1px solid #EAECF0;\n  padding-top: 20px;\n  padding-left: 24px;\n  padding-bottom: 20px;\n  padding-right: 24px;\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-actions .acf-btn,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions .acf-btn {\n  display: inline-block;\n  margin-left: 8px;\n}\n.acf-admin-page .acf-link-field-groups-popup .acf-popup-box .inner .acf-actions .acf-btn.acf-btn-primary,\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions .acf-btn.acf-btn-primary {\n  width: 120px;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-error-message.-success {\n  display: none;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .-dismiss {\n  margin: 24px 32px !important;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field {\n  padding: 24px 32px 0 32px;\n  margin: 0;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field.acf-error .acf-input-wrap {\n  overflow: inherit;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field.acf-error .acf-input-wrap input[type=text] {\n  border: 1px rgba(209, 55, 55, 0.5) solid !important;\n  box-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.12), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n  background-image: url(../../images/icons/icon-info-red.svg);\n  background-position: right 10px top 50%;\n  background-size: 14px;\n  background-repeat: no-repeat;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-field .acf-options-page-modal-error p {\n  font-size: 12px;\n  color: #D13737;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions {\n  margin-top: 32px;\n}\n.acf-admin-page .acf-create-options-page-popup .acf-popup-box .inner .acf-actions .acf-btn:disabled {\n  background-color: #0783BE;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide original #post-body-content from edit field group page\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group #post-body-content {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Settings section footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-group-settings-footer {\n  display: flex;\n  justify-content: space-between;\n  align-content: stretch;\n  align-items: center;\n  position: relative;\n  min-height: 88px;\n  margin-right: -24px;\n  margin-left: -24px;\n  margin-bottom: -24px;\n  padding-right: 24px;\n  padding-left: 24px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.acf-field-group-settings-footer .acf-created-on {\n  display: inline-flex;\n  justify-content: flex-start;\n  align-content: stretch;\n  align-items: center;\n  color: #667085;\n}\n.acf-field-group-settings-footer .acf-created-on:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-time.svg\");\n  mask-image: url(\"../../images/icons/icon-time.svg\");\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic enabled badge\n*\n*----------------------------------------------------------------------------*/\n.conditional-logic-badge {\n  display: none;\n}\n.conditional-logic-badge.is-enabled {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  overflow: hidden;\n  margin-left: 8px;\n  background-color: rgba(82, 170, 89, 0.4);\n  border-width: 1px;\n  border-style: solid;\n  border-color: #52AA59;\n  border-radius: 100px;\n  text-indent: 100%;\n  white-space: nowrap;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field settings container\n*\n*----------------------------------------------------------------------------*/\n.acf-field-type-settings {\n  container-name: settings;\n  container-type: inline-size;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Split field settings\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-split {\n  display: flex;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.acf-field-settings-split .acf-field {\n  margin: 0;\n  padding-top: 32px;\n  padding-bottom: 32px;\n}\n.acf-field-settings-split .acf-field:nth-child(2n) {\n  border-left-width: 1px;\n  border-left-style: solid;\n  border-left-color: #EAECF0;\n}\n\n@container settings (max-width: 1170px) {\n  .acf-field-settings-split {\n    border: none;\n    flex-direction: column;\n  }\n  .acf-field {\n    border-top-width: 1px;\n    border-top-style: solid;\n    border-top-color: #EAECF0;\n  }\n}\n/*----------------------------------------------------------------------------\n*\n*  Display & return format\n*\n*----------------------------------------------------------------------------*/\n.acf-field-setting-display_format .acf-label,\n.acf-field-setting-return_format .acf-label {\n  margin-bottom: 6px;\n}\n.acf-field-setting-display_format .acf-radio-list li,\n.acf-field-setting-return_format .acf-radio-list li {\n  display: flex;\n}\n.acf-field-setting-display_format .acf-radio-list li label,\n.acf-field-setting-return_format .acf-radio-list li label {\n  display: inline-flex;\n  width: 100%;\n}\n.acf-field-setting-display_format .acf-radio-list li label span,\n.acf-field-setting-return_format .acf-radio-list li label span {\n  flex: 1 1 auto;\n}\n.acf-field-setting-display_format .acf-radio-list li label code,\n.acf-field-setting-return_format .acf-radio-list li label code {\n  padding-right: 8px;\n  padding-left: 8px;\n  background-color: #F2F4F7;\n  border-radius: 4px;\n  color: #475467;\n}\n.acf-field-setting-display_format .acf-radio-list li input[type=text],\n.acf-field-setting-return_format .acf-radio-list li input[type=text] {\n  height: 32px;\n}\n\n.acf-field-settings .acf-field-setting-first_day {\n  padding-top: 32px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Image and Gallery fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-image .acf-hl[data-cols=\"3\"] > li,\n.acf-field-object-gallery .acf-hl[data-cols=\"3\"] > li {\n  width: auto;\n}\n\n/*----------------------------------------------------------------------------\n*\n* Appended fields fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-appended {\n  overflow: auto;\n}\n.acf-field-settings .acf-field-appended .acf-input {\n  float: left;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible widths for image minimum / maximum size fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field.acf-field-setting-min_width .acf-input,\n.acf-field-settings .acf-field.acf-field-setting-max_width .acf-input {\n  max-width: none;\n}\n.acf-field-settings .acf-field.acf-field-setting-min_width .acf-input-wrap input[type=text],\n.acf-field-settings .acf-field.acf-field-setting-max_width .acf-input-wrap input[type=text] {\n  max-width: 81px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Temporary fix to hide pagination setting for repeaters used as subfields.\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object-flexible-content .acf-field-setting-pagination {\n  display: none;\n}\n.post-type-acf-field-group .acf-field-object-repeater .acf-field-object-repeater .acf-field-setting-pagination {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible content field width\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-field-object-flexible-content .acf-is-subfields .acf-field-object .acf-label,\n.acf-admin-single-field-group .acf-field-object-flexible-content .acf-is-subfields .acf-field-object .acf-input {\n  max-width: 600px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Fix default value checkbox focus state\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-field.acf-field-true-false.acf-field-setting-default_value .acf-true-false {\n  border: none;\n}\n.acf-admin-single-field-group .acf-field.acf-field-true-false.acf-field-setting-default_value .acf-true-false input[type=checkbox] {\n  margin-right: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  With front field extra spacing\n*\n*----------------------------------------------------------------------------*/\n.acf-field.acf-field-with-front {\n  margin-top: 32px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Sub-fields layout\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub {\n  max-width: 100%;\n  overflow: hidden;\n  border-radius: 8px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: rgb(219.125, 222.5416666667, 229.375);\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-sub-field-list-header {\n  display: flex;\n  justify-content: space-between;\n  align-content: stretch;\n  align-items: center;\n  min-height: 64px;\n  padding-right: 24px;\n  padding-left: 24px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-list-wrap {\n  box-shadow: none;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-hl.acf-tfoot {\n  min-height: 64px;\n  align-items: center;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input.acf-input-sub {\n  max-width: 100%;\n  margin-right: 0;\n  margin-left: 0;\n}\n\n.post-type-acf-field-group .acf-input-sub .acf-field-object .acf-sortable-handle {\n  width: 100%;\n  height: 100%;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-sortable-handle:before {\n  display: none;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-field-list .acf-field-object:hover .acf-sortable-handle:before {\n  display: block;\n}\n\n.post-type-acf-field-group .acf-field-object .acf-is-subfields .acf-thead .li-field-label:before {\n  display: none;\n}\n\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open {\n  border-top-color: rgb(219.125, 222.5416666667, 229.375);\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Flexible content field\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group i.acf-icon.-duplicate.duplicate-layout {\n  margin: 0 auto !important;\n  background-color: #667085;\n  color: #667085;\n}\n.post-type-acf-field-group i.acf-icon.acf-icon-trash.delete-layout {\n  margin: 0 auto !important;\n  background-color: #667085;\n  color: #667085;\n}\n.post-type-acf-field-group button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-duplicate, .post-type-acf-field-group button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-delete {\n  background-color: #ffffff !important;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  width: 32px;\n  height: 32px !important;\n  min-height: 32px;\n  padding: 0;\n}\n.post-type-acf-field-group button.add-layout.acf-btn.acf-btn-primary.add-field,\n.post-type-acf-field-group .acf-sub-field-list-header a.acf-btn.acf-btn-secondary.add-field,\n.post-type-acf-field-group .acf-field-list-wrap.acf-is-subfields a.acf-btn.acf-btn-secondary.add-field {\n  height: 32px !important;\n  min-height: 32px;\n  margin-left: 5px;\n}\n.post-type-acf-field-group .acf-field.acf-field-setting-fc_layout {\n  background-color: #ffffff;\n  margin-bottom: 16px;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout {\n  width: calc(100% - 144px);\n  margin-right: 72px;\n  margin-left: 72px;\n  padding-right: 0;\n  padding-left: 0;\n  border-width: 1px;\n  border-style: solid;\n  border-color: rgb(219.125, 222.5416666667, 229.375);\n  border-radius: 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-field-layout-settings.open {\n  background-color: #ffffff;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n@media screen and (max-width: 768px) {\n  .post-type-acf-field-group .acf-field-setting-fc_layout {\n    width: calc(100% - 16px);\n    margin-right: 8px;\n    margin-left: 8px;\n  }\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input-sub {\n  max-width: 100%;\n  margin-right: 0;\n  margin-left: 0;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-label,\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input {\n  max-width: 100% !important;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input-sub {\n  margin-right: 32px;\n  margin-bottom: 32px;\n  margin-left: 32px;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-fc-meta {\n  max-width: 100%;\n  padding-top: 24px;\n  padding-right: 32px;\n  padding-left: 32px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head {\n  display: flex;\n  align-items: center;\n  justify-content: left;\n  background-color: #F9FAFB;\n  border-radius: 8px;\n  min-height: 64px;\n  margin-bottom: 0px;\n  padding-right: 24px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc_draggable {\n  min-height: 64px;\n  padding-left: 24px;\n  display: flex;\n  white-space: nowrap;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name {\n  min-width: 0;\n  color: #98A2B3;\n  padding-left: 8px;\n  font-size: 16px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name.copyable:not(.input-copyable, .copy-unsupported):hover:after {\n  width: 14px !important;\n  height: 14px !important;\n}\n@media screen and (max-width: 880px) {\n  .post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name {\n    display: none !important;\n  }\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc-layout-name span {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head span.toggle-indicator {\n  pointer-events: none;\n  margin-top: 7px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head label {\n  display: inline-flex;\n  align-items: center;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-name {\n  margin-left: 1rem;\n}\n@media screen and (max-width: 880px) {\n  .post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-name {\n    display: none !important;\n  }\n}\n.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-name span.acf-fc-layout-name {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  height: 22px;\n  white-space: nowrap;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-label:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n.rtl.post-type-acf-field-group .acf-field-settings-fc_head label.acf-fc-layout-label:before {\n  padding-right: 10px;\n}\n\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions {\n  display: flex;\n  align-items: center;\n  white-space: nowrap;\n  margin-left: auto;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions .acf-fc-add-layout {\n  margin-left: 10px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions .acf-fc-add-layout .add-field {\n  margin-left: 0px !important;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions li {\n  margin-right: 4px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions li:last-of-type {\n  margin-right: 0;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head.open {\n  border-radius: 8px 8px 0px 0px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field open / closed icon state\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object.open > .handle > .acf-tbody > .li-field-label::before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Different coloured levels (current 5 supported)\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .handle:hover {\n  background-color: rgb(248.6, 242, 251);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open .handle {\n  background-color: rgb(244.76, 234.2, 248.6);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .settings {\n  border-left-color: #BF7DD7;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {\n  background-color: rgb(234.7348066298, 247.2651933702, 244.1712707182);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object.open .handle {\n  background-color: rgb(227.3524861878, 244.4475138122, 240.226519337);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .settings {\n  border-left-color: #7CCDB9;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {\n  background-color: rgb(252.2544378698, 244.8698224852, 241.7455621302);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object.open .handle {\n  background-color: rgb(250.5041420118, 238.4118343195, 233.2958579882);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .settings {\n  border-left-color: #E29473;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {\n  background-color: rgb(249.8888888889, 250.6666666667, 251.1111111111);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object.open .handle {\n  background-color: rgb(244.0962962963, 245.7555555556, 246.7037037037);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .settings {\n  border-left-color: #A3B1B9;\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* colors */\n$acf_blue: #2a9bd9;\n$acf_notice: #2a9bd9;\n$acf_error: #d94f4f;\n$acf_success: #49ad52;\n$acf_warning: #fd8d3b;\n\n/* acf-field */\n$field_padding: 15px 12px;\n$field_padding_x: 12px;\n$field_padding_y: 15px;\n$fp: 15px 12px;\n$fy: 15px;\n$fx: 12px;\n\n/* responsive */\n$md: 880px;\n$sm: 640px;\n\n// Admin.\n$wp-card-border: #ccd0d4;\t\t\t// Card border.\n$wp-card-border-1: #d5d9dd;\t\t  // Card inner border 1: Structural (darker).\n$wp-card-border-2: #eeeeee;\t\t  // Card inner border 2: Fields (lighter).\n$wp-input-border: #7e8993;\t\t   // Input border.\n\n// Admin 3.8\n$wp38-card-border: #E5E5E5;\t\t  // Card border.\n$wp38-card-border-1: #dfdfdf;\t\t// Card inner border 1: Structural (darker).\n$wp38-card-border-2: #eeeeee;\t\t// Card inner border 2: Fields (lighter).\n$wp38-input-border: #dddddd;\t\t // Input border.\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Grays\n$gray-50:  #F9FAFB;\n$gray-100: #F2F4F7;\n$gray-200: #EAECF0;\n$gray-300: #D0D5DD;\n$gray-400: #98A2B3;\n$gray-500: #667085;\n$gray-600: #475467;\n$gray-700: #344054;\n$gray-800: #1D2939;\n$gray-900: #101828;\n\n// Blues\n$blue-50:  #EBF5FA;\n$blue-100: #D8EBF5;\n$blue-200: #A5D2E7;\n$blue-300: #6BB5D8;\n$blue-400: #399CCB;\n$blue-500: #0783BE;\n$blue-600: #066998;\n$blue-700: #044E71;\n$blue-800: #033F5B;\n$blue-900: #032F45;\n\n// Utility\n$color-info:\t#2D69DA;\n$color-success:\t#52AA59;\n$color-warning:\t#F79009;\n$color-danger:\t#D13737;\n\n$color-primary: $blue-500;\n$color-primary-hover: $blue-600;\n$color-secondary: $gray-500;\n$color-secondary-hover: $gray-400;\n\n// Gradients\n$gradient-pro: radial-gradient(141.77% 141.08% at 100.26% 99.25%, #0ECAD4 0%, #7A45E5 100%);\n\n// Border radius\n$radius-sm:\t4px;\n$radius-md: 6px;\n$radius-lg: 8px;\n$radius-xl: 12px;\n\n// Elevations / Box shadows\n$elevation-01: 0px 1px 2px rgba($gray-900, 0.10);\n\n// Input & button focus outline\n$outline: 3px solid $blue-50;\n\n// Link colours\n$link-color: $blue-500;\n\n// Responsive\n$max-width: 1440px;", "/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n@mixin clearfix() {\n\t&:after {\n\t\tdisplay: block;\n\t\tclear: both;\n\t\tcontent: \"\";\n\t}\n}\n\n@mixin border-box() {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n}\n\n@mixin centered() {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n@mixin animate( $properties: 'all' ) {\n\t-webkit-transition: $properties 0.3s ease;  // Safari 3.2+, Chrome\n    -moz-transition: $properties 0.3s ease;  \t// Firefox 4-15\n    -o-transition: $properties 0.3s ease;  \t\t// Opera 10.5–12.00\n    transition: $properties 0.3s ease;  \t\t// Firefox 16+, Opera 12.50+\n}\n\n@mixin rtl() {\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t\t@content;\n\t}\n}\n\n@mixin wp-admin( $version: '3-8' ) {\n\t.acf-admin-#{$version} & {\n\t\t@content;\n\t}\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tField Group\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Reset postbox inner padding.\n#acf-field-group-fields > .inside,\n#acf-field-group-locations > .inside,\n#acf-field-group-options > .inside {\n\tpadding: 0;\n\tmargin: 0;\n}\n\n// Hide metabox order buttons added in WP 5.5.\n.postbox {\n\t.handle-order-higher,\n\t.handle-order-lower {\n\t\tdisplay: none;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Publish\n*\n*----------------------------------------------------------------------------*/\n#minor-publishing-actions,\n#misc-publishing-actions #visibility,\n#misc-publishing-actions .edit-timestamp {\n\tdisplay: none;\n}\n\n#minor-publishing {\n\tborder-bottom: 0 none;\n}\n\n#misc-pub-section {\n\tborder-bottom: 0 none;\n}\n\n#misc-publishing-actions .misc-pub-section {\n\tborder-bottom-color: #F5F5F5;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Fields\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-fields {\n\tborder: 0 none;\n\n\t.inside {\n\t\tborder-top: {\n\t\t\twidth: 0;\n\t\t\tstyle: none;\n\t\t};\n\t}\n\n\t/* links */\n\ta {\n\t\ttext-decoration: none;\n\t}\n\n\t/* Field type */\n\t.li-field-type {\n\n\t\t.field-type-icon {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: 600px) {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t}\n\n\t\t.field-type-label {\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\t.acf-pro-label-field-type {\n\t\t\tposition: relative;\n\t\t\ttop: -3px;\n\t\t\tmargin-left: 8px;\n\n\t\t\timg {\n\t\t\t\tmax-width: 34px;\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/* table header */\n\t.li-field-order {\n\t\twidth: 64px;\n\t\tjustify-content: center;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\twidth: 32px;\n\t\t}\n\n\t}\n\t.li-field-label { width: calc(50% - 64px); }\n\t.li-field-name { width: 25%; word-break: break-word; }\n\t.li-field-key { display: none; }\n\t.li-field-type { width: 25%; }\n\n\t/* show keys */\n\t&.show-field-keys {\n\n\t\t.li-field-label { width: calc(35% - 64px); };\n\t\t.li-field-name { width: 15%; };\n\t\t.li-field-key { width: 25%;  display: flex;  };\n\t\t.li-field-type { width: 25%; };\n\n\t}\n\n\t/* hide tabs */\n\t&.hide-tabs {\n\t\t.acf-field-settings-tab-bar {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t.acf-field-settings-main {\n\t\t\tpadding: 0;\n\n\t\t\t&.acf-field-settings-main-general {\n\t\t\t\tpadding-top: 32px;\n\t\t\t}\n\n\t\t\t.acf-field {\n\t\t\t\tmargin-bottom: 32px;\n\t\t\t}\n\n\t\t\t.acf-field-setting-wrapper {\n\t\t\t\tpadding-top: 0;\n\t\t\t\tborder-top: none;\n\t\t\t}\n\n\t\t\t.acf-field-settings-split .acf-field {\n\t\t\t\tborder-bottom: {\n\t\t\t\t\twidth: 1px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: $gray-200;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-field-setting-first_day {\n\t\t\t\tpadding-top: 0;\n\t\t\t\tborder-top: none;\n\t\t\t}\n\t\t}\n\n\t\t.acf-field-settings-footer {\n\t\t\tmargin-top: 32px;\n\t\t}\n\t}\n\n\t/* fields */\n\t.acf-field-list-wrap {\n\t\tborder: $wp-card-border solid 1px;\n\t}\n\n\t.acf-field-list {\n\t\tbackground: #f5f5f5;\n\t\tmargin-top: -1px;\n\n\t\t.acf-tbody {\n\n\t\t\t> .li-field-name,\n\t\t\t> .li-field-key {\n\t\t\t\talign-items: flex-start;\n\t\t\t}\n\n\t\t}\n\n\t\t.copyable:not(.input-copyable, .copy-unsupported) {\n\t\t\tcursor: pointer;\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\n\t\t\t&:hover:after {\n\t\t\t\tcontent: '';\n\t\t\t\tpadding-left: 5px;\n\t\t\t\t$icon-size: 12px;\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tbackground-color: $gray-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\ttext-indent: 500%;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-copy.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-copy.svg');\n\t\t\t\tbackground-size: cover;\n\t\t\t}\n\n\t\t\t&.sub-label {\n\t\t\t\tpadding-right: 22px;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tpadding-right: 0;\n\n\t\t\t\t\t&:after {\n\t\t\t\t\t\t$icon-size: 14px;\n\t\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\t\theight: $icon-size;\n\t\t\t\t\t\tpadding-left: 8px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.copied:hover:after {\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t\tbackground-color: $acf_success;\n\t\t\t}\n\t\t}\n\n\t\t.copyable.input-copyable:not(.copy-unsupported) {\n\t\t\tcursor: pointer;\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\t\t\talign-items: center;\n\n\t\t\tinput {\n\t\t\t\tpadding-right: 40px;\n\t\t\t}\n\n\t\t\t.acf-input-wrap:after {\n\t\t\t\tcontent: '';\n\t\t\t\tpadding-left: 5px;\n\t\t\t\t$icon-size: 16px;\n\t\t\t\tright: 12px;\n\t\t\t\ttop: 12px;\n\t\t\t\tposition: absolute;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\ttext-indent: 500%;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-copy.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-copy.svg');\n\t\t\t\tbackground-size: cover;\n\t\t\t}\n\n\t\t\t&.copied .acf-input-wrap:after {\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t\tbackground-color: $acf_success;\n\t\t\t}\n\t\t}\n\n\t\t\n\n\t\t/* no fields */\n\t\t.no-fields-message {\n\t\t\tpadding: 15px 15px;\n\t\t\tbackground: #fff;\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t/* empty */\n\t\t&.-empty {\n\t\t\t.no-fields-message {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin('3-8') {\n\t\t.acf-field-list-wrap {\n\t\t\tborder-color: $wp38-card-border-1;\n\t\t}\n\t}\n}\n\n\n.rtl #acf-field-group-fields {\n\t.li-field-type {\n\t\t.field-type-icon {\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t\tright: 0;\n\t\t\t};\n\t\t}\n\t}\n}\n\n/* field object */\n.acf-field-object {\n\tborder-top: $wp38-card-border-2 solid 1px;\n\tbackground: #fff;\n\n\t/* sortable */\n\t&.ui-sortable-helper {\n\t\toverflow: hidden !important;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $blue-200 !important;\n\t\t};\n\t\tborder-radius: $radius-lg;\n\t\tfilter: drop-shadow(0px 10px 20px rgba(16, 24, 40, 0.14)) drop-shadow(0px 1px 3px rgba(16, 24, 40, 0.1));\n\n\t\t&:before {\n\t\t\tdisplay: none !important;\n\t\t}\n\n\t}\n\n\t&.ui-sortable-placeholder {\n\t\tbox-shadow: 0 -1px 0 0 #DFDFDF;\n\t\tvisibility: visible !important;\n\t\tbackground: #F9F9F9;\n\t\tborder-top-color: transparent;\n\t\tmin-height: 54px;\n\n\t\t// hide tab field separator\n\t\t&:after, &:before {\n\t\t\tvisibility: hidden;\n\t\t}\n\t}\n\n\n\t/* meta */\n\t> .meta {\n\t\tdisplay: none;\n\t}\n\n\n\t/* handle */\n\t> .handle {\n\n\t\ta {\n\t\t\t-webkit-transition: none;\n\t\t\t-moz-transition: none;\n\t\t\t-o-transition: none;\n\t\t\ttransition: none;\n\t\t}\n\n\t\tli {\n\t\t\tword-wrap: break-word;\n\t\t}\n\n\t\tstrong {\n\t\t\tdisplay: block;\n\t\t\tpadding-bottom: 0;\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 14px;\n\t\t\tmin-height: 14px;\n\t\t}\n\n\t\t.row-options {\n\t\t\tdisplay: block;\n\t\t\topacity: 0;\n\t\t\tmargin: {\n\t\t\t\ttop: 5px;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: 880px) {\n\t\t\t\topacity: 1;\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\ta {\n\t\t\t\tmargin-right: 4px;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tcolor: darken($color-primary-hover, 10%);\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\ta.delete-field {\n\t\t\t\tcolor: #a00;\n\n\t\t\t\t&:hover { color: #f00; }\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\tvisibility: visible;\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/* open */\n\t&.open {\n\n\t\t+ .acf-field-object {\n\t\t\tborder-top-color: #E1E1E1;\n\t\t}\n\n\t\t> .handle {\n\t\t\tbackground: $acf_blue;\n\t\t\tborder: darken($acf_blue, 2%) solid 1px;\n\t\t\ttext-shadow: #268FBB 0 1px 0;\n\t\t\tcolor: #fff;\n\t\t\tposition: relative;\n\t\t\tmargin: 0 -1px 0 -1px;\n\n\t\t\ta {\n\t\t\t\tcolor: #fff !important;\n\n\t\t\t\t&:hover {\n\t\t\t\t\ttext-decoration: underline !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t}\n\n\n\t/*\n\t// debug\n\t&[data-save=\"meta\"] {\n\t\t> .handle {\n\t\t\tborder-left: #ffb700 solid 5px !important;\n\t\t}\n\t}\n\n\t&[data-save=\"settings\"] {\n\t\t> .handle {\n\t\t\tborder-left: #0ec563 solid 5px !important;\n\t\t}\n\t}\n*/\n\n\n\t/* hover */\n\t&:hover, &.-hover, &:focus-within {\n\n\t\t> .handle {\n\n\t\t\t.row-options {\n\t\t\t\topacity: 1;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t}\n\t}\n\n\n\t/* settings */\n\t> .settings {\n\t\tdisplay: none;\n\t\twidth: 100%;\n\n\t\t> .acf-table {\n\t\t\tborder: none;\n\t\t}\n\t}\n\n\n\t/* conditional logic */\n\t.rule-groups {\n\t\tmargin-top: 20px;\n\t}\n\n}\n\n\n/*----------------------------------------------------------------------------\n*\n* Postbox: Locations\n*\n*----------------------------------------------------------------------------*/\n\n.rule-groups {\n\n\th4 {\n\t\tmargin: 3px 0;\n\t}\n\n\t.rule-group {\n\t\tmargin: 0 0 5px;\n\n\t\th4 {\n\t\t\tmargin: 0 0 3px;\n\t\t}\n\n\t\ttd.param {\n\t\t\twidth: 35%;\n\t\t}\n\n\t\ttd.operator {\n\t\t\twidth: 20%;\n\t\t}\n\n\t\ttd.add {\n\t\t\twidth: 40px;\n\t\t}\n\n\t\ttd.remove {\n\t\t\twidth: 28px;\n\t\t\tvertical-align: middle;\n\n\t\t\ta {\n\t\t\t\twidth: 22px;\n\t\t\t\theight: 22px;\n\t\t\t\tvisibility: hidden;\n\n\t\t\t\t&:before {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\ttop: -2px;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t\ttr:hover td.remove a {\n\t\t\tvisibility: visible;\n\t\t}\n\n\t\t// empty select\n\t\tselect:empty {\n\t\t\tbackground: #f8f8f8;\n\t\t}\n\t}\n\n\n\t&:not(.rule-groups-multiple) {\n\t\t.rule-group {\n\t\t\t&:first-child tr:first-child td.remove a {\n\t\t\t\t/* Don't allow user to delete the only rule group */\n\t\t\t\tvisibility: hidden !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tOptions\n*\n*----------------------------------------------------------------------------*/\n\n#acf-field-group-options tr[data-name=\"hide_on_screen\"] li {\n\tfloat: left;\n\twidth: 33%;\n}\n\n@media (max-width: 1100px) {\n\n\t#acf-field-group-options tr[data-name=\"hide_on_screen\"] li {\n\t\twidth: 50%;\n\t}\n\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tConditional Logic\n*\n*----------------------------------------------------------------------------*/\n\ntable.conditional-logic-rules {\n\tbackground: transparent;\n\tborder: 0 none;\n\tborder-radius: 0;\n}\n\ntable.conditional-logic-rules tbody td {\n\tbackground: transparent;\n\tborder: 0 none !important;\n\tpadding: 5px 2px !important;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Tab\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-tab {\n\n\t// hide setting\n\t.acf-field-setting-name,\n\t.acf-field-setting-instructions,\n\t.acf-field-setting-required,\n\t.acf-field-setting-warning,\n\t.acf-field-setting-wrapper {\n\t\tdisplay: none;\n\t}\n\n\t// hide name\n\t.li-field-name {\n\t\tvisibility: hidden;\n\t}\n\n\tp:first-child {\n\t\tmargin: 0.5em 0;\n\t}\n\n\t// hide presentation setting tabs.\n\tli.acf-settings-type-presentation,\n\t.acf-field-settings-main-presentation {\n\t\tdisplay: none !important;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Accordion\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-accordion {\n\n\t// hide setting\n\t.acf-field-setting-name,\n\t.acf-field-setting-instructions,\n\t.acf-field-setting-required,\n\t.acf-field-setting-warning,\n\t.acf-field-setting-wrapper {\n\t\tdisplay: none;\n\t}\n\n\t// hide name\n\t.li-field-name {\n\t\tvisibility: hidden;\n\t}\n\n\tp:first-child {\n\t\tmargin: 0.5em 0;\n\t}\n\n\t// show settings\n\t.acf-field-setting-instructions {\n\t\tdisplay: block;\n\t}\n\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Message\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-message tr[data-name=\"name\"],\n.acf-field-object-message tr[data-name=\"instructions\"],\n.acf-field-object-message tr[data-name=\"required\"] {\n\tdisplay: none !important;\n}\n\n.acf-field-object-message .li-field-name {\n\tvisibility: hidden;\n}\n\n.acf-field-object-message textarea {\n\theight: 175px !important;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Separator\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-separator tr[data-name=\"name\"],\n.acf-field-object-separator tr[data-name=\"instructions\"],\n.acf-field-object-separator tr[data-name=\"required\"] {\n\tdisplay: none !important;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Date Picker\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-date-picker,\n.acf-field-object-time-picker,\n.acf-field-object-date-time-picker {\n\n\t.acf-radio-list {\n\n\t\tli {\n\t\t\tline-height: 25px;\n\t\t}\n\n\t\tspan {\n\t\t\tdisplay: inline-block;\n\t\t\tmin-width: 10em;\n\t\t}\n\n\t\tinput[type=\"text\"] {\n\t\t\twidth: 100px;\n\t\t}\n\t}\n\n}\n\n.acf-field-object-date-time-picker {\n\n\t.acf-radio-list {\n\n\t\tspan {\n\t\t\tmin-width: 15em;\n\t\t}\n\n\t\tinput[type=\"text\"] {\n\t\t\twidth: 200px;\n\t\t}\n\t}\n\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSlug\n*\n*--------------------------------------------------------------------------------------------*/\n\n#slugdiv {\n\n\t.inside {\n\t\tpadding: 12px;\n\t\tmargin: 0;\n\t}\n\n\tinput[type=\"text\"] {\n\t\twidth: 100%;\n\t\theight: 28px;\n\t\tfont-size: 14px;\n\t}\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\n\nhtml[dir=\"rtl\"] .acf-field-object.open > .handle {\n\tmargin: 0\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Device\n*\n*----------------------------------------------------------------------------*/\n\n@media only screen and (max-width: 850px) {\n\n\ttr.acf-field,\n\ttd.acf-label,\n\ttd.acf-input {\n\t\tdisplay: block !important;\n\t\twidth: auto !important;\n\t\tborder: 0 none !important;\n\t}\n\n\ttr.acf-field {\n\t\tborder-top: #ededed solid 1px !important;\n\t\tmargin-bottom: 0 !important;\n\t}\n\n\ttd.acf-label {\n\t\tbackground: transparent !important;\n\t\tpadding-bottom: 0 !important;\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Subtle background on accordion & tab fields to separate them from others\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t#acf-field-group-fields {\n\n\t\t.acf-field-object-tab,\n\t\t.acf-field-object-accordion {\n\t\t\tbackground-color: $gray-50;\n\t\t}\n\n\t}\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\t#wpcontent {\n\t\tline-height: 140%;\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\ta {\n\t\tcolor: $blue-500;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-h1 {\n\tfont-size: 21px;\n\tfont-weight: 400;\n}\n\n.acf-h2 {\n\tfont-size: 18px;\n\tfont-weight: 400;\n}\n\n.acf-h3 {\n\tfont-size: 16px;\n\tfont-weight: 400;\n}\n\n.acf-admin-page,\n.acf-headerbar {\n\n\th1 {\n\t\t@extend .acf-h1;\n\t}\n\n\th2 {\n\t\t@extend .acf-h2;\n\t}\n\n\th3 {\n\t\t@extend .acf-h3;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-admin-page {\n\n\t.p1 {\n\t\tfont-size: 15px;\n\t}\n\n\t.p2 {\n\t\tfont-size: 14px;\n\t}\n\n\t.p3 {\n\t\tfont-size: 13.5px;\n\t}\n\n\t.p4 {\n\t\tfont-size: 13px;\n\t}\n\n\t.p5 {\n\t\tfont-size: 12.5px;\n\t}\n\n\t.p6 {\n\t\tfont-size: 12px;\n\t}\n\n\t.p7 {\n\t\tfont-size: 11.5px;\n\t}\n\n\t.p8 {\n\t\tfont-size: 11px;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n\t@extend .acf-h2;\n\tcolor: $gray-700;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\n\t.acf-settings-wrap h1 {\n\t\tdisplay: none !important;\n\t}\n\n\t#acf-admin-tools h1:not(.acf-field-group-pro-features-title, .acf-field-group-pro-features-title-sm) {\n\t\tdisplay: none !important;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-small {\n\t@extend .p6;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-page {\n\ta:focus {\n\t\tbox-shadow: none;\n\t\toutline: none;\n\t}\n\n\ta:focus-visible {\n\t\tbox-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgb(79 148 212 / 80%);\n\t\toutline: 1px solid transparent;\n\t}\n}\n", ".acf-admin-page {\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  All Inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"],\n\tinput[type=\"search\"],\n\tinput[type=\"number\"],\n\ttextarea,\n\tselect {\n\t\tbox-sizing: border-box;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-color: #fff;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: $radius-md;\n\t\t/* stylelint-disable-next-line scss/at-extend-no-missing-placeholder */\n\t\t@extend .p4;\n\t\tcolor: $gray-700;\n\n\t\t&:focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: lighten($gray-500, 10%);\n\t\t}\n\n\t\t&::placeholder {\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Read only text inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"] {\n\n\t\t&:read-only {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Number fields\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field.acf-field-number {\n\n\t\t.acf-label,\n\t\t.acf-input input[type=\"number\"] {\n\t\t\tmax-width: 180px;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Textarea\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\ttextarea {\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 10px;\n\t\t\tbottom: 10px;\n\t\t};\n\t\theight: 80px;\n\t\tmin-height: 56px;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tselect {\n\t\tmin-width: 160px;\n\t\tmax-width: 100%;\n\t\tpadding: {\n\t\t\tright: 40px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-image: url('../../images/icons/icon-chevron-down.svg');\n\t\tbackground-position: right 10px top 50%;\n\t\tbackground-size: 20px;\n\t\t@extend .p4;\n\n\t\t&:hover,\n\t\t&:focus {\n\t\t\tcolor: $blue-500;\n\t\t}\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 5px;\n\t\t\tleft: 5px;\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\n\t\tselect {\n\t\t\tpadding: {\n\t\t\t\tright: 12px;\n\t\t\t\tleft: 40px;\n\t\t\t};\n\t\t\tbackground-position: left 10px top 50%;\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Button & Checkbox base styling\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"],\n\tinput[type=\"checkbox\"] {\n\t\tbox-sizing: border-box;\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\tpadding: 0;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-400;\n\t\t};\n\t\tbackground: #fff;\n\t\tbox-shadow: none;\n\n\t\t&:hover {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\t\t}\n\n\t\t&:checked,\n\t\t&:focus-visible {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -1px;\n\t\t\t\tleft: -1px;\n\t\t\t\twidth: 16px;\n\t\t\t\theight: 16px;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tbackground-size: cover;\n\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\tbackground-position: center;\n\t\t\t}\n\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-color: $gray-300;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\n\t\tinput[type=\"radio\"],\n\t\tinput[type=\"checkbox\"] {\n\n\t\t\t&:checked,\n\t\t\t&:focus-visible {\n\n\t\t\t\t&:before {\n\t\t\t\t\tleft: 1px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/radio-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Checkboxes\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"checkbox\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/checkbox-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons & Checkbox lists\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-radio-list,\n\t.acf-checkbox-list {\n\n\t\tli input[type=\"radio\"],\n\t\tli input[type=\"checkbox\"] {\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t};\n\t\t}\n\n\t\t&.acf-bl li {\n\t\t\tmargin: {\n\t\t\t\tbottom: 8px;\n\t\t\t};\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\n\t\t}\n\n\t\tlabel {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\talign-content: center;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF Switch\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-switch {\n\t\twidth: 42px;\n\t\theight: 24px;\n\t\tborder: none;\n\t\tbackground-color: $gray-300;\n\t\tborder-radius: 12px;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&.-on {\n\t\t\tbackground-color: $color-primary;\n\n\t\t\t&:hover {\n\t\t\t\tbackground-color: $color-primary-hover;\n\t\t\t}\n\n\t\t\t.acf-switch-slider {\n\t\t\t\tleft: 20px;\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-switch-off,\n\t\t.acf-switch-on {\n\t\t\tvisibility: hidden;\n\t\t}\n\n\t\t.acf-switch-slider {\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tborder: none;\n\t\t\tborder-radius: 100px;\n\t\t\tbox-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n\t\t}\n\n\t}\n\n\t.acf-field-true-false {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\n\t\t.acf-label {\n\t\t\torder: 2;\n\t\t\tdisplay: block;\n\t\t\talign-items: center;\n\t\t\tmax-width: 550px !important;\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t};\n\n\t\t\tlabel {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.description {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 2px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t}\n\n\t&.rtl {\n\n\t\t.acf-field-true-false {\n\n\t\t\t.acf-label {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  File input button\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\n\tinput::file-selector-button {\n\t\tbox-sizing: border-box;\n\t\tmin-height: 40px;\n\t\tmargin: {\n\t\t\tright: 16px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 16px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tcolor: $color-primary !important;\n\t\tborder-radius: $radius-md;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $color-primary;\n\t\t};\n\t\ttext-decoration: none;\n\n\t\t&:hover {\n\t\t\tborder-color: $color-primary-hover;\n\t\t\tcursor: pointer;\n\t\t\tcolor: $color-primary-hover !important;\n\t\t}\n\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Action Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.button {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 16px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tborder-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: $blue-500;\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $blue-500;\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\tborder-color: $color-primary;\n\t\t\tcolor: $color-primary;\n\t\t}\n\n\t\t&:focus {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\toutline: $outline;\n\t\t\tcolor: $color-primary;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Edit field group header\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.edit-field-group-header {\n\t\tdisplay: block !important;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select2 inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-input,\n\t.rule-groups {\n\n\t\t.select2-container.-acf .select2-selection {\n\t\t\tborder: none;\n\t\t\tline-height: 1;\n\t\t}\n\n\t\t.select2-container.-acf .select2-selection__rendered {\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tbackground-color: #fff;\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-300;\n\t\t\t};\n\t\t\tbox-shadow: $elevation-01;\n\t\t\tborder-radius: $radius-md;\n\t\t\t/* stylelint-disable-next-line scss/at-extend-no-missing-placeholder */\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\t.acf-conditional-select-name {\n\t\t\tmin-width: 180px;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\ttext-overflow: ellipsis;\n\t\t}\n\n\t\t.acf-conditional-select-id {\n\t\t\tpadding-right: 30px;\n\t\t}\n\n\t\t.value .select2-container--focus {\n\t\t\theight: 40px;\n\t\t}\n\n\t\t.value .select2-container--open .select2-selection__rendered {\n\t\t\tborder-color: $blue-400;\n\t\t}\n\n\t\t.select2-container--focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t\tborder-radius: $radius-md;\n\n\t\t\t.select2-selection__rendered {\n\t\t\t\tborder-color: $blue-400 !important;\n\t\t\t}\n\n\t\t\t&.select2-container--below.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-bottom-right-radius: 0 !important;\n\t\t\t\t\tborder-bottom-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t&.select2-container--above.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-top-right-radius: 0 !important;\n\t\t\t\t\tborder-top-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container .select2-search--inline .select2-search__field {\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\tleft: 6px;\n\t\t\t};\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tborder: none;\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container--default .select2-selection--multiple .select2-selection__rendered {\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 6px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 6px;\n\t\t\t};\n\t\t}\n\n\t\t.select2-selection__clear {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tright: 1px;\n\t\t\t};\n\t\t\ttext-indent: 100%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\tcolor: #fff;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 16px;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t}\n\n\t\t\t&:hover::before {\n\t\t\t\tbackground-color: $blue-500;\n\t\t\t}\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF label\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t.acf-icon-help {\n\t\t\t$icon-size: 18px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\tlabel {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t\t.description {\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Tooltip for field name field setting (result of a fix for keyboard navigation)\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field-setting-name .acf-tip {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 654px;\n\t\tcolor: #98A2B3;\n\n\t\t@at-root .rtl#{&} {\n\t\t\tleft: auto;\n\t\t\tright: 654px;\n\t\t}\n\n\t\t.acf-icon-help {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t}\n\t}\n\n\t/* Field Type Selection select2 */\n\t.acf-field-setting-type,\n\t.acf-field-permalink-rewrite,\n\t.acf-field-query-var,\n\t.acf-field-capability,\n\t.acf-field-parent-slug,\n\t.acf-field-data-storage,\n\t.acf-field-manage-terms,\n\t.acf-field-edit-terms,\n\t.acf-field-delete-terms,\n\t.acf-field-assign-terms,\n\t.acf-field-meta-box,\n\t.rule-groups {\n\n\t\t.select2-container.-acf {\n\t\t\tmin-height: 40px;\n\t\t}\n\n\t\t.select2-container--default .select2-selection--single {\n\n\t\t\t.select2-selection__rendered {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 800;\n\t\t\t\tmin-height: 40px;\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.field-type-icon {\n\t\t\t\ttop: auto;\n\t\t\t\twidth: 18px;\n\t\t\t\theight: 18px;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 2px;\n\t\t\t\t};\n\n\t\t\t\t&:before {\n\t\t\t\t\twidth: 9px;\n\t\t\t\t\theight: 9px;\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t\t.select2-container--open .select2-selection__rendered {\n\t\t\tborder-color: $blue-300 !important;\n\t\t\tborder-bottom-color: $gray-300 !important;\n\t\t}\n\n\t\t.select2-container--open.select2-container--below .select2-selection__rendered {\n\t\t\tborder-bottom-right-radius: 0 !important;\n\t\t\tborder-bottom-left-radius: 0 !important;\n\t\t}\n\n\t\t.select2-container--open.select2-container--above .select2-selection__rendered {\n\t\t\tborder-top-right-radius: 0 !important;\n\t\t\tborder-top-left-radius: 0 !important;\n\t\t\tborder-bottom-color: $blue-300 !important;\n\t\t\tborder-top-color: $gray-300 !important;\n\t\t}\n\n\t\t// icon margins\n\t\t.acf-selection.has-icon {\n\t\t\tmargin-left: 6px;\n\t\n\t\t\t@at-root .rtl#{&} {\n\t\t\t\tmargin-right: 6px;\n\t\t\t}\n\t\t}\n\t\t\n\t\t// Dropdown icon\n\t\t.select2-selection__arrow {\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\ttop: calc(50% - 10px);\n\t\t\tright: 12px;\n\t\t\tbackground-color: transparent;\n\t\t\t\n\t\t\t&:after {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: absolute;\n\t\t\t\tz-index: 850;\n\t\t\t\ttop: 1px;\n\t\t\t\tleft: 0;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tbackground-color: $gray-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\ttext-indent: 500%;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\t\t\t\n\t\t\tb[role=\"presentation\"] {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t// Open state\n\t\t.select2-container--open {\n\t\t\t\n\t\t\t// Swap chevron icon\n\t\t\t.select2-selection__arrow:after {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t}\n\n\t.acf-term-search-term-name {\n\t\tbackground-color: $gray-50;\n\t\tborder-top: 1px solid $gray-200;\n\t\tborder-bottom: 1px solid $gray-200;\n\t\tcolor: $gray-400;\n\t\tpadding: 5px 5px 5px 10px;\n\t\twidth: 100%;\n\t\tmargin: 0;\n\t\tdisplay: block;\n\t\tfont-weight: 300;\n\t}\n\n\t.field-type-select-results {\n\t\tposition: relative;\n\t\ttop: 4px;\n\t\tz-index: 1002;\n\t\tborder-radius: 0 0 $radius-md $radius-md;\n\t\tbox-shadow: 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n\n\t\t&.select2-dropdown--above {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column-reverse;\t  \n\t\t\ttop: 0;\n\t\t\tborder-radius: $radius-md $radius-md 0 0;\n\t\t\tz-index: 99999;\n\t\t}\n\t\t\n\t\t@at-root .select2-container.select2-container--open#{&} {\n\t\t\t// outline: 3px solid $blue-50;\n\t\t\tbox-shadow: 0px 0px 0px 3px #EBF5FA, 0px 8px 24px 4px rgba(16, 24, 40, 0.12);\n\t\t}\n\n\t\t// icon margins\n\t\t.acf-selection.has-icon {\n\t\t\tmargin-left: 6px;\n\n\t\t\t@at-root .rtl#{&} {\n\t\t\t\tmargin-right: 6px;\n\t\t\t}\n\t\t}\n\n\t\t// Search field\n\t\t.select2-search {\n\t\t\tposition: relative;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\n\t\t\t&--dropdown {\n\n\t\t\t\t&:after {\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\t$icon-size: 16px;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 12px;\n\t\t\t\t\tleft: 13px;\n\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\theight: $icon-size;\n\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-search.svg\");\n\t\t\t\t\tmask-image: url(\"../../images/icons/icon-search.svg\");\n\t\t\t\t\tbackground-color: $gray-400;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\t\tmask-size: contain;\n\t\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t\t-webkit-mask-position: center;\n\t\t\t\t\tmask-position: center;\n\t\t\t\t\ttext-indent: 500%;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\t\tright: 12px;\n\t\t\t\t\t\tleft: auto;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.select2-search__field {\n\t\t\t\tpadding-left: 38px;\n\n\t\t\t\tborder-right: 0;\n\t\t\t\tborder-bottom: 0;\n\t\t\t\tborder-left: 0;\n\t\t\t\tborder-radius: 0;\n\n\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\tpadding-right: 38px;\n\t\t\t\t\tpadding-left: 0;\n\t\t\t\t}\n\n\t\t\t\t&:focus {\n\t\t\t\t\tborder-top-color: $gray-300;\n\t\t\t\t\toutline: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-results__options {\n\t\t\tmax-height: 440px;\n\t\t}\n\t\t\n\t\t.select2-results__option {\n\n\t\t\t.select2-results__option--highlighted {\n\t\t\t\tbackground-color: $blue-500 !important;\n\t\t\t\tcolor: $gray-50 !important;\n\t\t\t}\n\t\t}\n\n\t\t// List items\n\t\t.select2-results__option .select2-results__option {\n\t\t\tdisplay: inline-flex;\n\t\t\tposition: relative;\n\t\t\twidth: calc(100% - 24px);\n\t\t\tmin-height: 32px;\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t}\n\t\t\talign-items: center;\n\t\t\t\n\t\t\t.field-type-icon {\n\t\t\t\ttop: auto;\n\t\t\t\twidth: 18px;\n\t\t\t\theight: 18px;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 2px;\n\t\t\t\t};\n\t\t\t\tbox-shadow: 0 0 0 1px $gray-50;\n\t\t\t\n\t\t\t\t&:before {\n\t\t\t\t\twidth: 9px;\n\t\t\t\t\theight: 9px;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t}\n\n\t\t.select2-results__option[aria-selected=\"true\"] {\n\t\t\tbackground-color: $blue-50 !important;\n\t\t\tcolor: $gray-700 !important;\n\t\t\t\n\t\t\t&:after {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 16px;\n\t\t\t\tright: 13px;\n\t\t\t\tposition: absolute;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-check.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-check.svg\");\n\t\t\t\tbackground-color: $blue-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\ttext-indent: 500%;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\n\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\tleft: 13px;\n\t\t\t\t\tright: auto;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.select2-results__group {\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\twidth: calc(100% - 24px);\n\t\t\tmin-height: 25px;\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t\tcolor: $gray-400;\n\t\t\tfont-size: 11px;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t};\n\t\t\tfont-weight: normal;\n\t\t}\n\t}\n\t\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  RTL arrow position\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t&.rtl {\n\n\t\t.acf-field-setting-type,\n\t\t.acf-field-permalink-rewrite,\n\t\t.acf-field-query-var {\n\n\t\t\t.select2-selection__arrow:after {\n\t\t\t\tright: auto;\n\t\t\t\tleft: 10px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.rtl.post-type-acf-field-group,\n.rtl.acf-internal-post-type {\n\n\t.acf-field-setting-name .acf-tip {\n\t\tleft: auto;\n\t\tright: 654px;\n\t}\n}", "/*----------------------------------------------------------------------------\n*\n*  Container sizes\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .metabox-holder.columns-1 {\n\t#acf-field-group-fields,\n\t#acf-field-group-options,\n\t.meta-box-sortables.ui-sortable,\n\t.notice {\n\t\tmax-width: $max-width;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Max width for notices in 1 column edit field group layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-1 {\n\t.notice {\n\t\tmax-width: $max-width;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Widen edit field group headerbar for 2 column layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-2 {\n\t.acf-headerbar .acf-headerbar-inner {\n\t\tmax-width: 100%;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Post stuff\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t#poststuff {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t#acf-field-group-fields .acf-field-list-wrap {\n\t\toverflow: hidden;\n\t\tborder: none;\n\t\tborder-radius: 0 0 $radius-lg $radius-lg;\n\t\tbox-shadow: $elevation-01;\n\n\t\t&.-empty {\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\n\t\t\t.acf-thead,\n\t\t\t.acf-tfoot {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t\t.no-fields-message {\n\t\t\t\tmin-height: 280px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table header\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t.acf-thead {\n\t\tbackground-color: $gray-50;\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t}\n\t\tborder-bottom: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t}\n\n\t\tli {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmin-height: 48px;\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 0;\n\t\t\t}\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table body\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t.acf-field-object {\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t}\n\n\t\t&:hover {\n\t\t\t.acf-sortable-handle:before {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t}\n\t\t}\n\n\t\t// Add divider to show which fields have endpoint\n\t\t&.acf-field-is-endpoint {\n\t\t\t&:before {\n\t\t\t\tdisplay: block;\n\t\t\t\tcontent: \"\";\n\t\t\t\theight: 2px;\n\t\t\t\twidth: 100%;\n\t\t\t\tbackground: $gray-300;\n\t\t\t\tmargin-top: -1px;\n\t\t\t}\n\n\t\t\t&.acf-field-object-accordion {\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\theight: 2px;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tbackground: $gray-300;\n\t\t\t\t\tz-index: 500;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 3%);\n\t\t}\n\n\t\t&.open {\n\t\t\tbackground-color: #fff;\n\t\t\tborder-top-color: $blue-200;\n\t\t}\n\n\t\t&.open .handle {\n\t\t\tbackground-color: $blue-100;\n\t\t\tborder: none;\n\t\t\ttext-shadow: none;\n\n\t\t\ta {\n\t\t\t\tcolor: $link-color !important;\n\n\t\t\t\t&.delete-field {\n\t\t\t\t\tcolor: #a00 !important;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.acf-field-setting-type .acf-hl {\n\t\t\tmargin: 0;\n\n\t\t\tli {\n\t\t\t\twidth: auto;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tflex-grow: 1;\n\t\t\t\t\tmargin-left: -10px;\n\t\t\t\t}\n\n\t\t\t\t&:nth-child(2) {\n\t\t\t\t\tpadding-right: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tul.acf-hl {\n\t\t\tdisplay: flex;\n\t\t\talign-items: stretch;\n\t\t}\n\n\t\t.handle li {\n\t\t\tdisplay: flex;\n\t\t\talign-items: top;\n\t\t\tflex-wrap: wrap;\n\t\t\tmin-height: 60px;\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\n\t\t\t&.li-field-label {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\talign-content: flex-start;\n\t\t\t\talign-items: flex-start;\n\t\t\t\twidth: auto;\n\n\t\t\t\ta.edit-field {\n\t\t\t\t\t@extend .p4;\n\t\t\t\t}\n\n\t\t\t\tstrong {\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\n\t\t\t\t.row-options {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\n\t\t\t\t.row-options a {\n\t\t\t\t\t@extend .p6;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table footer\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t.acf-tfoot {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-end;\n\t\tmin-height: 80px;\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 24px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 24px;\n\t\t}\n\t\tbackground-color: #fff;\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t}\n\n\t\t.acf-fr {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit field settings\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object .settings {\n\tbox-sizing: border-box;\n\tpadding: {\n\t\ttop: 0;\n\t\tbottom: 0;\n\t}\n\tbackground-color: #fff;\n\tborder-left: {\n\t\twidth: 4px;\n\t\tstyle: solid;\n\t\tcolor: $blue-300;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Main field settings container\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main {\n\tpadding: {\n\t\ttop: 32px;\n\t\tright: 0;\n\t\tbottom: 32px;\n\t\tleft: 0;\n\t}\n\n\t.acf-field:last-of-type,\n\t.acf-field.acf-last-visible {\n\t\tmargin: {\n\t\t\tbottom: 0;\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field label\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-label {\n\tdisplay: block;\n\tjustify-content: space-between;\n\talign-items: center;\n\talign-content: center;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 6px;\n\t\tleft: 0;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Single field\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field {\n\tbox-sizing: border-box;\n\twidth: 100%;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 32px;\n\t\tleft: 0;\n\t}\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 72px;\n\t\tbottom: 0;\n\t\tleft: 72px;\n\t}\n\n\t@media screen and (max-width: 600px) {\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t}\n\t}\n\n\t.acf-label,\n\t.acf-input {\n\t\tmax-width: 600px;\n\n\t\t&.acf-input-sub {\n\t\t\tmax-width: 100%;\n\t\t}\n\n\t\t.acf-btn {\n\t\t\t&:disabled {\n\t\t\t\tbackground-color: $gray-100;\n\t\t\t\tcolor: $gray-400 !important;\n\t\t\t\tborder: 1px $gray-300 solid;\n\t\t\t\tcursor: default;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-input-wrap {\n\t\toverflow: visible;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field separators\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-settings .acf-field.acf-field-setting-label,\n.acf-field-settings .acf-field-setting-wrapper {\n\tpadding: {\n\t\ttop: 24px;\n\t}\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t}\n}\n\n.acf-field-settings .acf-field-setting-wrapper {\n\tmargin: {\n\t\ttop: 24px;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Informational Notes for specific fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-setting-bidirectional_notes {\n\t.acf-label {\n\t\tdisplay: none;\n\t}\n\n\t.acf-feature-notice {\n\t\tbackground-color: $gray-50;\n\t\tborder: 1px solid $gray-200;\n\t\tborder-radius: 6px;\n\t\tpadding: 16px;\n\t\tcolor: $gray-700;\n\t\tposition: relative;\n\n\t\t&.with-warning-icon {\n\t\t\tpadding-left: 45px;\n\t\t\t&::before {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 18px;\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 17px;\n\t\t\t\tleft: 18px;\n\t\t\t\tz-index: 600;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t}\n\t\t\t\tbackground-color: $gray-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-info.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-info.svg\");\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit fields footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-settings-footer {\n\tdisplay: flex;\n\talign-items: center;\n\tmin-height: 72px;\n\tbox-sizing: border-box;\n\twidth: 100%;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t}\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 72px;\n\t}\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t}\n\n\t@media screen and (max-width: 600px) {\n\t\tpadding: {\n\t\t\tleft: 12px;\n\t\t}\n\t}\n}\n\n.rtl .acf-field-settings .acf-field-settings-footer {\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 72px;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Tabs\n*\n*----------------------------------------------------------------------------*/\n.acf-fields,\n.acf-admin-page.acf-internal-post-type,\n.acf-browse-fields-modal-wrap {\n\t.acf-tab-wrap {\n\t\tbackground: $gray-50;\n\t\tborder-bottom: {\n\t\t\tcolor: $gray-800;\n\t\t}\n\n\t\t.acf-tab-group {\n\t\t\tpadding: {\n\t\t\t\tright: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t}\n\t\t\tborder-top: {\n\t\t\t\twidth: 0;\n\t\t\t}\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-field-settings-tab-bar,\n\t.acf-tab-wrap .acf-tab-group {\n\t\tdisplay: flex;\n\t\talign-items: stretch;\n\t\tmin-height: 48px;\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 24px;\n\t\t}\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tbottom: 0;\n\t\t}\n\t\tborder-bottom: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t}\n\t\tli {\n\t\t\tdisplay: flex;\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t\tpadding: 0;\n\n\t\t\ta {\n\t\t\t\t&:focus-visible {\n\t\t\t\t\tborder: 1px solid #5897fb;\n\t\t\t\t}\n\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\talign-items: center;\n\t\t\t\theight: 100%;\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 3px;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t\tbackground: none;\n\t\t\t\tborder-top: none;\n\t\t\t\tborder-right: none;\n\t\t\t\tborder-bottom: {\n\t\t\t\t\twidth: 3px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: transparent;\n\t\t\t\t}\n\t\t\t\tborder-left: none;\n\t\t\t\tcolor: $gray-500;\n\t\t\t\t@extend .p5;\n\t\t\t\tfont-weight: normal;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tcolor: $gray-800;\n\t\t\t\t}\n\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.active a {\n\t\t\t\tbackground: none;\n\t\t\t\tborder-bottom: {\n\t\t\t\t\tcolor: $color-primary;\n\t\t\t\t}\n\t\t\t\tcolor: $blue-500;\n\n\t\t\t\t&:focus-visible {\n\t\t\t\t\tborder-bottom: {\n\t\t\t\t\t\tcolor: $color-primary;\n\t\t\t\t\t\twidth: 3px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.acf-admin-page.acf-internal-post-type\n\t.acf-field-editor\n\t.acf-field-settings-tab-bar {\n\tpadding: {\n\t\tleft: 72px;\n\t}\n\n\t@media screen and (max-width: 600px) {\n\t\tpadding: {\n\t\t\tleft: 12px;\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field group settings\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-options {\n\t.field-group-settings-tab {\n\t\tpadding: {\n\t\t\ttop: 24px;\n\t\t\tright: 24px;\n\t\t\tbottom: 24px;\n\t\t\tleft: 24px;\n\t\t}\n\n\t\t.acf-field:last-of-type {\n\t\t\tpadding: 0;\n\t\t}\n\t}\n\n\t.acf-field {\n\t\tborder: none;\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 24px;\n\t\t\tleft: 0;\n\t\t}\n\t}\n\n\t// Split layout\n\t.field-group-setting-split-container {\n\t\tdisplay: flex;\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\n\t\t.field-group-setting-split {\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t}\n\t\t}\n\n\t\t.field-group-setting-split:nth-child(1) {\n\t\t\tflex: 1 0 auto;\n\t\t}\n\n\t\t.field-group-setting-split:nth-child(2n) {\n\t\t\tflex: 1 0 auto;\n\t\t\tmax-width: 320px;\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 32px;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\tright: 32px;\n\t\t\t\tleft: 32px;\n\t\t\t}\n\t\t\tborder-left: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Description field\n\t.acf-field[data-name=\"description\"] {\n\t\tmax-width: 600px;\n\t}\n\n\t// Button group\n\t.acf-button-group {\n\t\tdisplay: inline-flex;\n\t}\n}\n\n.rtl #acf-field-group-options {\n\t.field-group-setting-split-container {\n\t\t.field-group-setting-split:nth-child(2n) {\n\t\t\tmargin: {\n\t\t\t\tright: 32px;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t\tborder-left: none;\n\t\t\tborder-right: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Reorder handles\n*\n*----------------------------------------------------------------------------*/\n.acf-field-list {\n\t.li-field-order {\n\t\tpadding: 0;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: nowrap;\n\t\tjustify-content: center;\n\t\talign-content: stretch;\n\t\talign-items: stretch;\n\t\tbackground-color: transparent;\n\t}\n\n\t.acf-sortable-handle {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: nowrap;\n\t\tjustify-content: center;\n\t\talign-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: relative;\n\t\tpadding: {\n\t\t\ttop: 11px;\n\t\t\tbottom: 8px;\n\t\t}\n\t\t@extend .p4;\n\t\tbackground-color: transparent;\n\t\tborder: none;\n\t\tborder-radius: 0;\n\n\t\t&:hover {\n\t\t\tcursor: grab;\n\t\t}\n\n\t\t&:before {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: none;\n\t\t\tposition: absolute;\n\t\t\ttop: 16px;\n\t\t\tleft: 8px;\n\t\t\twidth: 16px;\n\t\t\theight: 16px;\n\t\t\t$icon-size: 12px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: $gray-400;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\ttext-indent: 500%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-draggable.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-draggable.svg\");\n\t\t}\n\t}\n}\n\n.rtl .acf-field-list {\n\t.acf-sortable-handle {\n\t\t&:before {\n\t\t\tleft: 0;\n\t\t\tright: 8px;\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Expand / collapse field icon\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object {\n\t.li-field-label {\n\t\tposition: relative;\n\t\tpadding: {\n\t\t\tleft: 40px;\n\t\t}\n\n\t\t&:before {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\tleft: 6px;\n\t\t\t$icon-size: 18px;\n\t\t\tdisplay: inline-flex;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\ttop: -2px;\n\t\t\t}\n\t\t\tbackground-color: $gray-500;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\ttext-indent: 500%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t}\n\n\t\t&:hover:before {\n\t\t\tcursor: pointer;\n\t\t}\n\t}\n}\n\n.rtl {\n\t.acf-field-object {\n\t\t.li-field-label {\n\t\t\tpadding: {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 40px;\n\t\t\t}\n\n\t\t\t&:before {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 6px;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t}\n\t\t}\n\n\t\t// Open\n\t\t&.open {\n\t\t\t.li-field-label:before {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t}\n\n\t\t\t.acf-input-sub .li-field-label:before {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n\t\t\t}\n\n\t\t\t.acf-input-sub .acf-field-object.open .li-field-label:before {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t}\n\t\t}\n\t}\n}\n\n.acf-thead {\n\t.li-field-label {\n\t\tpadding: {\n\t\t\tleft: 40px;\n\t\t}\n\t}\n\t.rtl & {\n\t\t.li-field-label {\n\t\t\tpadding: {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 40px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic layout\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main-conditional-logic {\n\n\t.acf-conditional-toggle {\n\t\tdisplay: flex;\n\t\tpadding: {\n\t\t\tright: 72px;\n\t\t\tleft: 72px;\n\t\t}\n\n\t\t@media screen and (max-width: 600px) {\n\t\t\tpadding: {\n\t\t\t\tleft: 12px;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-field {\n\t\tflex-wrap: wrap;\n\t\tmargin: {\n\t\t\tbottom: 0;\n\t\t}\n\t\tpadding: {\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t}\n\n\t\t.rule-groups {\n\t\t\tflex: 0 1 100%;\n\t\t\torder: 3;\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\ttop: 32px;\n\t\t\t\tright: 72px;\n\t\t\t\tleft: 72px;\n\t\t\t}\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\n\t\t\t@media screen and (max-width: 600px) {\n\t\t\t\tpadding: {\n\t\t\t\t\tleft: 12px;\n\t\t\t\t}\n\n\t\t\t\ttable.acf-table tbody tr {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\tjustify-content: flex-start;\n\t\t\t\t\talign-content: flex-start;\n\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\ttd {\n\t\t\t\t\t\tflex: 1 1 100%;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.acf-taxonomy-select-id,\n.acf-relationship-select-id,\n.acf-post_object-select-id,\n.acf-page_link-select-id,\n.acf-user-select-id {\n\tcolor: $gray-400;\n\tpadding-left: 10px;\n}\n\n.acf-taxonomy-select-sub-item {\n\tmax-width: 180px;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tmargin-left: 5px;\n}\n\n.acf-taxonomy-select-name {\n\tmax-width: 180px;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Prefix & append styling\n*\n*----------------------------------------------------------------------------*/\n.acf-input {\n\t.acf-input-prepend,\n\t.acf-input-append {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 100%;\n\t\tmin-height: 40px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t}\n\t\tbackground-color: $gray-50;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: $elevation-01;\n\t\tcolor: $gray-500;\n\t}\n\n\t.acf-input-prepend {\n\t\tborder-radius: $radius-md 0 0 $radius-md;\n\t}\n\n\t.acf-input-append {\n\t\tborder-radius: 0 $radius-md $radius-md 0;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  ACF input wrap\n*\n*----------------------------------------------------------------------------*/\n.acf-input-wrap {\n\tdisplay: flex;\n}\n\n.acf-field-settings-main-presentation .acf-input-wrap {\n\tdisplay: flex;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Empty state\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group\n\t#acf-field-group-fields\n\t.acf-field-list.-empty\n\t.no-fields-message {\n\tdisplay: flex;\n\tjustify-content: center;\n\tpadding: {\n\t\ttop: 48px;\n\t\tbottom: 48px;\n\t}\n\n\t.no-fields-message-inner {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: center;\n\t\talign-content: center;\n\t\talign-items: flex-start;\n\t\ttext-align: center;\n\t\tmax-width: 400px;\n\n\t\timg,\n\t\th2,\n\t\tp {\n\t\t\tflex: 1 0 100%;\n\t\t}\n\n\t\th2 {\n\t\t\t@extend .acf-h2;\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t\tbottom: 0;\n\t\t\t}\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\tp {\n\t\t\t@extend .p2;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t}\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-500;\n\n\t\t\t&.acf-small {\n\t\t\t\t@extend .p6;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 32px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\timg {\n\t\t\tmax-width: 284px;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t}\n\t\t}\n\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide add title prompt label\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t.acf-headerbar {\n\t\t#title-prompt-text {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Modal styling\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-page {\n\t#acf-popup .acf-popup-box {\n\t\tmin-width: 480px;\n\n\t\t.title {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\talign-content: center;\n\t\t\tjustify-content: space-between;\n\t\t\tmin-height: 64px;\n\t\t\tbox-sizing: border-box;\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\tright: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t}\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\n\t\t\th1,\n\t\t\th2,\n\t\t\th3,\n\t\t\th4 {\n\t\t\t\t@extend .acf-h3;\n\t\t\t\tpadding: {\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t\tcolor: $gray-700;\n\t\t\t}\n\n\t\t\t.acf-icon {\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: auto;\n\t\t\t\tright: auto;\n\t\t\t\twidth: 22px;\n\t\t\t\theight: 22px;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: transparent;\n\n\t\t\t\t&:before {\n\t\t\t\t\t$icon-size: 22px;\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\theight: $icon-size;\n\t\t\t\t\tbackground-color: $gray-500;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\t\tmask-size: contain;\n\t\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t\t-webkit-mask-position: center;\n\t\t\t\t\tmask-position: center;\n\t\t\t\t\ttext-indent: 500%;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-close-circle.svg\");\n\t\t\t\t\tmask-image: url(\"../../images/icons/icon-close-circle.svg\");\n\t\t\t\t}\n\n\t\t\t\t&:hover:before {\n\t\t\t\t\tbackground-color: $color-primary;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.inner {\n\t\t\tbox-sizing: border-box;\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t}\n\t\t\tborder-top: none;\n\n\t\t\tp {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Custom styling for move custom field modal/link field groups modal.\n\t\t#acf-move-field-form,\n\t\t#acf-link-field-groups-form {\n\t\t\t.acf-field-select {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Custom styling for the link field groups/create options page modal.\n\t.acf-link-field-groups-popup .acf-popup-box,\n\t.acf-create-options-page-popup .acf-popup-box {\n\t\t.title h3 {\n\t\t\tcolor: $gray-800;\n\t\t\tfont-weight: 500;\n\n\t\t\t&:before {\n\t\t\t\tcontent: \"\";\n\t\t\t\twidth: 18px;\n\t\t\t\theight: 18px;\n\t\t\t\tbackground: $gray-400;\n\t\t\t\tmargin-right: 9px;\n\t\t\t}\n\t\t}\n\n\t\t.inner {\n\t\t\tpadding: 0 !important;\n\n\t\t\t.acf-field-select,\n\t\t\t.acf-link-successful {\n\t\t\t\tpadding: 32px 24px;\n\t\t\t\tmargin-bottom: 0;\n\n\t\t\t\t.description {\n\t\t\t\t\tmargin-top: 6px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.acf-actions {\n\t\t\t\tbackground: $gray-50;\n\t\t\t\tborder-top: 1px solid $gray-200;\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 20px;\n\t\t\t\t\tleft: 24px;\n\t\t\t\t\tbottom: 20px;\n\t\t\t\t\tright: 24px;\n\t\t\t\t}\n\t\t\t\tborder-bottom-left-radius: 8px;\n\t\t\t\tborder-bottom-right-radius: 8px;\n\n\t\t\t\t.acf-btn {\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\tmargin-left: 8px;\n\n\t\t\t\t\t&.acf-btn-primary {\n\t\t\t\t\t\twidth: 120px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-create-options-page-popup .acf-popup-box {\n\t\t.inner {\n\t\t\t.acf-error-message.-success {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t\t.-dismiss {\n\t\t\t\tmargin: 24px 32px !important;\n\t\t\t}\n\n\t\t\t.acf-field {\n\t\t\t\tpadding: 24px 32px 0 32px;\n\t\t\t\tmargin: 0;\n\n\t\t\t\t&.acf-error {\n\t\t\t\t\t.acf-input-wrap {\n\t\t\t\t\t\toverflow: inherit;\n\n\t\t\t\t\t\tinput[type=\"text\"] {\n\t\t\t\t\t\t\tborder: 1px rgba($color-danger, 0.5) solid !important;\n\t\t\t\t\t\t\tbox-shadow:\n\t\t\t\t\t\t\t\t0px 0px 0px 3px rgba(209, 55, 55, 0.12),\n\t\t\t\t\t\t\t\t0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n\t\t\t\t\t\t\tbackground-image: url(../../images/icons/icon-info-red.svg);\n\t\t\t\t\t\t\tbackground-position: right 10px top 50%;\n\t\t\t\t\t\t\tbackground-size: 14px;\n\t\t\t\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.acf-options-page-modal-error p {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tcolor: $color-danger;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.acf-actions {\n\t\t\t\tmargin-top: 32px;\n\n\t\t\t\t.acf-btn:disabled {\n\t\t\t\t\tbackground-color: $blue-500;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide original #post-body-content from edit field group page\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group {\n\t#post-body-content {\n\t\tdisplay: none;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Settings section footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-group-settings-footer {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-content: stretch;\n\talign-items: center;\n\tposition: relative;\n\tmin-height: 88px;\n\tmargin: {\n\t\tright: -24px;\n\t\tleft: -24px;\n\t\tbottom: -24px;\n\t}\n\tpadding: {\n\t\tright: 24px;\n\t\tleft: 24px;\n\t}\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t}\n\n\t.acf-created-on {\n\t\tdisplay: inline-flex;\n\t\tjustify-content: flex-start;\n\t\talign-content: stretch;\n\t\talign-items: center;\n\t\t@extend .p5;\n\t\tcolor: $gray-500;\n\n\t\t&:before {\n\t\t\tcontent: \"\";\n\t\t\t$icon-size: 20px;\n\t\t\tdisplay: inline-block;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t}\n\t\t\tbackground-color: $gray-400;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-time.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-time.svg\");\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic enabled badge\n*\n*----------------------------------------------------------------------------*/\n.conditional-logic-badge {\n\tdisplay: none;\n\n\t&.is-enabled {\n\t\tdisplay: inline-block;\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\toverflow: hidden;\n\t\tmargin: {\n\t\t\tleft: 8px;\n\t\t}\n\t\tbackground-color: rgba($color-success, 0.4);\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $color-success;\n\t\t}\n\t\tborder-radius: 100px;\n\t\ttext-indent: 100%;\n\t\twhite-space: nowrap;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field settings container\n*\n*----------------------------------------------------------------------------*/\n.acf-field-type-settings {\n\tcontainer-name: settings;\n\tcontainer-type: inline-size;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Split field settings\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-split {\n\tdisplay: flex;\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t}\n\t.acf-field {\n\t\tmargin: 0;\n\t\tpadding: {\n\t\t\ttop: 32px;\n\t\t\tbottom: 32px;\n\t\t}\n\n\t\t&:nth-child(2n) {\n\t\t\tborder-left: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t}\n\t\t}\n\t}\n}\n\n@container settings (max-width: 1170px) {\n\t.acf-field-settings-split {\n\t\tborder: none;\n\t\tflex-direction: column;\n\t}\n\t.acf-field {\n\t\tborder-top-width: 1px;\n\t\tborder-top-style: solid;\n\t\tborder-top-color: $gray-200;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Display & return format\n*\n*----------------------------------------------------------------------------*/\n.acf-field-setting-display_format,\n.acf-field-setting-return_format {\n\t.acf-label {\n\t\tmargin: {\n\t\t\tbottom: 6px;\n\t\t}\n\t}\n\n\t.acf-radio-list {\n\t\tli {\n\t\t\tdisplay: flex;\n\n\t\t\tlabel {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\twidth: 100%;\n\n\t\t\t\tspan {\n\t\t\t\t\tflex: 1 1 auto;\n\t\t\t\t}\n\n\t\t\t\tcode {\n\t\t\t\t\tpadding: {\n\t\t\t\t\t\tright: 8px;\n\t\t\t\t\t\tleft: 8px;\n\t\t\t\t\t}\n\t\t\t\t\tbackground-color: $gray-100;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\t@extend .p5;\n\t\t\t\t\tcolor: $gray-600;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tinput[type=\"text\"] {\n\t\t\t\theight: 32px;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.acf-field-settings .acf-field-setting-first_day {\n\tpadding: {\n\t\ttop: 32px;\n\t}\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Image and Gallery fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-image,\n.acf-field-object-gallery {\n\t.acf-hl[data-cols=\"3\"] > li {\n\t\twidth: auto;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n* Appended fields fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-appended {\n\toverflow: auto;\n\n\t.acf-input {\n\t\tfloat: left;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible widths for image minimum / maximum size fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field.acf-field-setting-min_width,\n.acf-field-settings .acf-field.acf-field-setting-max_width {\n\t.acf-input {\n\t\tmax-width: none;\n\t}\n\n\t.acf-input-wrap input[type=\"text\"] {\n\t\tmax-width: 81px;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Temporary fix to hide pagination setting for repeaters used as subfields.\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t.acf-field-object-flexible-content {\n\t\t.acf-field-setting-pagination {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t.acf-field-object-repeater {\n\t\t.acf-field-object-repeater {\n\t\t\t.acf-field-setting-pagination {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible content field width\n*\n*----------------------------------------------------------------------------*/\n\n.acf-admin-single-field-group\n\t.acf-field-object-flexible-content\n\t.acf-is-subfields\n\t.acf-field-object {\n\t.acf-label,\n\t.acf-input {\n\t\tmax-width: 600px;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Fix default value checkbox focus state\n*\n*----------------------------------------------------------------------------*/\n\n.acf-admin-single-field-group {\n\t.acf-field.acf-field-true-false.acf-field-setting-default_value\n\t\t.acf-true-false {\n\t\tborder: none;\n\n\t\tinput[type=\"checkbox\"] {\n\t\t\tmargin-right: 0;\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  With front field extra spacing\n*\n*----------------------------------------------------------------------------*/\n.acf-field.acf-field-with-front {\n\tmargin: {\n\t\ttop: 32px;\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n*\n*  Sub-fields layout\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub {\n\tmax-width: 100%;\n\toverflow: hidden;\n\tborder-radius: $radius-lg;\n\tborder: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: darken($gray-200, 5%);\n\t};\n\tbox-shadow: $elevation-01;\n\n\t// Header\n\t.acf-sub-field-list-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-content: stretch;\n\t\talign-items: center;\n\t\tmin-height: 64px;\n\t\tpadding: {\n\t\t\tright: 24px;\n\t\t\tleft: 24px;\n\t\t};\n\t}\n\n\t// Main sub-fields wrapper\n\t.acf-field-list-wrap {\n\t\tbox-shadow: none;\n\t}\n\n\t// Sub-field footer\n\t.acf-hl.acf-tfoot {\n\t\tmin-height: 64px;\n\t\talign-items: center;\n\t}\n\t\n\t// Secondary level sub-fields\n\t.acf-input.acf-input-sub {\n\t\tmax-width: 100%;\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n}\n\n.post-type-acf-field-group .acf-input-sub .acf-field-object .acf-sortable-handle {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-sortable-handle:before {\n\tdisplay: none;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-field-list .acf-field-object:hover .acf-sortable-handle:before {\n\tdisplay: block;\n}\n\n.post-type-acf-field-group .acf-field-object .acf-is-subfields .acf-thead .li-field-label:before {\n\tdisplay: none;\n}\n\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open {\n\tborder-top-color: darken($gray-200, 5%);\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Flexible content field\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\ti.acf-icon.-duplicate.duplicate-layout {\n\t\tmargin: 0 auto !important;\n\t\tbackground-color: $gray-500;\n\t\tcolor: $gray-500;\n\t}\n\ti.acf-icon.acf-icon-trash.delete-layout {\n\t\tmargin: 0 auto !important;\n\t\tbackground-color: $gray-500;\n\t\tcolor: $gray-500;\n\t}\n\n\tbutton.acf-btn.acf-btn-tertiary.acf-field-setting-fc-duplicate, button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-delete {\n\t\tbackground-color: #ffffff !important;\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: 6px;\n\t\twidth: 32px;\n\t\theight: 32px !important;\n\t\tmin-height: 32px;\n\t\tpadding: 0;\n\t}\n\n\tbutton.add-layout.acf-btn.acf-btn-primary.add-field,\n\t.acf-sub-field-list-header a.acf-btn.acf-btn-secondary.add-field, \n\t.acf-field-list-wrap.acf-is-subfields a.acf-btn.acf-btn-secondary.add-field {\n\t\theight: 32px !important;\n\t\tmin-height: 32px;\n\t\tmargin-left: 5px;\n\t}\n\n\t.acf-field.acf-field-setting-fc_layout {\n\t\tbackground-color: #ffffff;\n\t\tmargin-bottom: 16px;\n\t}\n\t\n\t.acf-field-setting-fc_layout {\n\t\t.acf-field-layout-settings.open {\n\t\t\tbackground-color: #ffffff;\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t}\n\n\t\twidth: calc(100% - 144px);\n\t\tmargin: {\n\t\t\tright: 72px;\n\t\t\tleft: 72px;\n\t\t};\n\t\tpadding: {\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: darken($gray-200, 5%);\n\t\t};\n\t\tborder-radius: $radius-lg;\n\t\tbox-shadow: $elevation-01;\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\twidth: calc(100% - 16px);\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t}\n\n\t\t// Secondary level sub-fields\n\t\t.acf-input-sub {\n\t\t\tmax-width: 100%;\n\t\t\tmargin: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\n\t\t.acf-label,\n\t\t.acf-input {\n\t\t\tmax-width: 100% !important;\n\t\t}\n\n\t\t.acf-input-sub {\n\t\t\tmargin: {\n\t\t\t\tright: 32px;\n\t\t\t\tbottom: 32px;\n\t\t\t\tleft: 32px;\n\t\t\t};\n\t\t}\n\n\t\t.acf-fc-meta {\n\t\t\tmax-width: 100%;\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 32px;\n\t\t\t\tleft: 32px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t.acf-field-settings-fc_head {\n\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: left;\n\n\t\tbackground-color: $gray-50;\n\t\tborder-radius: 8px;\n\t\tmin-height: 64px;\n\t\tmargin: {\n\t\t\tbottom: 0px;\n\t\t};\n\t\tpadding: {\n\t\t\tright: 24px;\n\t\t};\n\n\t\t.acf-fc_draggable {\n\t\t\tmin-height: 64px;\n\t\t\tpadding-left: 24px;\n\t\t\tdisplay: flex;\n\t\t\twhite-space: nowrap;\n\t\t}\n\n\t\t.acf-fc-layout-name {\n\t\t\tmin-width: 0;\n\t\t\tcolor: $gray-400;\n\t\t\tpadding-left: 8px;\n\t\t\tfont-size: 16px;\n\n\t\t\t&.copyable:not(.input-copyable, .copy-unsupported):hover:after {\n\t\t\t\twidth: 14px !important;\n\t\t\t\theight: 14px !important;\n\t\t\t}\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tdisplay: none !important;\n\t\t\t}\n\n\t\t\tspan {\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\t\t}\n\n\t\tspan.toggle-indicator {\n\t\t\tpointer-events: none;\n\t\t\tmargin-top: 7px;\n\t\t}\n\n\t\tlabel {\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\t@extend .acf-h3;\n\n\t\t\t&.acf-fc-layout-name {\n\t\t\t\tmargin-left: 1rem;\n\n\t\t\t\t@media screen and (max-width: $md) {\n\t\t\t\t\tdisplay: none !important;\n\t\t\t\t}\n\n\t\t\t\tspan.acf-fc-layout-name {\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\theight: 22px;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&.acf-fc-layout-label:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t};\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\n\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\tpadding-right: 10px;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-fl-actions {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\twhite-space: nowrap;\n\t\t\tmargin-left: auto;\n\n\t\t\t.acf-fc-add-layout {\n\t\t\t\tmargin-left: 10px;\n\t\t\t}\n\n\t\t\t.acf-fc-add-layout .add-field {\n\t\t\t\tmargin-left: 0px !important;\n\t\t\t}\n\n\t\t\tli {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 4px;\n\t\t\t\t};\n\n\t\t\t\t&:last-of-type {\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-field-settings-fc_head.open {\n\t\tborder-radius: 8px 8px 0px 0px;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field open / closed icon state\n*\n*---------------------------------------------------------------------------------------------*/\n\n.post-type-acf-field-group .acf-field-object.open > .handle > .acf-tbody > .li-field-label::before {\n\t-webkit-mask-image: url('../../images/icons/icon-chevron-up.svg');\n\tmask-image: url('../../images/icons/icon-chevron-up.svg');\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Different coloured levels (current 5 supported)\n*\n*---------------------------------------------------------------------------------------------*/\n\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub {\n\t\n\t// Second level\n\t$nested-color: #BF7DD7;\n\t// Row hover color \n\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t// Active row color \n\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t// Active border color \n\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\n\t// Third level\n\t.acf-input-sub {\n\t\t$nested-color: #7CCDB9;\n\t\t// Row hover color \n\t\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t\t// Active row color \n\t\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t\t// Active border color \n\t\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\t\n\t\t// Fourth level\n\t\t.acf-input-sub {\n\t\t\t$nested-color: #E29473;\n\t\t\t// Row hover color \n\t\t\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t\t\t// Active row color \n\t\t\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t\t\t// Active border color \n\t\t\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\t\t\n\t\t\t// Fifth level\n\t\t\t.acf-input-sub {\n\t\t\t\t$nested-color: #A3B1B9;\n\t\t\t\t// Row hover color \n\t\t\t\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t\t\t\t// Active row color \n\t\t\t\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t\t\t\t// Active border color \n\t\t\t\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t}\n\t\n}"], "names": [], "sourceRoot": ""}