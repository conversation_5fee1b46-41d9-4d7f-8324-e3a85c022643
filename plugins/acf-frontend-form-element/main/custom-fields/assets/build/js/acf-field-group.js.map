{"version": 3, "file": "acf-field-group.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA,CAAE,UAAWA,CAAC,EAAEC,SAAS,EAAEC,GAAG,EAAG;EAChC,MAAMC,iBAAiB,GAAG;IACzBC,IAAI,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,CAClB,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,MAAM,EACN,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,aAAa,EACb,cAAc,EACd,UAAU,EACV,kBAAkB,EAClB,OAAO;IAET,CAAC;IAEDC,MAAM,EAAE;MACP,wBAAwB,EAAE,cAAc;MACxC,kCAAkC,EAAE,oBAAoB;MACxD,yBAAyB,EAAE,oBAAoB;MAC/C,uBAAuB,EAAE,kBAAkB;MAC3C,0BAA0B,EAAE,mBAAmB;MAC/C,+BAA+B,EAAE,oBAAoB;MACrD,kCAAkC,EAAE;IACrC,CAAC;IAEDC,KAAK,EAAE,SAAAA,CAAWC,KAAK,EAAG;MACzBV,CAAC,CAACW,MAAM,CAAE,IAAI,CAACP,IAAI,EAAEM,KAAM,CAAC;MAC5B,IAAI,CAACE,GAAG,GAAGZ,CAAC,CAAE,IAAI,CAACa,IAAI,CAAC,CAAE,CAAC;MAC3B,IAAI,CAACC,MAAM,CAAC,CAAC;IACd,CAAC;IAEDC,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,CAACC,gBAAgB,CAAE,IAAK,CAAC;MAC7B,IAAI,CAACL,GAAG,CAACM,IAAI,CAAE,kBAAmB,CAAC,CAACC,KAAK,CAAC,CAAC;MAC3CjB,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAE,IAAI,CAACR,GAAI,CAAC;IACjC,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB,OAAOb,CAAC,CAAE,+BAAgC,CAAC,CAACqB,IAAI,CAAC,CAAC;IACnD,CAAC;IAEDC,aAAa,EAAE,SAAAA,CAAWC,QAAQ,EAAEC,MAAM,EAAG;MAC5C,IAAIC,UAAU;MACd,IAAK,CAAEvB,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,EAAG;QAC5B;QACAD,UAAU,GAAGE,MAAM,CAACC,MAAM,CAAAC,aAAA,CAAAA,aAAA,KACtB3B,GAAG,CAACwB,GAAG,CAAE,YAAa,CAAC,GACvBxB,GAAG,CAACwB,GAAG,CAAE,eAAgB,CAAC,CAC5B,CAAC;MACJ,CAAC,MAAM;QACND,UAAU,GAAGE,MAAM,CAACC,MAAM,CAAE1B,GAAG,CAACwB,GAAG,CAAE,YAAa,CAAE,CAAC;MACtD;MAEA,IAAKH,QAAQ,EAAG;QACf,IAAK,SAAS,KAAKA,QAAQ,EAAG;UAC7B,OAAOE,UAAU,CAACK,MAAM,CAAIC,SAAS,IACpC,IAAI,CAACL,GAAG,CAAE,mBAAoB,CAAC,CAACM,QAAQ,CACvCD,SAAS,CAACE,IACX,CACD,CAAC;QACF;QAEA,IAAK,KAAK,KAAKV,QAAQ,EAAG;UACzB,OAAOE,UAAU,CAACK,MAAM,CAAIC,SAAS,IAAMA,SAAS,CAACG,GAAI,CAAC;QAC3D;QAEAT,UAAU,GAAGA,UAAU,CAACK,MAAM,CAC3BC,SAAS,IAAMA,SAAS,CAACR,QAAQ,KAAKA,QACzC,CAAC;MACF;MAEA,IAAKC,MAAM,EAAG;QACbC,UAAU,GAAGA,UAAU,CAACK,MAAM,CAAIC,SAAS,IAAM;UAChD,MAAMI,KAAK,GAAGJ,SAAS,CAACI,KAAK,CAACC,WAAW,CAAC,CAAC;UAC3C,MAAMC,UAAU,GAAGF,KAAK,CAACG,KAAK,CAAE,GAAI,CAAC;UACrC,IAAIC,KAAK,GAAG,KAAK;UAEjB,IAAKJ,KAAK,CAACK,UAAU,CAAEhB,MAAM,CAACY,WAAW,CAAC,CAAE,CAAC,EAAG;YAC/CG,KAAK,GAAG,IAAI;UACb,CAAC,MAAM,IAAKF,UAAU,CAACI,MAAM,GAAG,CAAC,EAAG;YACnCJ,UAAU,CAACK,OAAO,CAAIC,IAAI,IAAM;cAC/B,IAAKA,IAAI,CAACH,UAAU,CAAEhB,MAAM,CAACY,WAAW,CAAC,CAAE,CAAC,EAAG;gBAC9CG,KAAK,GAAG,IAAI;cACb;YACD,CAAE,CAAC;UACJ;UAEA,OAAOA,KAAK;QACb,CAAE,CAAC;MACJ;MAEA,OAAOd,UAAU;IAClB,CAAC;IAEDX,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnBZ,GAAG,CAACkB,QAAQ,CAAE,QAAQ,EAAE,IAAI,CAACR,GAAI,CAAC;MAElC,MAAMgC,KAAK,GAAG,IAAI,CAAChC,GAAG,CAACM,IAAI,CAAE,sBAAuB,CAAC;MACrD,MAAM2B,IAAI,GAAG,IAAI;MAEjBD,KAAK,CAACE,IAAI,CAAE,YAAY;QACvB,MAAMvB,QAAQ,GAAGvB,CAAC,CAAE,IAAK,CAAC,CAACI,IAAI,CAAE,UAAW,CAAC;QAC7C,MAAMqB,UAAU,GAAGoB,IAAI,CAACvB,aAAa,CAAEC,QAAS,CAAC;QACjDE,UAAU,CAACiB,OAAO,CAAIX,SAAS,IAAM;UACpC/B,CAAC,CAAE,IAAK,CAAC,CAAC+C,MAAM,CAAEF,IAAI,CAACG,gBAAgB,CAAEjB,SAAU,CAAE,CAAC;QACvD,CAAE,CAAC;MACJ,CAAE,CAAC;MAEH,IAAI,CAACkB,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDH,gBAAgB,EAAE,SAAAA,CAAWjB,SAAS,EAAG;MACxC,MAAMqB,QAAQ,GAAGrB,SAAS,CAACE,IAAI,CAACoB,UAAU,CAAE,GAAG,EAAE,GAAI,CAAC;MAEtD,OAAO;AACV,yDAA0DtB,SAAS,CAACE,IAAI;AACxE,MACKF,SAAS,CAACG,GAAG,IAAI,CAAEhC,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,GACnC,uDAAuD,GACvDK,SAAS,CAACG,GAAG,GACb,+CAA+C,GAC/C,EAAE;AACV,gDACiDkB,QAAQ;AACzD,qCAAsCrB,SAAS,CAACI,KAAK;AACrD;AACA,IAAI;IACF,CAAC;IAEDmB,kBAAkB,EAAE,SAAAA,CAAWC,GAAG,EAAG;MACpC,IAAK,OAAOA,GAAG,IAAI,QAAQ,EAAG,OAAOA,GAAG;MACxC,OAAOA,GAAG,CAACF,UAAU,CAAE,QAAQ,EAAE,GAAI,CAAC;IACvC,CAAC;IAEDG,mBAAmB,EAAE,SAAAA,CAAWzB,SAAS,EAAG;MAC3C,MAAM0B,aAAa,GAClB,IAAI,CAACnC,aAAa,CAAC,CAAC,CAACQ,MAAM,CACxB4B,eAAe,IAAMA,eAAe,CAACzB,IAAI,KAAKF,SACjD,CAAC,CAAE,CAAC,CAAE,IAAI,CAAC,CAAC;MAEb,MAAM4B,IAAI,GAAGzD,GAAG,CAAC0D,SAAS,CAAEH,aAAa,EAAE;QAC1CtB,KAAK,EAAE,EAAE;QACT0B,WAAW,EAAE,EAAE;QACfC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE,KAAK;QACnBC,aAAa,EAAE,KAAK;QACpB9B,GAAG,EAAE;MACN,CAAE,CAAC;MAEH,IAAI,CAACtB,GAAG,CAACM,IAAI,CAAE,kBAAmB,CAAC,CAAC+C,IAAI,CAAEN,IAAI,CAACxB,KAAM,CAAC;MACtD,IAAI,CAACvB,GAAG,CAACM,IAAI,CAAE,kBAAmB,CAAC,CAAC+C,IAAI,CAAEN,IAAI,CAACE,WAAY,CAAC;MAE5D,IAAKF,IAAI,CAACG,OAAO,EAAG;QACnB,IAAI,CAAClD,GAAG,CACNM,IAAI,CAAE,iBAAkB,CAAC,CACzBgD,IAAI,CAAE,MAAM,EAAE,IAAI,CAACZ,kBAAkB,CAAEK,IAAI,CAACG,OAAQ,CAAE,CAAC,CACvDK,IAAI,CAAC,CAAC;MACT,CAAC,MAAM;QACN,IAAI,CAACvD,GAAG,CAACM,IAAI,CAAE,iBAAkB,CAAC,CAACkD,IAAI,CAAC,CAAC;MAC1C;MAEA,IAAKT,IAAI,CAACI,YAAY,EAAG;QACxB,IAAI,CAACnD,GAAG,CACNM,IAAI,CAAE,sBAAuB,CAAC,CAC9BgD,IAAI,CACJ,MAAM,EACN,IAAI,CAACZ,kBAAkB,CAAEK,IAAI,CAACI,YAAa,CAC5C,CAAC,CACAM,MAAM,CAAC,CAAC,CACRF,IAAI,CAAC,CAAC;MACT,CAAC,MAAM;QACN,IAAI,CAACvD,GAAG,CAACM,IAAI,CAAE,sBAAuB,CAAC,CAACmD,MAAM,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC;MACxD;MAEA,IAAKT,IAAI,CAACK,aAAa,EAAG;QACzB,IAAI,CAACpD,GAAG,CACNM,IAAI,CAAE,mBAAoB,CAAC,CAC3BgD,IAAI,CAAE,KAAK,EAAEP,IAAI,CAACK,aAAc,CAAC,CACjCG,IAAI,CAAC,CAAC;MACT,CAAC,MAAM;QACN,IAAI,CAACvD,GAAG,CAACM,IAAI,CAAE,mBAAoB,CAAC,CAACkD,IAAI,CAAC,CAAC;MAC5C;MAEA,MAAME,KAAK,GAAGpE,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC;MACjC,MAAM6C,QAAQ,GAAGrE,GAAG,CAACwB,GAAG,CAAE,iBAAkB,CAAC;MAC7C,MAAM8C,kBAAkB,GAAG,IAAI,CAAC5D,GAAG,CAACM,IAAI,CAAE,cAAe,CAAC;MAC1D,MAAMuD,sBAAsB,GAAG,IAAI,CAAC7D,GAAG,CAACM,IAAI,CAC3C,+BACD,CAAC;MAED,IAAKyC,IAAI,CAACzB,GAAG,KAAM,CAAEoC,KAAK,IAAI,CAAEC,QAAQ,CAAE,EAAG;QAC5CC,kBAAkB,CAACL,IAAI,CAAC,CAAC;QACzBK,kBAAkB,CAACN,IAAI,CACtB,MAAM,EACNM,kBAAkB,CAACpE,IAAI,CAAE,SAAU,CAAC,GAAG2B,SACxC,CAAC;QAED0C,sBAAsB,CAACN,IAAI,CAAC,CAAC;QAC7BM,sBAAsB,CAACP,IAAI,CAC1B,MAAM,EACNO,sBAAsB,CAACrE,IAAI,CAAE,SAAU,CAAC,GAAG2B,SAC5C,CAAC;QACD,IAAI,CAACnB,GAAG,CACNM,IAAI,CAAE,yBAA0B,CAAC,CACjCgD,IAAI,CAAE,UAAU,EAAE,IAAK,CAAC;QAC1B,IAAI,CAACtD,GAAG,CAACM,IAAI,CAAE,mBAAoB,CAAC,CAACkD,IAAI,CAAC,CAAC;MAC5C,CAAC,MAAM;QACNI,kBAAkB,CAACJ,IAAI,CAAC,CAAC;QACzBK,sBAAsB,CAACL,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACxD,GAAG,CACNM,IAAI,CAAE,yBAA0B,CAAC,CACjCgD,IAAI,CAAE,UAAU,EAAE,KAAM,CAAC;QAC3B,IAAI,CAACtD,GAAG,CAACM,IAAI,CAAE,mBAAoB,CAAC,CAACiD,IAAI,CAAC,CAAC;MAC5C;IACD,CAAC;IAEDjB,mBAAmB,EAAE,SAAAA,CAAA,EAAY;MAAA,IAAAwB,iBAAA;MAChC,MAAMC,WAAW,GAAG,IAAI,CAACjD,GAAG,CAAE,UAAW,CAAC;MAC1C,MAAMK,SAAS,GAAG4C,WAAW,aAAXA,WAAW,gBAAAD,iBAAA,GAAXC,WAAW,CAAEvE,IAAI,cAAAsE,iBAAA,uBAAjBA,iBAAA,CAAmBE,IAAI;;MAEzC;MACA,IAAK7C,SAAS,EAAG;QAChB,IAAI,CAAC8C,GAAG,CAAE,kBAAkB,EAAE9C,SAAU,CAAC;MAC1C,CAAC,MAAM;QACN,IAAI,CAAC8C,GAAG,CAAE,kBAAkB,EAAE,MAAO,CAAC;MACvC;;MAEA;MACA;MACA;MACA,MAAMpD,UAAU,GAAG,IAAI,CAACH,aAAa,CAAC,CAAC;MACvC,MAAMwD,kBAAkB,GACvB,IAAI,CAACpD,GAAG,CAAE,mBAAoB,CAAC,CAACM,QAAQ,CAAED,SAAU,CAAC;MAEtD,IAAIR,QAAQ,GAAG,EAAE;MACjB,IAAKuD,kBAAkB,EAAG;QACzBvD,QAAQ,GAAG,SAAS;MACrB,CAAC,MAAM;QACN,MAAMwD,iBAAiB,GAAGtD,UAAU,CAACP,IAAI,CAAI8D,CAAC,IAAM;UACnD,OAAOA,CAAC,CAAC/C,IAAI,KAAKF,SAAS;QAC5B,CAAE,CAAC;QAEHR,QAAQ,GAAGwD,iBAAiB,CAACxD,QAAQ;MACtC;MAEA,MAAM0D,iBAAiB,GACtB1D,QAAQ,CAAE,CAAC,CAAE,CAAC2D,WAAW,CAAC,CAAC,GAAG3D,QAAQ,CAAC4D,KAAK,CAAE,CAAE,CAAC;MAClD,MAAMC,gBAAgB,GAAG,gDAAiDH,iBAAiB,IAAK;MAChGI,UAAU,CAAE,MAAM;QACjBrF,CAAC,CAAEoF,gBAAiB,CAAC,CAACE,KAAK,CAAC,CAAC;MAC9B,CAAC,EAAE,CAAE,CAAC;IACP,CAAC;IAEDrC,oBAAoB,EAAE,SAAAA,CAAA,EAAY;MACjC,MAAM0B,WAAW,GAAG,IAAI,CAACjD,GAAG,CAAE,UAAW,CAAC;MAC1C,MAAM6D,SAAS,GAAGZ,WAAW,CAACa,WAAW,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC;MACjD,MAAMD,WAAW,GAAG,IAAI,CAAC5E,GAAG,CAACM,IAAI,CAAE,yBAA0B,CAAC;MAC9D,IAAKqE,SAAS,EAAG;QAChBC,WAAW,CAACC,GAAG,CAAEF,SAAU,CAAC;MAC7B,CAAC,MAAM;QACNC,WAAW,CAACC,GAAG,CAAE,EAAG,CAAC;MACtB;IACD,CAAC;IAEDC,2BAA2B,EAAE,SAAAA,CAAA,EAAY;MACxC,MAAMvD,KAAK,GAAG,IAAI,CAACvB,GAAG,CAACM,IAAI,CAAE,yBAA0B,CAAC,CAACuE,GAAG,CAAC,CAAC;MAC9D,MAAMd,WAAW,GAAG,IAAI,CAACjD,GAAG,CAAE,UAAW,CAAC;MAC1CiD,WAAW,CAACa,WAAW,CAAC,CAAC,CAACC,GAAG,CAAEtD,KAAM,CAAC;MACtCwC,WAAW,CAACa,WAAW,CAAC,CAAC,CAACG,OAAO,CAAE,MAAO,CAAC;IAC5C,CAAC;IAEDxC,iBAAiB,EAAE,SAAAA,CAAA,EAAY;MAC9B,MAAMpB,SAAS,GAAG,IAAI,CAACL,GAAG,CAAE,kBAAmB,CAAC;MAEhD,IAAI,CAACd,GAAG,CAACM,IAAI,CAAE,WAAY,CAAC,CAAC0E,WAAW,CAAE,UAAW,CAAC;MACtD,IAAI,CAAChF,GAAG,CACNM,IAAI,CAAE,mCAAmC,GAAGa,SAAS,GAAG,IAAK,CAAC,CAC9D8D,QAAQ,CAAE,UAAW,CAAC;MAExB,IAAI,CAACrC,mBAAmB,CAAEzB,SAAU,CAAC;IACtC,CAAC;IAED+D,kBAAkB,EAAE,SAAAA,CAAWC,CAAC,EAAG;MAClC,MAAMC,MAAM,GAAG,IAAI,CAACpF,GAAG,CAACM,IAAI,CAAE,0BAA2B,CAAC;MAC1D,MAAM+E,QAAQ,GAAG,IAAI,CAACrF,GAAG,CAACM,IAAI,CAAE,yBAA0B,CAAC,CAACuE,GAAG,CAAC,CAAC;MACjE,MAAM5C,IAAI,GAAG,IAAI;MACjB,IAAIqD,YAAY;QACfC,WAAW,GAAG,EAAE;MACjB,IAAIC,OAAO,GAAG,EAAE;MAEhB,IAAK,QAAQ,KAAK,OAAOH,QAAQ,EAAG;QACnCC,YAAY,GAAGD,QAAQ,CAACI,IAAI,CAAC,CAAC;QAC9BD,OAAO,GAAG,IAAI,CAAC9E,aAAa,CAAE,KAAK,EAAE4E,YAAa,CAAC;MACpD;MAEA,IAAKA,YAAY,CAACzD,MAAM,IAAI2D,OAAO,CAAC3D,MAAM,EAAG;QAC5CuD,MAAM,CAACH,QAAQ,CAAE,cAAe,CAAC;MAClC,CAAC,MAAM;QACNG,MAAM,CAACJ,WAAW,CAAE,cAAe,CAAC;MACrC;MAEA,IAAK,CAAEQ,OAAO,CAAC3D,MAAM,EAAG;QACvBuD,MAAM,CAACH,QAAQ,CAAE,kBAAmB,CAAC;QACrC,IAAI,CAACjF,GAAG,CACNM,IAAI,CAAE,0BAA2B,CAAC,CAClC+C,IAAI,CAAEiC,YAAa,CAAC;QACtB;MACD,CAAC,MAAM;QACNF,MAAM,CAACJ,WAAW,CAAE,kBAAmB,CAAC;MACzC;MAEAQ,OAAO,CAAC1D,OAAO,CAAIX,SAAS,IAAM;QACjCoE,WAAW,GAAGA,WAAW,GAAGtD,IAAI,CAACG,gBAAgB,CAAEjB,SAAU,CAAC;MAC/D,CAAE,CAAC;MAEH/B,CAAC,CAAE,gCAAiC,CAAC,CAACqB,IAAI,CAAE8E,WAAY,CAAC;MAEzD,IAAI,CAACtB,GAAG,CAAE,kBAAkB,EAAEuB,OAAO,CAAE,CAAC,CAAE,CAACnE,IAAK,CAAC;MACjD,IAAI,CAACkB,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDmD,oBAAoB,EAAE,SAAAA,CAAA,EAAY;MACjC,IAAI,CAAC1F,GAAG,CACNM,IAAI,CAAE,yBAA0B,CAAC,CACjCuE,GAAG,CAAE,EAAG,CAAC,CACTE,OAAO,CAAE,OAAQ,CAAC;MACpB,IAAI,CAAC/E,GAAG,CAACM,IAAI,CAAE,iBAAkB,CAAC,CAACqF,KAAK,CAAC,CAAC,CAACZ,OAAO,CAAE,OAAQ,CAAC;IAC9D,CAAC;IAEDa,kBAAkB,EAAE,SAAAA,CAAWT,CAAC,EAAG;MAClC,MAAMpB,WAAW,GAAG,IAAI,CAACjD,GAAG,CAAE,UAAW,CAAC;MAE1CiD,WAAW,CACT8B,gBAAgB,CAAC,CAAC,CAClBhB,GAAG,CAAE,IAAI,CAAC/D,GAAG,CAAE,kBAAmB,CAAE,CAAC;MACvCiD,WAAW,CAAC8B,gBAAgB,CAAC,CAAC,CAACd,OAAO,CAAE,QAAS,CAAC;MAElD,IAAI,CAACD,2BAA2B,CAAC,CAAC;MAElC,IAAI,CAACgB,KAAK,CAAC,CAAC;IACb,CAAC;IAEDC,gBAAgB,EAAE,SAAAA,CAAWZ,CAAC,EAAG;MAChC,MAAMa,UAAU,GAAG5G,CAAC,CAAE+F,CAAC,CAACc,aAAc,CAAC;MACvC,IAAI,CAAChC,GAAG,CAAE,kBAAkB,EAAE+B,UAAU,CAACxG,IAAI,CAAE,YAAa,CAAE,CAAC;IAChE,CAAC;IAED0G,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAI,CAACJ,KAAK,CAAC,CAAC;IACb,CAAC;IAEDK,kBAAkB,EAAE,SAAAA,CAAWhB,CAAC,EAAG;MAClC,IAAKA,CAAC,CAACiB,GAAG,KAAK,QAAQ,EAAG;QACzB,IAAI,CAACN,KAAK,CAAC,CAAC;MACb;IACD,CAAC;IAEDA,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,IAAI,CAACzF,gBAAgB,CAAE,KAAM,CAAC;MAC9B,IAAI,CAACgG,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,MAAM,CAAC,CAAC;IACd,CAAC;IAED/F,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,IAAI,CAACP,GAAG,CAACM,IAAI,CAAE,QAAS,CAAC,CAACqF,KAAK,CAAC,CAAC,CAACZ,OAAO,CAAE,OAAQ,CAAC;IACrD;EACD,CAAC;EAEDzF,GAAG,CAACiH,MAAM,CAAChH,iBAAiB,GAAGD,GAAG,CAACiH,MAAM,CAACC,KAAK,CAACzG,MAAM,CAAER,iBAAkB,CAAC;EAC3ED,GAAG,CAACmH,oBAAoB,GAAK3G,KAAK,IACjC,IAAIR,GAAG,CAACiH,MAAM,CAAChH,iBAAiB,CAAEO,KAAM,CAAC;AAC3C,CAAC,EAAI4G,MAAM,CAACC,MAAM,EAAEtH,SAAS,EAAEqH,MAAM,CAACpH,GAAI,CAAC;;;;;;;;;;ACpY3C,CAAE,UAAWF,CAAC,EAAEC,SAAS,EAAG;EAC3B,IAAIuH,IAAI,GAAGtH,GAAG,CAACuH,gBAAgB,CAAEvH,GAAI,CAAC;;EAEtC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECsH,IAAI,CAACE,WAAW,GAAG;IAClBC,UAAU,EAAE,SAAAA,CAAWC,MAAM,EAAEhD,IAAI,EAAG;MACrCA,IAAI,GAAGA,IAAI,KAAK3E,SAAS,GAAG2E,IAAI,GAAG,UAAU;MAC7C1E,GAAG,CAAC2H,cAAc,CAAED,MAAO,CAAC,CAACE,IAAI,CAAElD,IAAK,CAAC;IAC1C,CAAC;IAEDmD,YAAY,EAAE,SAAAA,CAAWH,MAAM,EAAEI,OAAO,EAAG;MAC1CA,OAAO,GAAGA,OAAO,KAAK/H,SAAS,GAAG+H,OAAO,GAAG,IAAI;MAChD9H,GAAG,CAAC2H,cAAc,CAAED,MAAO,CAAC,CAACK,MAAM,CAAE;QACpCD,OAAO,EAAEA;MACV,CAAE,CAAC;IACJ,CAAC;IAEDE,iBAAiB,EAAE,SAAAA,CAAWN,MAAM,EAAE3F,IAAI,EAAEkG,KAAK,EAAG;MACnDjI,GAAG,CAAC2H,cAAc,CAAED,MAAO,CAAC,CAACQ,IAAI,CAAEnG,IAAI,EAAEkG,KAAM,CAAC;IACjD,CAAC;IAEDE,iBAAiB,EAAE,SAAAA,CAAWT,MAAM,EAAE3F,IAAI,EAAG;MAC5C/B,GAAG,CAAC2H,cAAc,CAAED,MAAO,CAAC,CAACQ,IAAI,CAAEnG,IAAI,EAAE,IAAK,CAAC;IAChD;EACD,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECuF,IAAI,CAACE,WAAW,CAACY,YAAY,GAAGpI,GAAG,CAACqI,KAAK,CAAC5H,MAAM,CAAE;IACjD;IACAiE,IAAI,EAAE,EAAE;IACR4D,CAAC,EAAE,CAAC,CAAC;IACLZ,MAAM,EAAE,IAAI;IACZa,SAAS,EAAE,IAAI;IAEfC,GAAG,EAAE,SAAAA,CAAWA,GAAG,EAAG;MACrB;MACA,IAAI9D,IAAI,GAAG,IAAI,CAACA,IAAI;;MAEpB;MACA;MACA;MACA,IAAI+D,IAAI,GAAGD,GAAG,CAACpG,KAAK,CAAE,GAAI,CAAC;MAC3BqG,IAAI,CAACC,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE,OAAQ,CAAC;MAC5BF,GAAG,GAAGC,IAAI,CAACE,IAAI,CAAE,GAAI,CAAC;;MAEtB;MACA,IAAKjE,IAAI,EAAG;QACX8D,GAAG,IAAI,QAAQ,GAAG9D,IAAI;MACvB;;MAEA;MACA,OAAO8D,GAAG;IACX,CAAC;IAEDI,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIA,QAAQ,GAAG,mBAAmB;MAClC,IAAIlE,IAAI,GAAG,IAAI,CAACA,IAAI;;MAEpB;MACA,IAAKA,IAAI,EAAG;QACXkE,QAAQ,IAAI,GAAG,GAAGlE,IAAI;QACtBkE,QAAQ,GAAG5I,GAAG,CAAC6I,WAAW,CAAE,GAAG,EAAE,GAAG,EAAED,QAAS,CAAC;MACjD;;MAEA;MACA,OAAOA,QAAQ;IAChB,CAAC;IAEDE,WAAW,EAAE,SAAAA,CAAW/G,IAAI,EAAEgH,QAAQ,EAAG;MACxC;MACA,IAAIV,KAAK,GAAG,IAAI;;MAEhB;MACArI,GAAG,CAACgJ,UAAU,CAAE,IAAI,CAACR,GAAG,CAAEzG,IAAK,CAAC,EAAE,UAAW2F,MAAM,EAAG;QACrD;QACAW,KAAK,CAAC1D,GAAG,CAAE,QAAQ,EAAE+C,MAAO,CAAC;;QAE7B;QACAW,KAAK,CAAEU,QAAQ,CAAE,CAACE,KAAK,CAAEZ,KAAK,EAAEa,SAAU,CAAC;MAC5C,CAAE,CAAC;IACJ,CAAC;IAEDC,WAAW,EAAE,SAAAA,CAAWpH,IAAI,EAAEgH,QAAQ,EAAG;MACxC;MACA,IAAIV,KAAK,GAAG,IAAI;;MAEhB;MACArI,GAAG,CAACoJ,UAAU,CAAE,IAAI,CAACZ,GAAG,CAAEzG,IAAK,CAAC,EAAE,UAAW2F,MAAM,EAAG;QACrD;QACAW,KAAK,CAAC1D,GAAG,CAAE,QAAQ,EAAE+C,MAAO,CAAC;;QAE7B;QACAW,KAAK,CAAEU,QAAQ,CAAE,CAACE,KAAK,CAAEZ,KAAK,EAAEa,SAAU,CAAC;MAC5C,CAAE,CAAC;IACJ,CAAC;IAEDG,UAAU,EAAE,SAAAA,CAAWtH,IAAI,EAAEgH,QAAQ,EAAG;MACvC;MACA,IAAIV,KAAK,GAAG,IAAI;MAChB,IAAIiB,KAAK,GAAGvH,IAAI,CAACwH,MAAM,CAAE,CAAC,EAAExH,IAAI,CAACyH,OAAO,CAAE,GAAI,CAAE,CAAC;MACjD,IAAIZ,QAAQ,GAAG7G,IAAI,CAACwH,MAAM,CAAExH,IAAI,CAACyH,OAAO,CAAE,GAAI,CAAC,GAAG,CAAE,CAAC;MACrD,IAAIC,OAAO,GAAG,IAAI,CAACb,QAAQ,CAAC,CAAC;;MAE7B;MACA9I,CAAC,CAAE4J,QAAS,CAAC,CAACC,EAAE,CAAEL,KAAK,EAAEG,OAAO,GAAG,GAAG,GAAGb,QAAQ,EAAE,UAAW/C,CAAC,EAAG;QACjE;QACAA,CAAC,CAACnF,GAAG,GAAGZ,CAAC,CAAE,IAAK,CAAC;QACjB+F,CAAC,CAAC6B,MAAM,GAAG7B,CAAC,CAACnF,GAAG,CAACkJ,OAAO,CAAE,mBAAoB,CAAC;;QAE/C;QACAvB,KAAK,CAAC1D,GAAG,CAAE,QAAQ,EAAEkB,CAAC,CAAC6B,MAAO,CAAC;;QAE/B;QACAW,KAAK,CAAEU,QAAQ,CAAE,CAACE,KAAK,CAAEZ,KAAK,EAAE,CAAExC,CAAC,CAAG,CAAC;MACxC,CAAE,CAAC;IACJ,CAAC;IAEDgE,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAI,CAACvB,CAAC,GAAG,IAAI,CAACZ,MAAM,CAACxH,IAAI,CAAC,CAAC;;MAE3B;MACA,IAAI,CAACqI,SAAS,GAAG,IAAI,CAACb,MAAM,CAAC1G,IAAI,CAAE,6BAA8B,CAAC;;MAElE;MACA,IAAI,CAACC,KAAK,CAAC,CAAC;IACb,CAAC;IAEDA,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB;IAAA,CACA;IAED6I,OAAO,EAAE,SAAAA,CAAW/H,IAAI,EAAG;MAC1B,OAAO,IAAI,CAACwG,SAAS,CAACvH,IAAI,CAAE,uBAAuB,GAAGe,IAAK,CAAC;IAC7D;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIgI,aAAa,GAAG,IAAI/J,GAAG,CAACgK,KAAK,CAAE;IAClCC,OAAO,EAAE;MACRC,iBAAiB,EAAE,mBAAmB;MACtCC,kBAAkB,EAAE,oBAAoB;MACxCC,gBAAgB,EAAE,kBAAkB;MACpCC,sBAAsB,EAAE,wBAAwB;MAChDC,mBAAmB,EAAE,qBAAqB;MAC1CC,wBAAwB,EAAE,yBAAyB;MACnDC,yBAAyB,EAAE,0BAA0B;MACrDC,wBAAwB,EAAE,yBAAyB;MACnDC,0BAA0B,EAAE,2BAA2B;MACvDC,qBAAqB,EAAE;IACxB,CAAC;IAEDC,iBAAiB,EAAE,SAAAA,CAAWC,KAAK,EAAG;MACrC7K,GAAG,CAACkB,QAAQ,CAAE,YAAY,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MACvCV,GAAG,CAACkB,QAAQ,CAAE,kBAAkB,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EAAEqJ,KAAK,CAACnK,GAAI,CAAC;MAEnEV,GAAG,CAACkB,QAAQ,CAAE,uBAAuB,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MAClDV,GAAG,CAACkB,QAAQ,CACX,6BAA6B,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EACnDqJ,KAAK,CAACnK,GACP,CAAC;IACF,CAAC;IAEDoK,kBAAkB,EAAE,SAAAA,CAAWD,KAAK,EAAG;MACtC7K,GAAG,CAACkB,QAAQ,CAAE,aAAa,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MACxCV,GAAG,CAACkB,QAAQ,CACX,mBAAmB,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EACzCqJ,KAAK,CAACnK,GACP,CAAC;IACF,CAAC;IAEDqK,gBAAgB,EAAE,SAAAA,CAAWF,KAAK,EAAG;MACpC7K,GAAG,CAACkB,QAAQ,CAAE,WAAW,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MACtCV,GAAG,CAACkB,QAAQ,CAAE,iBAAiB,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EAAEqJ,KAAK,CAACnK,GAAI,CAAC;IACnE,CAAC;IAEDsK,sBAAsB,EAAE,SAAAA,CAAWH,KAAK,EAAG;MAC1C7K,GAAG,CAACkB,QAAQ,CAAE,iBAAiB,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MAC5CV,GAAG,CAACkB,QAAQ,CACX,uBAAuB,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EAC7CqJ,KAAK,CAACnK,GACP,CAAC;IACF,CAAC;IAEDuK,mBAAmB,EAAE,SAAAA,CAAWJ,KAAK,EAAG;MACvC7K,GAAG,CAACkB,QAAQ,CAAE,cAAc,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MACzCV,GAAG,CAACkB,QAAQ,CACX,oBAAoB,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EAC1CqJ,KAAK,CAACnK,GACP,CAAC;IACF,CAAC;IAEDwK,uBAAuB,EAAE,SAAAA,CAAWL,KAAK,EAAG;MAC3C7K,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MAC9CV,GAAG,CAACkB,QAAQ,CACX,yBAAyB,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EAC/CqJ,KAAK,CAACnK,GACP,CAAC;MAEDV,GAAG,CAACkB,QAAQ,CAAE,uBAAuB,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MAClDV,GAAG,CAACkB,QAAQ,CACX,6BAA6B,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EACnDqJ,KAAK,CAACnK,GACP,CAAC;IACF,CAAC;IAEDyK,wBAAwB,EAAE,SAAAA,CAAWN,KAAK,EAAG;MAC5C7K,GAAG,CAACkB,QAAQ,CAAE,oBAAoB,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MAC/CV,GAAG,CAACkB,QAAQ,CACX,0BAA0B,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EAChDqJ,KAAK,CAACnK,GACP,CAAC;IACF,CAAC;IAED0K,uBAAuB,EAAE,SAAAA,CAAWP,KAAK,EAAG;MAC3C7K,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE2J,KAAK,CAACnK,GAAI,CAAC;MAC9CV,GAAG,CAACkB,QAAQ,CACX,yBAAyB,GAAG2J,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,EAC/CqJ,KAAK,CAACnK,GACP,CAAC;IACF,CAAC;IAED2K,yBAAyB,EAAE,SAAAA,CAAWR,KAAK,EAAG;MAC7C7K,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE2J,KAAK,CAACnK,GAAI,CAAC;IACjD;EACD,CAAE,CAAC;AACJ,CAAC,EAAI2G,MAAO,CAAC;;;;;;;;;;ACrQb,CAAE,UAAWvH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIuL,4BAA4B,GAAGtL,GAAG,CAACuL,YAAY,CAAC9K,MAAM,CAAE;IAC3DiE,IAAI,EAAE,EAAE;IACR3C,IAAI,EAAE,mBAAmB;IACzBzB,MAAM,EAAE;MACP,2BAA2B,EAAE,gBAAgB;MAC7C,8BAA8B,EAAE,iBAAiB;MACjD,6BAA6B,EAAE,cAAc;MAC7C,8BAA8B,EAAE,eAAe;MAC/C,iCAAiC,EAAE,kBAAkB;MACrD,6BAA6B,EAAE,YAAY;MAC3C,gCAAgC,EAAE;IACnC,CAAC;IAEDkL,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,SAAAA,CAAWD,KAAK,EAAG;MACzB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,OAAO,IAAI;IACZ,CAAC;IAEDE,QAAQ,EAAE,SAAAA,CAAW3J,IAAI,EAAEkG,KAAK,EAAG;MAClC,OAAO,IAAI,CAACuD,KAAK,CAACtL,IAAI,CAAC+I,KAAK,CAAE,IAAI,CAACuC,KAAK,EAAEtC,SAAU,CAAC;IACtD,CAAC;IAEDyC,MAAM,EAAE,SAAAA,CAAW5J,IAAI,EAAG;MACzB,OAAO,IAAI,CAACyJ,KAAK,CAACxK,IAAI,CAAE,kBAAkB,GAAGe,IAAK,CAAC;IACpD,CAAC;IAED6J,GAAG,EAAE,SAAAA,CAAW7J,IAAI,EAAG;MACtB,OAAO,IAAI,CAACyJ,KAAK,CAACxK,IAAI,CAAE,KAAK,GAAGe,IAAK,CAAC;IACvC,CAAC;IAED8J,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAC/L,CAAC,CAAE,oBAAqB,CAAC;IACtC,CAAC;IAEDgM,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAAChM,CAAC,CAAE,cAAe,CAAC;IAChC,CAAC;IAEDiM,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACjM,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDkM,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAAClM,CAAC,CAAE,OAAQ,CAAC;IACzB,CAAC;IAEDmM,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAO,IAAI,CAACxH,WAAW,CAAC/D,GAAG,CAACM,IAAI,CAAC,0BAA0B,CAAC;IAC7D,CAAC;IAEDkL,uBAAuB,EAAE,SAAAA,CAAA,EAAY;MACpC,OAAO,IAAI,CAACpM,CAAC,CAAE,uBAAwB,CAAC;IACzC,CAAC;IAEDgB,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB,IAAIqL,IAAI,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;MAC1BK,IAAI,CAAClI,IAAI,CAAC,CAAC;MACXjE,GAAG,CAACoM,MAAM,CAAED,IAAK,CAAC;IACnB,CAAC;IAED3F,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,IAAI2F,IAAI,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;MAC1BK,IAAI,CAACjI,IAAI,CAAC,CAAC;MACXlE,GAAG,CAACqM,OAAO,CAAEF,IAAK,CAAC;IACpB,CAAC;IAEDvL,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAK,IAAI,CAACiL,OAAO,CAAC,CAAC,CAAC3D,IAAI,CAAE,SAAU,CAAC,EAAG;QACvC,IAAI,CAAC+D,SAAS,CAAC,CAAC,CAACtG,QAAQ,CAAC,YAAY,CAAC;QACvC,IAAI,CAAC2G,WAAW,CAAC,CAAC;QAClB,IAAI,CAACxL,IAAI,CAAC,CAAC;;QAEX;MACD,CAAC,MAAM;QACN,IAAI,CAACmL,SAAS,CAAC,CAAC,CAACvG,WAAW,CAAC,YAAY,CAAC;QAC1C,IAAI,CAACc,KAAK,CAAC,CAAC;MACb;IACD,CAAC;IAED8F,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAI3J,IAAI,GAAG,IAAI;;MAEf;MACA,IAAI,CAACqJ,MAAM,CAAC,CAAC,CAACpJ,IAAI,CAAE,YAAY;QAC/BD,IAAI,CAAC4J,UAAU,CAAEzM,CAAC,CAAE,IAAK,CAAE,CAAC;MAC7B,CAAE,CAAC;IACJ,CAAC;IAEDyM,UAAU,EAAE,SAAAA,CAAWf,KAAK,EAAG;MAC9B,IAAI,CAACC,KAAK,CAAED,KAAM,CAAC;MACnB,IAAI,CAACgB,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,WAAW,CAAC,CAAC;IACnB,CAAC;IAEDF,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAIG,OAAO,GAAG,EAAE;MAChB,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIC,GAAG,GAAG,IAAI,CAACpI,WAAW,CAACoI,GAAG;MAC9B,IAAIC,OAAO,GAAG,IAAI,CAACnB,MAAM,CAAE,OAAQ,CAAC;;MAEpC;MACA3L,GAAG,CAAC+M,eAAe,CAAC,CAAC,CAACC,GAAG,CAAE,UAAWvI,WAAW,EAAG;QACnD;QACA,IAAIwI,MAAM,GAAG;UACZC,EAAE,EAAEzI,WAAW,CAAC0I,MAAM,CAAC,CAAC;UACxBpJ,IAAI,EAAEU,WAAW,CAAC2I,QAAQ,CAAC;QAC5B,CAAC;;QAED;QACA,IAAK3I,WAAW,CAACoI,GAAG,KAAKA,GAAG,EAAG;UAC9BI,MAAM,CAAClJ,IAAI,IAAI,GAAG,GAAG/D,GAAG,CAACqN,EAAE,CAAE,cAAe,CAAC;UAC7CJ,MAAM,CAACK,QAAQ,GAAG,IAAI;QACvB;;QAEA;QACA,IAAIC,cAAc,GAAGvN,GAAG,CAACwN,iBAAiB,CAAE;UAC3C3L,SAAS,EAAE4C,WAAW,CAACgJ,OAAO,CAAC;QAChC,CAAE,CAAC;;QAEH;QACA,IAAK,CAAEF,cAAc,CAAChL,MAAM,EAAG;UAC9B0K,MAAM,CAACK,QAAQ,GAAG,IAAI;QACvB;;QAEA;QACA,IAAII,OAAO,GAAGjJ,WAAW,CAACkJ,UAAU,CAAC,CAAC,CAACpL,MAAM;QAC7C0K,MAAM,CAAClJ,IAAI,GAAG,IAAI,CAAC6J,MAAM,CAAEF,OAAQ,CAAC,GAAGT,MAAM,CAAClJ,IAAI;;QAElD;QACA4I,OAAO,CAACkB,IAAI,CAAEZ,MAAO,CAAC;MACvB,CAAE,CAAC;;MAEH;MACA,IAAK,CAAEN,OAAO,CAACpK,MAAM,EAAG;QACvBoK,OAAO,CAACkB,IAAI,CAAE;UACbX,EAAE,EAAE,EAAE;UACNnJ,IAAI,EAAE/D,GAAG,CAACqN,EAAE,CAAE,4BAA6B;QAC5C,CAAE,CAAC;MACJ;;MAEA;MACArN,GAAG,CAAC8N,YAAY,CAAEhB,OAAO,EAAEH,OAAQ,CAAC;;MAEpC;MACA,IAAI,CAACjB,QAAQ,CAAE,OAAO,EAAEoB,OAAO,CAACvH,GAAG,CAAC,CAAE,CAAC;IACxC,CAAC;IAEDkH,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B;MACA,IAAK,CAAE,IAAI,CAACf,QAAQ,CAAE,OAAQ,CAAC,EAAG;QACjC;MACD;;MAEA;MACA,IAAIoB,OAAO,GAAG,IAAI,CAACnB,MAAM,CAAE,UAAW,CAAC;MACvC,IAAIpG,GAAG,GAAGuH,OAAO,CAACvH,GAAG,CAAC,CAAC;MACvB,IAAIoH,OAAO,GAAG,EAAE;;MAEhB;MACA;MACA,IAAKG,OAAO,CAACvH,GAAG,CAAC,CAAC,KAAK,IAAI,EAAG;QAC7BvF,GAAG,CAAC8N,YAAY,CAAEhB,OAAO,EAAE,CAC1B;UACCI,EAAE,EAAE,IAAI,CAACxB,QAAQ,CAAE,UAAW,CAAC;UAC/B3H,IAAI,EAAE;QACP,CAAC,CACA,CAAC;MACJ;;MAEA;MACA,IAAI2D,MAAM,GAAG1H,GAAG,CAAC+N,eAAe,CAAE,IAAI,CAACrC,QAAQ,CAAE,OAAQ,CAAE,CAAC;MAC5D,IAAIb,KAAK,GAAG7K,GAAG,CAAC2H,cAAc,CAAED,MAAO,CAAC;;MAExC;MACA,IAAI6F,cAAc,GAAGvN,GAAG,CAACwN,iBAAiB,CAAE;QAC3C3L,SAAS,EAAEgJ,KAAK,CAAC4C,OAAO,CAAC;MAC1B,CAAE,CAAC;;MAEH;MACAF,cAAc,CAACP,GAAG,CAAE,UAAW3E,KAAK,EAAG;QACtCsE,OAAO,CAACkB,IAAI,CAAE;UACbX,EAAE,EAAE7E,KAAK,CAAC2F,SAAS,CAACC,QAAQ;UAC5BlK,IAAI,EAAEsE,KAAK,CAAC2F,SAAS,CAAC/L;QACvB,CAAE,CAAC;MACJ,CAAE,CAAC;;MAEH;MACAjC,GAAG,CAAC8N,YAAY,CAAEhB,OAAO,EAAEH,OAAQ,CAAC;;MAEpC;MACA,IAAI,CAACjB,QAAQ,CAAE,UAAU,EAAEoB,OAAO,CAACvH,GAAG,CAAC,CAAE,CAAC;IAC3C,CAAC;IAEDmH,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB;MACA,IAAK,CAAE,IAAI,CAAChB,QAAQ,CAAE,OAAQ,CAAC,IAAI,CAAE,IAAI,CAACA,QAAQ,CAAE,UAAW,CAAC,EAAG;QAClE;MACD;MAEA,IAAIoB,OAAO,GAAG,IAAI,CAACnB,MAAM,CAAE,OAAQ,CAAC;MACpC,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAE,OAAQ,CAAC;MAC7B,IAAIsC,UAAU,GAAGpB,OAAO,CAACvH,GAAG,CAAC,CAAC;MAC9B,IAAI4I,UAAU,GAAG,IAAI,CAAC3C,KAAK,CAAC,CAAC,CAAC,CAAC4C,YAAY,CAAE,YAAa,CAAC;;MAE3D;MACA,IAAI1G,MAAM,GAAG1H,GAAG,CAAC+N,eAAe,CAAE,IAAI,CAACrC,QAAQ,CAAE,OAAQ,CAAE,CAAC;MAC5D,IAAIb,KAAK,GAAG7K,GAAG,CAAC2H,cAAc,CAAED,MAAO,CAAC;MACxC;MACA,IAAI6F,cAAc,GAAGvN,GAAG,CAACwN,iBAAiB,CAAE;QAC3C3L,SAAS,EAAEgJ,KAAK,CAAC4C,OAAO,CAAC,CAAC;QAC1BQ,QAAQ,EAAE,IAAI,CAACvC,QAAQ,CAAE,UAAW;MACrC,CAAE,CAAC;MAEH,IAAI2C,aAAa,GAAGd,cAAc,CAAE,CAAC,CAAE,CAACS,SAAS;MACjD,IAAIrB,OAAO,GAAG0B,aAAa,CAAC1B,OAAO,CAAE9B,KAAM,CAAC;MAC5C,IAAIyD,UAAU;MACd,IAAK3B,OAAO,YAAYtF,MAAM,IAAI,CAAC,CAAEsF,OAAO,CAACzM,IAAI,CAAE,iBAAkB,CAAC,EAAG;QACxEoO,UAAU,GAAGxB,OAAO,CAACyB,KAAK,CAAC,CAAC;QAC5B;QACA,IAAKD,UAAU,CAACE,EAAE,CAAE,OAAQ,CAAC,EAAG;UAC/B,IAAIC,OAAO,GAAG3B,OAAO,CAAC9I,IAAI,CAAE,OAAQ,CAAC;UACrC,MAAM0K,cAAc,GAAG5O,CAAC,CAAE,mBAAoB,CAAC,CAAC6F,QAAQ,CAAE8I,OAAQ,CAAC,CAAClJ,GAAG,CAAE4I,UAAW,CAAC;UACrFG,UAAU,GAAGI,cAAc;QAC5B;QAEA1O,GAAG,CAAC2O,SAAS,CAAE,gCAAgC,EAAE,YAAW;UAC3D3O,GAAG,CAAC4O,UAAU,CAAEN,UAAU,EAAE3B,OAAO,CAACzM,IAAI,CAAE,iBAAkB,CAAE,CAAC;QAChE,CAAC,CAAC;MACH,CAAC,MAAM,IAAKyM,OAAO,YAAYkC,KAAK,EAAG;QACtC,IAAI,CAAC3C,uBAAuB,CAAC,CAAC,CAACxG,WAAW,CAAE,2BAA4B,CAAC;QACzE4I,UAAU,GAAGxO,CAAC,CAAE,mBAAoB,CAAC;QACrCE,GAAG,CAAC8N,YAAY,CAAEQ,UAAU,EAAE3B,OAAQ,CAAC;MACxC,CAAC,MAAM;QACN,IAAI,CAACT,uBAAuB,CAAC,CAAC,CAACxG,WAAW,CAAE,2BAA4B,CAAC;QACzE4I,UAAU,GAAGxO,CAAC,CAAE6M,OAAQ,CAAC;MAC1B;;MAEA;MACAG,OAAO,CAACgC,MAAM,CAAC,CAAC;MAChBlD,GAAG,CAACzK,IAAI,CAAEmN,UAAW,CAAC;;MAEtB;MACAnJ,UAAU,CAAE,YAAY;QACvB,CAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAE,CAAC6H,GAAG,CAAE,UAAWhJ,IAAI,EAAG;UAChDsK,UAAU,CAACtK,IAAI,CAAEA,IAAI,EAAE8I,OAAO,CAAC9I,IAAI,CAAEA,IAAK,CAAE,CAAC;QAC9C,CAAE,CAAC;QACH8I,OAAO,CAACvH,GAAG,CAAE4I,UAAW,CAAC;QACzBnO,GAAG,CAACkB,QAAQ,CAAE,gCAAiC,CAAC;MACjD,CAAC,EAAE,CAAE,CAAC;MACN;MACA,IAAK,CAAEoN,UAAU,CAACpG,IAAI,CAAE,UAAW,CAAC,EAAG;QACtClI,GAAG,CAACuF,GAAG,CAAE+I,UAAU,EAAEJ,UAAU,EAAE,IAAK,CAAC;MACxC;;MAEA;MACA,IAAI,CAACxC,QAAQ,CAAE,OAAO,EAAE4C,UAAU,CAAC/I,GAAG,CAAC,CAAE,CAAC;IAC3C,CAAC;IAEDwJ,cAAc,EAAE,SAAAA,CAAA,EAAY;MAC3B,IAAI,CAACnO,MAAM,CAAC,CAAC;IACd,CAAC;IAEDoO,eAAe,EAAE,SAAAA,CAAWnJ,CAAC,EAAEnF,GAAG,EAAG;MACpC,IAAI,CAACuO,QAAQ,CAAC,CAAC;IAChB,CAAC;IAEDA,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIC,MAAM,GAAG,IAAI,CAACpP,CAAC,CAAE,kBAAmB,CAAC;;MAEzC;MACA,IAAIqP,OAAO,GAAGnP,GAAG,CAACoP,SAAS,CAAEF,MAAO,CAAC;;MAErC;MACAC,OAAO,CAACnO,IAAI,CAAE,IAAK,CAAC,CAAC+C,IAAI,CAAE/D,GAAG,CAACqN,EAAE,CAAE,IAAK,CAAE,CAAC;;MAE3C;MACA8B,OAAO,CAACnO,IAAI,CAAE,IAAK,CAAC,CAACqO,GAAG,CAAE,QAAS,CAAC,CAACrI,MAAM,CAAC,CAAC;;MAE7C;MACA,IAAIsI,GAAG,GAAGH,OAAO,CAACnO,IAAI,CAAE,IAAK,CAAC;MAC9B,IAAI,CAACuL,UAAU,CAAE+C,GAAI,CAAC;;MAEtB;MACA,IAAI,CAAC7K,WAAW,CAACmD,IAAI,CAAC,CAAC;IACxB,CAAC;IAED2H,YAAY,EAAE,SAAAA,CAAW1J,CAAC,EAAEnF,GAAG,EAAG;MACjC,IAAI,CAAC8L,WAAW,CAAC,CAAC;IACnB,CAAC;IAEDgD,aAAa,EAAE,SAAAA,CAAW3J,CAAC,EAAEnF,GAAG,EAAG;MAClC;MACA,IAAI,CAAC+K,KAAK,CAAE/K,GAAG,CAACkJ,OAAO,CAAE,OAAQ,CAAE,CAAC;;MAEpC;MACA,IAAI,CAAC8B,QAAQ,CAAE,OAAO,EAAEhL,GAAG,CAAC6E,GAAG,CAAC,CAAE,CAAC;;MAEnC;MACA,IAAI,CAACkH,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,WAAW,CAAC,CAAC;IACnB,CAAC;IAED+C,gBAAgB,EAAE,SAAAA,CAAW5J,CAAC,EAAEnF,GAAG,EAAG;MACrC;MACA,IAAI,CAAC+K,KAAK,CAAE/K,GAAG,CAACkJ,OAAO,CAAE,OAAQ,CAAE,CAAC;;MAEpC;MACA,IAAI,CAAC8B,QAAQ,CAAE,UAAU,EAAEhL,GAAG,CAAC6E,GAAG,CAAC,CAAE,CAAC;;MAEtC;MACA,IAAI,CAACmH,WAAW,CAAC,CAAC;IACnB,CAAC;IAEDgD,UAAU,EAAE,SAAAA,CAAW7J,CAAC,EAAEnF,GAAG,EAAG;MAC/B;MACA,IAAI8K,KAAK,GAAGxL,GAAG,CAACoP,SAAS,CAAE1O,GAAG,CAACkJ,OAAO,CAAE,OAAQ,CAAE,CAAC;;MAEnD;MACA,IAAI,CAAC2C,UAAU,CAAEf,KAAM,CAAC;IACzB,CAAC;IAEDmE,aAAa,EAAE,SAAAA,CAAW9J,CAAC,EAAEnF,GAAG,EAAG;MAClC;MACA,IAAI8K,KAAK,GAAG9K,GAAG,CAACkJ,OAAO,CAAE,OAAQ,CAAC;;MAElC;MACA,IAAI,CAACnF,WAAW,CAACmD,IAAI,CAAC,CAAC;;MAEvB;MACA,IAAK4D,KAAK,CAACoE,QAAQ,CAAE,OAAQ,CAAC,CAACrN,MAAM,IAAI,CAAC,EAAG;QAC5CiJ,KAAK,CAAC5B,OAAO,CAAE,aAAc,CAAC,CAAC5C,MAAM,CAAC,CAAC;MACxC;;MAEA;MACAwE,KAAK,CAACxE,MAAM,CAAC,CAAC;IACf;EACD,CAAE,CAAC;EAEHhH,GAAG,CAAC6P,oBAAoB,CAAEvE,4BAA6B,CAAC;;EAExD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIwE,sBAAsB,GAAG,IAAI9P,GAAG,CAACgK,KAAK,CAAE;IAC3CC,OAAO,EAAE;MACR8F,uBAAuB,EAAE;IAC1B,CAAC;IAEDC,uBAAuB,EAAE,SAAAA,CAAWC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAG;MACnE;MACA,IAAIjQ,IAAI,GAAG,CAAC,CAAC;MACb,IAAIkQ,QAAQ,GAAGtQ,CAAC,CAAC,CAAC;;MAElB;MACAmQ,QAAQ,CAACjD,GAAG,CAAE,UAAWqD,KAAK,EAAG;QAChC;QACAnQ,IAAI,CAAEmQ,KAAK,CAAC7O,GAAG,CAAE,SAAU,CAAC,CAAE,GAAG6O,KAAK,CAAC7O,GAAG,CAAE,KAAM,CAAC;;QAEnD;QACA4O,QAAQ,GAAGA,QAAQ,CAACE,GAAG,CAAED,KAAK,CAACvQ,CAAC,CAAE,uBAAwB,CAAE,CAAC;MAC9D,CAAE,CAAC;;MAEH;MACAsQ,QAAQ,CAACxN,IAAI,CAAE,YAAY;QAC1B;QACA,IAAIkK,OAAO,GAAGhN,CAAC,CAAE,IAAK,CAAC;QACvB,IAAIyF,GAAG,GAAGuH,OAAO,CAACvH,GAAG,CAAC,CAAC;;QAEvB;QACA,IAAK,CAAEA,GAAG,IAAI,CAAErF,IAAI,CAAEqF,GAAG,CAAE,EAAG;UAC7B;QACD;;QAEA;QACAuH,OAAO,CAAC9L,IAAI,CAAE,iBAAkB,CAAC,CAACgD,IAAI,CAAE,OAAO,EAAE9D,IAAI,CAAEqF,GAAG,CAAG,CAAC;;QAE9D;QACAuH,OAAO,CAACvH,GAAG,CAAErF,IAAI,CAAEqF,GAAG,CAAG,CAAC;MAC3B,CAAE,CAAC;IACJ;EACD,CAAE,CAAC;AACJ,CAAC,EAAI8B,MAAO,CAAC;;;;;;;;;;ACzZb,CAAE,UAAWvH,CAAC,EAAEC,SAAS,EAAG;EAC3BC,GAAG,CAACuQ,WAAW,GAAGvQ,GAAG,CAACgK,KAAK,CAACvJ,MAAM,CAAE;IACnC;IACA+P,UAAU,EAAE,mBAAmB;IAE/B;IACAC,gBAAgB,EAAE,KAAK;IAEvB;IACAnQ,MAAM,EAAE;MACP,iBAAiB,EAAE,aAAa;MAChC,eAAe,EAAE,aAAa;MAC9B,oBAAoB,EAAE,aAAa;MACnC,6CAA6C,EAAE,qBAAqB;MACpE,qBAAqB,EAAE,eAAe;MACtC,wBAAwB,EAAE,WAAW;MACrC,mBAAmB,EAAE,MAAM;MAC3B,sBAAsB,EAAE,cAAc;MAEtC,mBAAmB,EAAE,aAAa;MAClC,kCAAkC,EAAE,YAAY;MAEhD,oBAAoB,EAAE,cAAc;MACpC,wBAAwB,EAAE,kBAAkB;MAC5C,mBAAmB,EAAE,eAAe;MACpC,kBAAkB,EAAE,cAAc;MAElCoQ,MAAM,EAAE,UAAU;MAClBC,OAAO,EAAE;IACV,CAAC;IAED;IACAzQ,IAAI,EAAE;MACL;MACA;MACAgN,EAAE,EAAE,CAAC;MAEL;MACApG,GAAG,EAAE,EAAE;MAEP;MACApC,IAAI,EAAE;;MAEN;MACA;;MAEA;MACA;;MAEA;MACA;IACD,CAAC;IAEDnE,KAAK,EAAE,SAAAA,CAAWmH,MAAM,EAAG;MAC1B;MACA,IAAI,CAAChH,GAAG,GAAGgH,MAAM;;MAEjB;MACA,IAAI,CAACkJ,OAAO,CAAElJ,MAAO,CAAC;;MAEtB;MACA;MACA,IAAI,CAACQ,IAAI,CAAE,IAAK,CAAC;MACjB,IAAI,CAACA,IAAI,CAAE,QAAS,CAAC;MACrB,IAAI,CAACA,IAAI,CAAE,YAAa,CAAC;IAC1B,CAAC;IAEDyD,MAAM,EAAE,SAAAA,CAAW5J,IAAI,EAAG;MACzB,OAAOjC,CAAC,CAAE,GAAG,GAAG,IAAI,CAAC+Q,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG9O,IAAK,CAAC;IACjD,CAAC;IAED+O,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAO,IAAI,CAAChR,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDiR,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACjR,CAAC,CAAE,eAAgB,CAAC;IACjC,CAAC;IAEDyI,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAO,IAAI,CAACzI,CAAC,CAAE,iBAAkB,CAAC;IACnC,CAAC;IAEDkR,QAAQ,EAAE,SAAAA,CAAWjP,IAAI,EAAG;MAC3B,OAAO,IAAI,CAACjC,CAAC,CAAE,+CAA+C,GAAGiC,IAAK,CAAC;IACxE,CAAC;IAEDwE,gBAAgB,EAAE,SAAAA,CAAA,EAAY;MAC7B,OAAO,IAAI,CAACzG,CAAC,CAAE,aAAc,CAAC;IAC/B,CAAC;IAEDwF,WAAW,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAACxF,CAAC,CAAE,cAAe,CAAC;IAChC,CAAC;IAEDmR,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAOjR,GAAG,CAAC+M,eAAe,CAAE;QAAEsD,KAAK,EAAE,IAAI,CAAC3P,GAAG;QAAEwQ,KAAK,EAAE;MAAE,CAAE,CAAC,CAACC,GAAG,CAAC,CAAC;IAClE,CAAC;IAEDxD,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,OAAO3N,GAAG,CAAC+M,eAAe,CAAE;QAAEsD,KAAK,EAAE,IAAI,CAAC3P;MAAI,CAAE,CAAC;IAClD,CAAC;IAED0Q,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAOpR,GAAG,CAAC+M,eAAe,CAAE;QAAE5I,MAAM,EAAE,IAAI,CAACzD;MAAI,CAAE,CAAC;IACnD,CAAC;IAED2Q,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,OAAO,aAAa,GAAG,IAAI,CAAC7P,GAAG,CAAE,IAAK,CAAC,GAAG,GAAG;IAC9C,CAAC;IAEDqP,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,OAAO,aAAa,GAAG,IAAI,CAACrP,GAAG,CAAE,IAAK,CAAC;IACxC,CAAC;IAED8P,QAAQ,EAAE,SAAAA,CAAWvP,IAAI,EAAEkG,KAAK,EAAG;MAClC;MACA,IAAIsJ,OAAO,GAAG,IAAI,CAACV,UAAU,CAAC,CAAC;MAC/B,IAAIW,SAAS,GAAG,IAAI,CAACH,YAAY,CAAC,CAAC;;MAEnC;MACA,IAAKtP,IAAI,EAAG;QACXwP,OAAO,IAAI,GAAG,GAAGxP,IAAI;QACrByP,SAAS,IAAI,GAAG,GAAGzP,IAAI,GAAG,GAAG;MAC9B;;MAEA;MACA,IAAI4J,MAAM,GAAG7L,CAAC,CAAE,WAAY,CAAC,CAACkE,IAAI,CAAE;QACnCkJ,EAAE,EAAEqE,OAAO;QACXxP,IAAI,EAAEyP,SAAS;QACfvJ,KAAK,EAAEA;MACR,CAAE,CAAC;MACH,IAAI,CAACnI,CAAC,CAAE,SAAU,CAAC,CAAC+C,MAAM,CAAE8I,MAAO,CAAC;;MAEpC;MACA,OAAOA,MAAM;IACd,CAAC;IAED8F,OAAO,EAAE,SAAAA,CAAW1P,IAAI,EAAG;MAC1B;MACA,IAAK,IAAI,CAAC2P,GAAG,CAAE3P,IAAK,CAAC,EAAG;QACvB,OAAO,IAAI,CAACP,GAAG,CAAEO,IAAK,CAAC;MACxB;;MAEA;MACA,IAAI4J,MAAM,GAAG,IAAI,CAACA,MAAM,CAAE5J,IAAK,CAAC;MAChC,IAAIkG,KAAK,GAAG0D,MAAM,CAACpJ,MAAM,GAAGoJ,MAAM,CAACpG,GAAG,CAAC,CAAC,GAAG,IAAI;;MAE/C;MACA,IAAI,CAACZ,GAAG,CAAE5C,IAAI,EAAEkG,KAAK,EAAE,IAAK,CAAC;;MAE7B;MACA,OAAOA,KAAK;IACb,CAAC;IAED0J,OAAO,EAAE,SAAAA,CAAW5P,IAAI,EAAEkG,KAAK,EAAG;MACjC;MACA,IAAI0D,MAAM,GAAG,IAAI,CAACA,MAAM,CAAE5J,IAAK,CAAC;MAChC,IAAI6P,OAAO,GAAGjG,MAAM,CAACpG,GAAG,CAAC,CAAC;;MAE1B;MACA,IAAK,CAAEoG,MAAM,CAACpJ,MAAM,EAAG;QACtBoJ,MAAM,GAAG,IAAI,CAAC2F,QAAQ,CAAEvP,IAAI,EAAEkG,KAAM,CAAC;MACtC;;MAEA;MACA,IAAKA,KAAK,KAAK,IAAI,EAAG;QACrB0D,MAAM,CAAC3E,MAAM,CAAC,CAAC;;QAEf;MACD,CAAC,MAAM;QACN2E,MAAM,CAACpG,GAAG,CAAE0C,KAAM,CAAC;MACpB;;MAEA;;MAEA;MACA,IAAK,CAAE,IAAI,CAACyJ,GAAG,CAAE3P,IAAK,CAAC,EAAG;QACzB;QACA,IAAI,CAAC4C,GAAG,CAAE5C,IAAI,EAAEkG,KAAK,EAAE,IAAK,CAAC;;QAE7B;MACD,CAAC,MAAM;QACN;QACA,IAAI,CAACtD,GAAG,CAAE5C,IAAI,EAAEkG,KAAM,CAAC;MACxB;;MAEA;MACA,OAAO,IAAI;IACZ,CAAC;IAEDC,IAAI,EAAE,SAAAA,CAAWnG,IAAI,EAAEkG,KAAK,EAAG;MAC9B,IAAKA,KAAK,KAAKlI,SAAS,EAAG;QAC1B,OAAO,IAAI,CAAC4R,OAAO,CAAE5P,IAAI,EAAEkG,KAAM,CAAC;MACnC,CAAC,MAAM;QACN,OAAO,IAAI,CAACwJ,OAAO,CAAE1P,IAAK,CAAC;MAC5B;IACD,CAAC;IAEDvB,KAAK,EAAE,SAAAA,CAAWA,KAAK,EAAG;MACzBiB,MAAM,CAACoQ,IAAI,CAAErR,KAAM,CAAC,CAACwM,GAAG,CAAE,UAAWlG,GAAG,EAAG;QAC1C,IAAI,CAAC6K,OAAO,CAAE7K,GAAG,EAAEtG,KAAK,CAAEsG,GAAG,CAAG,CAAC;MAClC,CAAC,EAAE,IAAK,CAAC;IACV,CAAC;IAEDsG,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAInL,KAAK,GAAG,IAAI,CAACiG,IAAI,CAAE,OAAQ,CAAC;MAChC,IAAKjG,KAAK,KAAK,EAAE,EAAG;QACnBA,KAAK,GAAGjC,GAAG,CAACqN,EAAE,CAAE,YAAa,CAAC;MAC/B;;MAEA;MACA,OAAOpL,KAAK;IACb,CAAC;IAED6P,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAAC5J,IAAI,CAAE,MAAO,CAAC;IAC3B,CAAC;IAEDuF,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB,OAAO,IAAI,CAACvF,IAAI,CAAE,MAAO,CAAC;IAC3B,CAAC;IAED6J,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB,IAAIrN,IAAI,GAAG,IAAI,CAACwD,IAAI,CAAE,MAAO,CAAC;MAC9B,IAAI8J,KAAK,GAAGhS,GAAG,CAACwB,GAAG,CAAE,YAAa,CAAC;MACnC,OAAOwQ,KAAK,CAAEtN,IAAI,CAAE,GAAGsN,KAAK,CAAEtN,IAAI,CAAE,CAACzC,KAAK,GAAGyC,IAAI;IAClD,CAAC;IAEDyI,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACjF,IAAI,CAAE,KAAM,CAAC;IAC1B,CAAC;IAEDrH,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACoR,aAAa,CAAC,CAAC;IACrB,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAWnO,IAAI,EAAG;MAC/B,IAAK,CAAEoO,SAAS,CAACC,SAAS,EAAG,OAAO,0CAA0C,GAAGrO,IAAI,GAAG,SAAS;MACjG,OAAO,yBAAyB,GAAGA,IAAI,GAAG,SAAS;IACpD,CAAC;IAEDkO,aAAa,EAAE,SAAAA,CAAA,EAAY;MAC1B,IAAK,CAAEE,SAAS,CAACC,SAAS,EAAG;QAC5B,IAAI,CAAC1R,GAAG,CAACM,IAAI,CAAE,WAAY,CAAC,CAAC2E,QAAQ,CAAE,kBAAmB,CAAC;MAC5D;IACD,CAAC;IAED0M,0BAA0B,EAAE,SAAAA,CAAA,EAAY;MACvC,IAAK,IAAI,CAAC5B,gBAAgB,EAAG;;MAE7B;MACA,IAAK,IAAI,CAAClK,gBAAgB,CAAC,CAAC,CAAC+L,QAAQ,CAAE,iBAAkB,CAAC,EAAG;;MAE7D;MACA,IAAI;QACHxS,CAAC,CAACyS,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,OAAO,CAAE,4BAA6B,CAAC;MACzD,CAAC,CAAC,OAAQC,GAAG,EAAG;QACfC,OAAO,CAACC,IAAI,CACX,mLACD,CAAC;QACD;MACD;MAEA,IAAI,CAACpC,gBAAgB,GAAGzQ,GAAG,CAAC4O,UAAU,CAAE,IAAI,CAACrI,gBAAgB,CAAC,CAAC,EAAE;QAChEsE,KAAK,EAAE,KAAK;QACZiI,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE,KAAK;QAChBC,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,2BAA2B;QAC7CC,cAAc,EAAE,SAAAA,CAAWC,SAAS,EAAG;UACtC,IAAKA,SAAS,CAACC,OAAO,IAAMD,SAAS,CAACE,OAAO,IAAIF,SAAS,CAACE,OAAO,CAACC,QAAQ,KAAK,UAAY,EAAG;YAC9F,IAAIC,UAAU,GAAG1T,CAAC,CAAE,qCAAsC,CAAC;YAC3D0T,UAAU,CAACrS,IAAI,CAAEnB,GAAG,CAACyT,SAAS,CAAEL,SAAS,CAACrP,IAAK,CAAE,CAAC;UACnD,CAAC,MAAM;YACN,IAAIyP,UAAU,GAAG1T,CAAC,CACjB,4CAA4C,GAC3CsT,SAAS,CAAClG,EAAE,CAAC/J,UAAU,CAAE,GAAG,EAAE,GAAI,CAAC,GACnC,6CAA6C,GAC7CnD,GAAG,CAACyT,SAAS,CAAEL,SAAS,CAACrP,IAAK,CAAC,GAC/B,SACF,CAAC;UACF;UACAyP,UAAU,CAACtT,IAAI,CAAE,SAAS,EAAEkT,SAAS,CAACE,OAAQ,CAAC;UAC/C,OAAOE,UAAU;QAClB,CAAC;QACDE,iBAAiB,EAAE,SAAAA,CAAWN,SAAS,EAAG;UACzC,IAAII,UAAU,GAAG1T,CAAC,CACjB,4CAA4C,GAC3CsT,SAAS,CAAClG,EAAE,CAAC/J,UAAU,CAAE,GAAG,EAAE,GAAI,CAAC,GACnC,6CAA6C,GAC7CnD,GAAG,CAACyT,SAAS,CAAEL,SAAS,CAACrP,IAAK,CAAC,GAC/B,SACF,CAAC;UACDyP,UAAU,CAACtT,IAAI,CAAE,SAAS,EAAEkT,SAAS,CAACE,OAAQ,CAAC;UAC/C,OAAOE,UAAU;QAClB;MACD,CAAE,CAAC;MAEH,IAAI,CAAC/C,gBAAgB,CAAC9G,EAAE,CAAE,cAAc,EAAE,YAAY;QACrD7J,CAAC,CAAE,wDAAyD,CAAC,CAACkE,IAAI,CACjE,aAAa,EACbhE,GAAG,CAACqN,EAAE,CAAE,mBAAoB,CAC7B,CAAC;MACF,CAAE,CAAC;MAEH,IAAI,CAACoD,gBAAgB,CAAC9G,EAAE,CAAE,QAAQ,EAAE,UAAW9D,CAAC,EAAG;QAClD/F,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAACC,OAAO,CAAE,UAAW,CAAC,CAAC5S,IAAI,CAAE,sBAAuB,CAAC,CAACkH,IAAI,CAAE,UAAU,EAAE,IAAK,CAAC;MAC5F,CAAE,CAAC;;MAEH;MACA,IAAI,CAACuI,gBAAgB,CAAC/P,GAAG,CACvByD,MAAM,CAAC,CAAC,CACRwF,EAAE,CAAE,SAAS,EAAE,8CAA8C,EAAE,IAAI,CAACkK,eAAgB,CAAC;IACxF,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAK9T,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,IAAIxB,GAAG,CAACwB,GAAG,CAAE,iBAAkB,CAAC,EAAG;QAC1D;MACD;;MAEA;MACA,IAAI+E,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC,CAAC;MAC9C,IAAKA,gBAAgB,CAAC+L,QAAQ,CAAE,qBAAsB,CAAC,EAAG;;MAE1D;MACA,MAAMyB,aAAa,GAAG/T,GAAG,CAACwB,GAAG,CAAE,eAAgB,CAAC;MAChD,IAAK,OAAOuS,aAAa,KAAK,QAAQ,EAAG;MAEzC,MAAMC,YAAY,GAAGzN,gBAAgB,CAACvF,IAAI,CAAE,gCAAiC,CAAC,CAACmD,MAAM,CAAC,CAAC;MAEvF,MAAM8P,aAAa,GAAG1N,gBAAgB,CAACvF,IAAI,CAAE,gCAAiC,CAAC,CAACmD,MAAM,CAAC,CAAC;MAExF,KAAM,MAAM,CAAEpC,IAAI,EAAE8I,KAAK,CAAE,IAAIpJ,MAAM,CAACyS,OAAO,CAAEH,aAAc,CAAC,EAAG;QAChE,MAAMI,SAAS,GAAGtJ,KAAK,CAACxJ,QAAQ,KAAK,SAAS,GAAG4S,aAAa,GAAGD,YAAY;QAC7E,MAAMI,SAAS,GAAGD,SAAS,CAAClE,QAAQ,CAAE,UAAU,GAAGlO,IAAI,GAAG,IAAK,CAAC;QAChE,MAAME,KAAK,GAAG,GAAIjC,GAAG,CAACyT,SAAS,CAAE5I,KAAK,CAAC5I,KAAM,CAAC,KAAOjC,GAAG,CAACyT,SAAS,CAAEzT,GAAG,CAACqN,EAAE,CAAE,UAAW,CAAE,CAAC,GAAI;QAE9F,IAAK+G,SAAS,CAAC7R,MAAM,EAAG;UACvB;UACA6R,SAAS,CAACrQ,IAAI,CAAE9B,KAAM,CAAC;;UAEvB;UACA,IAAKsE,gBAAgB,CAAChB,GAAG,CAAC,CAAC,KAAKxD,IAAI,EAAG;YACtCqS,SAAS,CAACpQ,IAAI,CAAE,UAAU,EAAE,UAAW,CAAC;UACzC;QACD,CAAC,MAAM;UACN;UACAmQ,SAAS,CAACtR,MAAM,CAAE,4CAA6CZ,KAAK,WAAa,CAAC;QACnF;MACD;MAEAsE,gBAAgB,CAACZ,QAAQ,CAAE,qBAAsB,CAAC;IACnD,CAAC;IAED/E,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAImQ,OAAO,GAAG,IAAI,CAACjR,CAAC,CAAE,eAAgB,CAAC;MACvC,IAAIuU,UAAU,GAAG,IAAI,CAACnM,IAAI,CAAE,YAAa,CAAC;MAC1C,IAAIjG,KAAK,GAAG,IAAI,CAACmL,QAAQ,CAAC,CAAC;MAC3B,IAAIrL,IAAI,GAAG,IAAI,CAACmG,IAAI,CAAE,MAAO,CAAC;MAC9B,IAAIxD,IAAI,GAAG,IAAI,CAACqN,YAAY,CAAC,CAAC;MAC9B,IAAIjL,GAAG,GAAG,IAAI,CAACoB,IAAI,CAAE,KAAM,CAAC;MAC5B,IAAIoM,QAAQ,GAAG,IAAI,CAAC3I,MAAM,CAAE,UAAW,CAAC,CAACzD,IAAI,CAAE,SAAU,CAAC;;MAE1D;MACA6I,OAAO,CAAC/P,IAAI,CAAE,WAAY,CAAC,CAACG,IAAI,CAAEoT,QAAQ,CAAEF,UAAW,CAAC,GAAG,CAAE,CAAC;;MAE9D;MACA,IAAKC,QAAQ,EAAG;QACfrS,KAAK,IAAI,sCAAsC;MAChD;;MAEA;MACA8O,OAAO,CAAC/P,IAAI,CAAE,0BAA2B,CAAC,CAACG,IAAI,CAAEc,KAAM,CAAC;;MAExD;MACA8O,OAAO,CAAC/P,IAAI,CAAE,gBAAiB,CAAC,CAACG,IAAI,CAAE,IAAI,CAAC+Q,YAAY,CAAElS,GAAG,CAACwU,WAAW,CAAEzS,IAAK,CAAE,CAAE,CAAC;;MAErF;MACA,MAAMmB,QAAQ,GAAGlD,GAAG,CAACyU,UAAU,CAAE,IAAI,CAAChH,OAAO,CAAC,CAAE,CAAC;MACjDsD,OAAO,CAAC/P,IAAI,CAAE,mBAAoB,CAAC,CAAC+C,IAAI,CAAE,GAAG,GAAGW,IAAK,CAAC;MACtDqM,OAAO,CACL/P,IAAI,CAAE,kBAAmB,CAAC,CAC1B0E,WAAW,CAAC,CAAC,CACbC,QAAQ,CAAE,kCAAkC,GAAGzC,QAAS,CAAC;;MAE3D;MACA6N,OAAO,CAAC/P,IAAI,CAAE,eAAgB,CAAC,CAACG,IAAI,CAAE,IAAI,CAAC+Q,YAAY,CAAEpL,GAAI,CAAE,CAAC;;MAEhE;MACA9G,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;IAC5C,CAAC;IAEDwT,OAAO,EAAE,SAAAA,CAAA,EAAY;MACpB1U,GAAG,CAACkB,QAAQ,CAAE,sBAAsB,EAAE,IAAK,CAAC;IAC7C,CAAC;IAEDyT,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,OAAO,IAAI,CAACjU,GAAG,CAAC4R,QAAQ,CAAE,MAAO,CAAC;IACnC,CAAC;IAEDsC,WAAW,EAAE,SAAAA,CAAW/O,CAAC,EAAG;MAC3BA,CAAC,CAACgP,eAAe,CAAC,CAAC;MACnB,IAAK,CAAE1C,SAAS,CAACC,SAAS,IAAItS,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAACnF,EAAE,CAAE,OAAQ,CAAC,EAAG;;MAE5D;MACA,IAAIsG,SAAS;MACb,IAAKhV,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAACrB,QAAQ,CAAE,gBAAiB,CAAC,EAAG;QACjDwC,SAAS,GAAGhV,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAAC3S,IAAI,CAAE,OAAQ,CAAC,CAACqF,KAAK,CAAC,CAAC,CAACd,GAAG,CAAC,CAAC;MACxD,CAAC,MAAM;QACNuP,SAAS,GAAGhV,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAAC5P,IAAI,CAAC,CAAC,CAACoC,IAAI,CAAC,CAAC;MACxC;MAEAgM,SAAS,CAACC,SAAS,CAAC2C,SAAS,CAAED,SAAU,CAAC,CAACE,IAAI,CAAE,MAAM;QACtDlV,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAAC/J,OAAO,CAAE,WAAY,CAAC,CAACjE,QAAQ,CAAE,QAAS,CAAC;QACzDR,UAAU,CAAE,YAAY;UACvBrF,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAAC/J,OAAO,CAAE,WAAY,CAAC,CAAClE,WAAW,CAAE,QAAS,CAAC;QAC7D,CAAC,EAAE,IAAK,CAAC;MACV,CAAE,CAAC;IACJ,CAAC;IAEDuP,WAAW,EAAE,SAAAA,CAAWpP,CAAC,EAAG;MAC3B,MAAMqP,OAAO,GAAGpV,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC;;MAE7B;MACA,IACC3T,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,IACnB,CAAExB,GAAG,CAACwB,GAAG,CAAE,iBAAkB,CAAC,IAC9B,CAAExB,GAAG,CAACwB,GAAG,CAAE,kBAAmB,CAAC,IAC/BxB,GAAG,CAACwB,GAAG,CAAE,eAAgB,CAAC,CAAC2T,cAAc,CAAE,IAAI,CAAC1H,OAAO,CAAC,CAAE,CAAC,EAC1D;QACD;MACD;MAEA,IAAKyH,OAAO,CAAC/Q,MAAM,CAAC,CAAC,CAACmO,QAAQ,CAAE,aAAc,CAAC,IAAI,CAAE4C,OAAO,CAAC5C,QAAQ,CAAE,YAAa,CAAC,EAAG;QACvF;MACD;MAEA,IAAI,CAACqC,MAAM,CAAC,CAAC,GAAG,IAAI,CAACnO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC1F,IAAI,CAAC,CAAC;IAC3C,CAAC;IAEDsU,mBAAmB,EAAE,SAAAA,CAAA,EAAY;MAChC,MAAM7M,SAAS,GAAG,IAAI,CAAC7H,GAAG,CAACuP,QAAQ,CAAE,WAAY,CAAC;MAClDjQ,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEqH,SAAU,CAAC;IAClC,CAAC;IAED;AACF;AACA;IACE8M,WAAW,EAAE,SAAAA,CAAWxP,CAAC,EAAG;MAC3B,IAAIyP,WAAW,GAAGxV,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAAC/J,OAAO,CAAE,IAAK,CAAC,CAAC5I,IAAI,CAAE,cAAe,CAAC;MACtEsU,WAAW,CAAC3P,QAAQ,CAAE,QAAS,CAAC;IACjC,CAAC;IAED;AACF;AACA;IACE4P,UAAU,EAAE,SAAAA,CAAW1P,CAAC,EAAG;MAC1B,IAAI2P,sBAAsB,GAAG,EAAE;MAC/B,IAAIC,sBAAsB,GAAG3V,CAAC,CAAE+F,CAAC,CAAC8N,MAAO,CAAC,CAAC/J,OAAO,CAAE,IAAK,CAAC,CAAC5I,IAAI,CAAE,cAAe,CAAC;;MAEjF;MACAmE,UAAU,CAAE,YAAY;QACvB,IAAIuQ,uBAAuB,GAAG5V,CAAC,CAAE4J,QAAQ,CAACiM,aAAc,CAAC,CAAC/L,OAAO,CAAE,IAAK,CAAC,CAAC5I,IAAI,CAAE,cAAe,CAAC;QAChG,IAAK,CAAEyU,sBAAsB,CAACjH,EAAE,CAAEkH,uBAAwB,CAAC,EAAG;UAC7DD,sBAAsB,CAAC/P,WAAW,CAAE,QAAS,CAAC;QAC/C;MACD,CAAC,EAAE8P,sBAAuB,CAAC;IAC5B,CAAC;IAED1U,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB;MACA,IAAIyH,SAAS,GAAG,IAAI,CAAC7H,GAAG,CAACuP,QAAQ,CAAE,WAAY,CAAC;;MAEhD;MACA,IAAI,CAAC6D,YAAY,CAAC,CAAC;MACnB,IAAI,CAACzB,0BAA0B,CAAC,CAAC;;MAEjC;MACArS,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE,IAAK,CAAC;MACzC,IAAI,CAACuE,OAAO,CAAE,iBAAkB,CAAC;;MAEjC;MACAzF,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEqH,SAAU,CAAC;MAEjC,IAAI,CAACqN,aAAa,CAAC,CAAC;;MAEpB;MACArN,SAAS,CAACsN,SAAS,CAAC,CAAC;MACrB,IAAI,CAACnV,GAAG,CAACiF,QAAQ,CAAE,MAAO,CAAC;IAC5B,CAAC;IAEDkO,eAAe,EAAE,SAAAA,CAAWhO,CAAC,EAAG;MAC/B;MACA,IACC,EACGA,CAAC,CAACiQ,KAAK,IAAI,GAAG,IAAIjQ,CAAC,CAACiQ,KAAK,IAAI,GAAG;MAAM;MACxC,CACC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAC7F,GAAG,CACH,CAAChU,QAAQ,CAAE+D,CAAC,CAACiQ,KAAM,CAAC;MAAI;MACvBjQ,CAAC,CAACiQ,KAAK,IAAI,GAAG,IAAIjQ,CAAC,CAACiQ,KAAK,IAAI,GAAK,CACpC,EACA;QACD;QACAhW,CAAC,CAAE,IAAK,CAAC,CAAC8J,OAAO,CAAE,oBAAqB,CAAC,CAACgG,QAAQ,CAAE,gBAAiB,CAAC,CAAC4C,OAAO,CAAE,MAAO,CAAC;QACxF;MACD;IACD,CAAC;IAEDhM,KAAK,EAAE,SAAAA,CAAA,EAAY;MAClB;MACA,IAAI+B,SAAS,GAAG,IAAI,CAAC7H,GAAG,CAACuP,QAAQ,CAAE,WAAY,CAAC;;MAEhD;MACA1H,SAAS,CAACwN,OAAO,CAAC,CAAC;MACnB,IAAI,CAACrV,GAAG,CAACgF,WAAW,CAAE,MAAO,CAAC;;MAE9B;MACA1F,GAAG,CAACkB,QAAQ,CAAE,oBAAoB,EAAE,IAAK,CAAC;MAC1C,IAAI,CAACuE,OAAO,CAAE,kBAAmB,CAAC;;MAElC;MACAzF,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEqH,SAAU,CAAC;IAClC,CAAC;IAEDyN,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB,OAAOhW,GAAG,CAACgW,SAAS,CAAE,IAAI,CAACtV,GAAG,EAAE,IAAI,CAAC2Q,YAAY,CAAC,CAAE,CAAC;IACtD,CAAC;IAEDzJ,IAAI,EAAE,SAAAA,CAAWlD,IAAI,EAAG;MACvB;MACAA,IAAI,GAAGA,IAAI,IAAI,UAAU,CAAC,CAAC;;MAE3B;MACA,IAAIkD,IAAI,GAAG,IAAI,CAAC6J,OAAO,CAAE,MAAO,CAAC;;MAEjC;MACA,IAAK7J,IAAI,KAAK,UAAU,EAAG;QAC1B;MACD;;MAEA;MACA,IAAI,CAAC+J,OAAO,CAAE,MAAM,EAAEjN,IAAK,CAAC;;MAE5B;MACA,IAAI,CAAChE,GAAG,CAACsD,IAAI,CAAE,WAAW,EAAEU,IAAK,CAAC;;MAElC;MACA1E,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE,IAAI,EAAEwD,IAAK,CAAC;IAChD,CAAC;IAEDuR,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;MACA,IAAIzE,SAAS,GAAG,IAAI,CAACH,YAAY,CAAC,CAAC;MACnC,IAAIzJ,IAAI,GAAG,IAAI,CAACpG,GAAG,CAAE,MAAO,CAAC;;MAE7B;MACA,IAAK,IAAI,CAACmT,MAAM,CAAC,CAAC,EAAG;QACpB,IAAI,CAACnO,KAAK,CAAC,CAAC;MACb;;MAEA;MACA,IAAKoB,IAAI,IAAI,UAAU,EAAG;QACzB;QACA;MAAA,CACA,MAAM,IAAKA,IAAI,IAAI,MAAM,EAAG;QAC5B,IAAI,CAAC9H,CAAC,CAAE,sBAAsB,GAAG0R,SAAS,GAAG,IAAK,CAAC,CAACxK,MAAM,CAAC,CAAC;;QAE5D;MACD,CAAC,MAAM;QACN,IAAI,CAAClH,CAAC,CAAE,UAAU,GAAG0R,SAAS,GAAG,IAAK,CAAC,CAACxK,MAAM,CAAC,CAAC;MACjD;;MAEA;MACAhH,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;IAC5C,CAAC;IAEDgV,QAAQ,EAAE,SAAAA,CAAWrQ,CAAC,EAAEnF,GAAG,EAAG;MAC7B;MACA,IAAI,CAACkH,IAAI,CAAC,CAAC;;MAEX;MACA5H,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;IAC5C,CAAC;IAEDiV,SAAS,EAAE,SAAAA,CAAWtQ,CAAC,EAAEnF,GAAG,EAAEqB,IAAI,EAAEkG,KAAK,EAAG;MAC3C,IAAK,IAAI,CAACwF,OAAO,CAAC,CAAC,KAAK/M,GAAG,CAACsD,IAAI,CAAE,WAAY,CAAC,EAAG;QACjDlE,CAAC,CAAE,8BAA+B,CAAC,CAACoI,IAAI,CAAE,UAAU,EAAE,KAAM,CAAC;MAC9D;;MAEA;MACA,IAAKnG,IAAI,IAAI,MAAM,EAAG;QACrB;MACD;;MAEA;MACA,IAAK,CAAE,YAAY,EAAE,QAAQ,CAAE,CAACyH,OAAO,CAAEzH,IAAK,CAAC,GAAG,CAAC,CAAC,EAAG;QACtD,IAAI,CAAC6F,IAAI,CAAE,MAAO,CAAC;;QAEnB;MACD,CAAC,MAAM;QACN,IAAI,CAACA,IAAI,CAAC,CAAC;MACZ;;MAEA;MACA,IAAK,CAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAE,CAAC4B,OAAO,CAAEzH,IAAK,CAAC,GAAG,CAAC,CAAC,EAAG;QACxF,IAAI,CAACnB,MAAM,CAAC,CAAC;MACd;;MAEA;MACAZ,GAAG,CAACkB,QAAQ,CAAE,sBAAsB,GAAGa,IAAI,EAAE,IAAI,EAAEkG,KAAM,CAAC;IAC3D,CAAC;IAEDmO,aAAa,EAAE,SAAAA,CAAWvQ,CAAC,EAAEnF,GAAG,EAAG;MAClC;MACA,MAAMuB,KAAK,GAAGvB,GAAG,CAAC6E,GAAG,CAAC,CAAC;MACvB,MAAM8Q,SAAS,GAAGrW,GAAG,CAACsW,MAAM,CAAErU,KAAM,CAAC;MACrC,IAAI,CAAC0C,GAAG,CAAE,OAAO,EAAE0R,SAAU,CAAC;;MAE9B;MACA,IAAK,IAAI,CAACnO,IAAI,CAAE,MAAO,CAAC,IAAI,EAAE,EAAG;QAChC,IAAInG,IAAI,GAAG/B,GAAG,CAACuW,YAAY,CAAE,4BAA4B,EAAEvW,GAAG,CAACwU,WAAW,CAAEvS,KAAM,CAAC,EAAE,IAAK,CAAC;QAC3F,IAAI,CAACiG,IAAI,CAAE,MAAM,EAAEnG,IAAK,CAAC;MAC1B;IACD,CAAC;IAEDyU,YAAY,EAAE,SAAAA,CAAW3Q,CAAC,EAAEnF,GAAG,EAAG;MACjC,MAAM+V,aAAa,GAAGzW,GAAG,CAACwU,WAAW,CAAE9T,GAAG,CAAC6E,GAAG,CAAC,CAAC,EAAE,KAAM,CAAC;MAEzD7E,GAAG,CAAC6E,GAAG,CAAEkR,aAAc,CAAC;MACxB,IAAI,CAAC9R,GAAG,CAAE,MAAM,EAAE8R,aAAc,CAAC;MAEjC,IAAKA,aAAa,CAACnU,UAAU,CAAE,QAAS,CAAC,EAAG;QAC3CoU,KAAK,CAAE1W,GAAG,CAACqN,EAAE,CAAE,kEAAmE,CAAE,CAAC;MACtF;IACD,CAAC;IAEDsJ,gBAAgB,EAAE,SAAAA,CAAW9Q,CAAC,EAAEnF,GAAG,EAAG;MACrC;MACA,IAAI4T,QAAQ,GAAG5T,GAAG,CAACwH,IAAI,CAAE,SAAU,CAAC,GAAG,CAAC,GAAG,CAAC;MAC5C,IAAI,CAACvD,GAAG,CAAE,UAAU,EAAE2P,QAAS,CAAC;IACjC,CAAC;IAEDvM,MAAM,EAAE,SAAAA,CAAWtE,IAAI,EAAG;MACzB;MACAA,IAAI,GAAGzD,GAAG,CAAC0D,SAAS,CAAED,IAAI,EAAE;QAC3BqE,OAAO,EAAE;MACV,CAAE,CAAC;;MAEH;MACA,IAAIoF,EAAE,GAAG,IAAI,CAAChF,IAAI,CAAE,IAAK,CAAC;MAE1B,IAAKgF,EAAE,EAAG;QACT,IAAIvB,MAAM,GAAG7L,CAAC,CAAE,qBAAsB,CAAC;QACvC,IAAI8W,MAAM,GAAGjL,MAAM,CAACpG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG2H,EAAE;QACpCvB,MAAM,CAACpG,GAAG,CAAEqR,MAAO,CAAC;MACrB;;MAEA;MACA5W,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE,IAAK,CAAC;;MAE3C;MACA,IAAKuC,IAAI,CAACqE,OAAO,EAAG;QACnB,IAAI,CAAC+O,aAAa,CAAC,CAAC;MACrB,CAAC,MAAM;QACN,IAAI,CAAC7P,MAAM,CAAC,CAAC;MACd;IACD,CAAC;IAED8P,aAAa,EAAE,SAAAA,CAAWjR,CAAC,EAAEnF,GAAG,EAAG;MAClC;MACA,IAAKmF,CAAC,CAACkR,QAAQ,EAAG;QACjB,OAAO,IAAI,CAAChP,MAAM,CAAC,CAAC;MACrB;;MAEA;MACA,IAAI,CAACrH,GAAG,CAACiF,QAAQ,CAAE,QAAS,CAAC;;MAE7B;MACA,IAAIqR,OAAO,GAAGhX,GAAG,CAACiX,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnBvD,MAAM,EAAEjT,GAAG;QACX+I,OAAO,EAAE,IAAI;QACb0N,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB,IAAI,CAACpP,MAAM,CAAC,CAAC;QACd,CAAC;QACDqP,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB,IAAI,CAAC1W,GAAG,CAACgF,WAAW,CAAE,QAAS,CAAC;QACjC;MACD,CAAE,CAAC;IACJ,CAAC;IAEDmR,aAAa,EAAE,SAAAA,CAAA,EAAY;MAC1B;MACA,IAAIhM,KAAK,GAAG,IAAI;MAChB,IAAIwM,KAAK,GAAG,IAAI,CAAC3W,GAAG,CAACyD,MAAM,CAAC,CAAC;MAC7B,IAAImT,OAAO,GAAGtX,GAAG,CAACuX,gBAAgB,CAAE;QACnCC,OAAO,EAAE,IAAI,CAAC9W;MACf,CAAE,CAAC;;MAEH;MACAV,GAAG,CAACgH,MAAM,CAAE;QACX2M,MAAM,EAAE,IAAI,CAACjT,GAAG;QAChB+W,SAAS,EAAEH,OAAO,CAAC/U,MAAM,GAAG,CAAC,GAAG,EAAE;QAClCmV,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB7M,KAAK,CAAC7D,MAAM,CAAC,CAAC;UACdhH,GAAG,CAACkB,QAAQ,CAAE,sBAAsB,EAAE2J,KAAK,EAAEwM,KAAM,CAAC;QACrD;MACD,CAAE,CAAC;;MAEH;MACArX,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAE2J,KAAK,EAAEwM,KAAM,CAAC;IACpD,CAAC;IAEDjI,SAAS,EAAE,SAAAA,CAAA,EAAY;MACtB;MACA,IAAIuI,MAAM,GAAG3X,GAAG,CAAC4X,MAAM,CAAE,QAAS,CAAC;;MAEnC;MACA,IAAIC,SAAS,GAAG7X,GAAG,CAACoP,SAAS,CAAE;QAC9BuE,MAAM,EAAE,IAAI,CAACjT,GAAG;QAChBY,MAAM,EAAE,IAAI,CAACE,GAAG,CAAE,IAAK,CAAC;QACxBsW,OAAO,EAAEH;MACV,CAAE,CAAC;;MAEH;MACAE,SAAS,CAAC7T,IAAI,CAAE,UAAU,EAAE2T,MAAO,CAAC;;MAEpC;MACA,IAAIzH,QAAQ,GAAGlQ,GAAG,CAAC2H,cAAc,CAAEkQ,SAAU,CAAC;;MAE9C;MACA,IAAI5V,KAAK,GAAGiO,QAAQ,CAAChI,IAAI,CAAE,OAAQ,CAAC;MACpC,IAAInG,IAAI,GAAGmO,QAAQ,CAAChI,IAAI,CAAE,MAAO,CAAC;MAClC,IAAI6P,GAAG,GAAGhW,IAAI,CAACK,KAAK,CAAE,GAAI,CAAC,CAAC+O,GAAG,CAAC,CAAC;MACjC,IAAI6G,IAAI,GAAGhY,GAAG,CAACqN,EAAE,CAAE,MAAO,CAAC;;MAE3B;MACA,IAAKrN,GAAG,CAACiY,SAAS,CAAEF,GAAI,CAAC,EAAG;QAC3B,IAAIG,CAAC,GAAGH,GAAG,GAAG,CAAC,GAAG,CAAC;QACnB9V,KAAK,GAAGA,KAAK,CAAC6V,OAAO,CAAEC,GAAG,EAAEG,CAAE,CAAC;QAC/BnW,IAAI,GAAGA,IAAI,CAAC+V,OAAO,CAAEC,GAAG,EAAEG,CAAE,CAAC;;QAE7B;MACD,CAAC,MAAM,IAAKH,GAAG,CAACvO,OAAO,CAAEwO,IAAK,CAAC,KAAK,CAAC,EAAG;QACvC,IAAIE,CAAC,GAAGH,GAAG,CAACD,OAAO,CAAEE,IAAI,EAAE,EAAG,CAAC,GAAG,CAAC;QACnCE,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC;;QAEjB;QACAjW,KAAK,GAAGA,KAAK,CAAC6V,OAAO,CAAEC,GAAG,EAAEC,IAAI,GAAGE,CAAE,CAAC;QACtCnW,IAAI,GAAGA,IAAI,CAAC+V,OAAO,CAAEC,GAAG,EAAEC,IAAI,GAAGE,CAAE,CAAC;;QAEpC;MACD,CAAC,MAAM;QACNjW,KAAK,IAAI,IAAI,GAAG+V,IAAI,GAAG,GAAG;QAC1BjW,IAAI,IAAI,GAAG,GAAGiW,IAAI;MACnB;MAEA9H,QAAQ,CAAChI,IAAI,CAAE,IAAI,EAAE,CAAE,CAAC;MACxBgI,QAAQ,CAAChI,IAAI,CAAE,OAAO,EAAEjG,KAAM,CAAC;MAC/BiO,QAAQ,CAAChI,IAAI,CAAE,MAAM,EAAEnG,IAAK,CAAC;MAC7BmO,QAAQ,CAAChI,IAAI,CAAE,KAAK,EAAEyP,MAAO,CAAC;;MAE9B;MACA,IAAK,IAAI,CAAChD,MAAM,CAAC,CAAC,EAAG;QACpB,IAAI,CAACnO,KAAK,CAAC,CAAC;MACb;;MAEA;MACA0J,QAAQ,CAACpP,IAAI,CAAC,CAAC;;MAEf;MACA,IAAIqX,MAAM,GAAGjI,QAAQ,CAACc,QAAQ,CAAE,aAAc,CAAC;MAC/C7L,UAAU,CAAE,YAAY;QACvBgT,MAAM,CAAC1S,OAAO,CAAE,OAAQ,CAAC;MAC1B,CAAC,EAAE,GAAI,CAAC;;MAER;MACAzF,GAAG,CAACkB,QAAQ,CAAE,wBAAwB,EAAE,IAAI,EAAEgP,QAAS,CAAC;MACxDlQ,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAEgP,QAAS,CAAC;IAChD,CAAC;IAEDkI,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB;MACA,IAAIC,MAAM,GAAG,IAAI,CAAC7W,GAAG,CAAE,IAAK,CAAC;MAC7B,IAAI8W,OAAO,GAAG,IAAI,CAAC9W,GAAG,CAAE,KAAM,CAAC;MAC/B,IAAImW,MAAM,GAAG3X,GAAG,CAAC4X,MAAM,CAAE,QAAS,CAAC;;MAEnC;MACA5X,GAAG,CAACuY,MAAM,CAAE;QACX5E,MAAM,EAAE,IAAI,CAACjT,GAAG;QAChBY,MAAM,EAAE+W,MAAM;QACdP,OAAO,EAAEH;MACV,CAAE,CAAC;;MAEH;MACA,IAAI,CAAChT,GAAG,CAAE,IAAI,EAAEgT,MAAO,CAAC;MACxB,IAAI,CAAChT,GAAG,CAAE,QAAQ,EAAE0T,MAAO,CAAC;MAC5B,IAAI,CAAC1T,GAAG,CAAE,SAAS,EAAE2T,OAAQ,CAAC;;MAE9B;MACA,IAAI,CAACpQ,IAAI,CAAE,KAAK,EAAEyP,MAAO,CAAC;MAC1B,IAAI,CAACzP,IAAI,CAAE,IAAI,EAAE,CAAE,CAAC;;MAEpB;MACA,IAAI,CAACxH,GAAG,CAACsD,IAAI,CAAE,UAAU,EAAE2T,MAAO,CAAC;MACnC,IAAI,CAACjX,GAAG,CAACsD,IAAI,CAAE,SAAS,EAAE2T,MAAO,CAAC;;MAElC;MACA3X,GAAG,CAACkB,QAAQ,CAAE,mBAAmB,EAAE,IAAK,CAAC;IAC1C,CAAC;IAEDsX,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB;MACA,IAAIC,UAAU,GAAG,SAAAA,CAAW5N,KAAK,EAAG;QACnC,OAAOA,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,IAAI,UAAU;MACzC,CAAC;;MAED;MACA,IAAImP,OAAO,GAAG8H,UAAU,CAAE,IAAK,CAAC;;MAEhC;MACA,IAAK,CAAE9H,OAAO,EAAG;QAChB3Q,GAAG,CAAC+M,eAAe,CAAE;UACpB5I,MAAM,EAAE,IAAI,CAACzD;QACd,CAAE,CAAC,CAACsM,GAAG,CAAE,UAAWnC,KAAK,EAAG;UAC3B8F,OAAO,GAAG8H,UAAU,CAAE5N,KAAM,CAAC,IAAIA,KAAK,CAAC8F,OAAO;QAC/C,CAAE,CAAC;MACJ;;MAEA;MACA,IAAKA,OAAO,EAAG;QACd+F,KAAK,CAAE1W,GAAG,CAACqN,EAAE,CAAE,8DAA+D,CAAE,CAAC;QACjF;MACD;;MAEA;MACA,IAAIH,EAAE,GAAG,IAAI,CAAChF,IAAI,CAAE,IAAK,CAAC;MAC1B,IAAI2C,KAAK,GAAG,IAAI;MAChB,IAAI6N,KAAK,GAAG,KAAK;MACjB,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAY;QACvB;QACAD,KAAK,GAAG1Y,GAAG,CAAC4Y,QAAQ,CAAE;UACrBC,KAAK,EAAE7Y,GAAG,CAACqN,EAAE,CAAE,mBAAoB,CAAC;UACpCgG,OAAO,EAAE,IAAI;UACbyF,KAAK,EAAE,OAAO;UACd3Y,QAAQ,EAAE0K,KAAK,CAACnK,GAAG,CAACM,IAAI,CAAE,aAAc;QACzC,CAAE,CAAC;;QAEH;QACA,IAAI+X,QAAQ,GAAG;UACdC,MAAM,EAAE,4BAA4B;UACpCC,QAAQ,EAAE/L;QACX,CAAC;;QAED;QACApN,CAAC,CAACgT,IAAI,CAAE;UACPzP,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;UACzBtB,IAAI,EAAEF,GAAG,CAACkZ,cAAc,CAAEH,QAAS,CAAC;UACpCrU,IAAI,EAAE,MAAM;UACZyU,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEC;QACV,CAAE,CAAC;MACJ,CAAC;MAED,IAAIA,KAAK,GAAG,SAAAA,CAAWlY,IAAI,EAAG;QAC7B;QACAuX,KAAK,CAACrF,OAAO,CAAE,KAAM,CAAC;QACtBqF,KAAK,CAACY,OAAO,CAAEnY,IAAK,CAAC;;QAErB;QACAuX,KAAK,CAAC/O,EAAE,CAAE,QAAQ,EAAE,MAAM,EAAE4P,KAAM,CAAC;MACpC,CAAC;MAED,IAAIA,KAAK,GAAG,SAAAA,CAAW1T,CAAC,EAAEnF,GAAG,EAAG;QAC/B;QACAmF,CAAC,CAAC2T,cAAc,CAAC,CAAC;;QAElB;QACAxZ,GAAG,CAACyZ,kBAAkB,CAAEf,KAAK,CAAC5Y,CAAC,CAAE,SAAU,CAAE,CAAC;;QAE9C;QACA,IAAIiZ,QAAQ,GAAG;UACdC,MAAM,EAAE,4BAA4B;UACpCC,QAAQ,EAAE/L,EAAE;UACZwM,cAAc,EAAEhB,KAAK,CAAC5Y,CAAC,CAAE,QAAS,CAAC,CAACyF,GAAG,CAAC;QACzC,CAAC;;QAED;QACAzF,CAAC,CAACgT,IAAI,CAAE;UACPzP,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;UACzBtB,IAAI,EAAEF,GAAG,CAACkZ,cAAc,CAAEH,QAAS,CAAC;UACpCrU,IAAI,EAAE,MAAM;UACZyU,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEO;QACV,CAAE,CAAC;MACJ,CAAC;MAED,IAAIA,KAAK,GAAG,SAAAA,CAAWxY,IAAI,EAAG;QAC7BuX,KAAK,CAACY,OAAO,CAAEnY,IAAK,CAAC;QAErB,IAAKyY,EAAE,CAACC,IAAI,IAAID,EAAE,CAACC,IAAI,CAACC,KAAK,IAAI9Z,GAAG,CAACqN,EAAE,EAAG;UACzCuM,EAAE,CAACC,IAAI,CAACC,KAAK,CAAE9Z,GAAG,CAACqN,EAAE,CAAE,4BAA6B,CAAC,EAAE,QAAS,CAAC;QAClE;QAEAqL,KAAK,CAAC5Y,CAAC,CAAE,kBAAmB,CAAC,CAACmB,KAAK,CAAC,CAAC;QAErC4J,KAAK,CAACgM,aAAa,CAAC,CAAC;MACtB,CAAC;;MAED;MACA8B,KAAK,CAAC,CAAC;IACR,CAAC;IAEDoB,YAAY,EAAE,SAAAA,CAAWlU,CAAC,EAAEnF,GAAG,EAAG;MACjCmF,CAAC,CAAC2T,cAAc,CAAC,CAAC;MAElB,MAAMQ,KAAK,GAAGha,GAAG,CAACmH,oBAAoB,CAAE;QACvChH,QAAQ,EAAE;MACX,CAAE,CAAC;IACJ,CAAC;IAED8Z,YAAY,EAAE,SAAAA,CAAWpU,CAAC,EAAEnF,GAAG,EAAG;MACjC;MACA,IAAK,IAAI,CAACwZ,aAAa,EAAG;QACzBC,YAAY,CAAE,IAAI,CAACD,aAAc,CAAC;MACnC;;MAEA;MACA;MACA,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC/U,UAAU,CAAE,YAAY;QACjD,IAAI,CAACiV,UAAU,CAAE1Z,GAAG,CAAC6E,GAAG,CAAC,CAAE,CAAC;MAC7B,CAAC,EAAE,GAAI,CAAC;IACT,CAAC;IAED6U,UAAU,EAAE,SAAAA,CAAWC,OAAO,EAAG;MAChC,IAAIC,QAAQ,GAAG,IAAI,CAACpS,IAAI,CAAE,MAAO,CAAC;MAClC,IAAIqS,SAAS,GAAGva,GAAG,CAACyU,UAAU,CAAE,mBAAmB,GAAG6F,QAAS,CAAC;MAChE,IAAIE,QAAQ,GAAGxa,GAAG,CAACyU,UAAU,CAAE,mBAAmB,GAAG4F,OAAQ,CAAC;;MAE9D;MACA,IAAI,CAAC3Z,GAAG,CAACgF,WAAW,CAAE6U,SAAU,CAAC,CAAC5U,QAAQ,CAAE6U,QAAS,CAAC;MACtD,IAAI,CAAC9Z,GAAG,CAACsD,IAAI,CAAE,WAAW,EAAEqW,OAAQ,CAAC;MACrC,IAAI,CAAC3Z,GAAG,CAACR,IAAI,CAAE,MAAM,EAAEma,OAAQ,CAAC;;MAEhC;MACA,IAAK,IAAI,CAAC3I,GAAG,CAAE,KAAM,CAAC,EAAG;QACxB,IAAI,CAAClQ,GAAG,CAAE,KAAM,CAAC,CAACiZ,KAAK,CAAC,CAAC;MAC1B;;MAEA;MACA,MAAMC,YAAY,GAAG,CAAC,CAAC;MAEvB,IAAI,CAACha,GAAG,CACNM,IAAI,CAAE,iFAAkF,CAAC,CACzF4B,IAAI,CAAE,YAAY;QAClB,IAAI+X,GAAG,GAAG7a,CAAC,CAAE,IAAK,CAAC,CAACI,IAAI,CAAE,YAAa,CAAC;QACxC,IAAI0a,YAAY,GAAG9a,CAAC,CAAE,IAAK,CAAC,CAACmQ,QAAQ,CAAC,CAAC,CAAC4K,UAAU,CAAC,CAAC;QAEpDH,YAAY,CAAEC,GAAG,CAAE,GAAGC,YAAY;QAElCA,YAAY,CAAC9L,MAAM,CAAC,CAAC;MACtB,CAAE,CAAC;MAEJ,IAAI,CAACnK,GAAG,CAAE,WAAW,GAAG2V,QAAQ,EAAEI,YAAa,CAAC;;MAEhD;MACA,IAAK,IAAI,CAAChJ,GAAG,CAAE,WAAW,GAAG2I,OAAQ,CAAC,EAAG;QACxC,IAAIS,YAAY,GAAG,IAAI,CAACtZ,GAAG,CAAE,WAAW,GAAG6Y,OAAQ,CAAC;QAEpD,IAAI,CAACU,qBAAqB,CAAED,YAAa,CAAC;QAC1C,IAAI,CAACnW,GAAG,CAAE,MAAM,EAAE0V,OAAQ,CAAC;QAC3B;MACD;;MAEA;MACA,MAAMW,QAAQ,GAAGlb,CAAC,CACjB,2FACD,CAAC;MACD,IAAI,CAACY,GAAG,CAACM,IAAI,CAAE,2DAA4D,CAAC,CAACia,MAAM,CAAED,QAAS,CAAC;MAE/F,MAAMjC,QAAQ,GAAG;QAChBC,MAAM,EAAE,uCAAuC;QAC/CnO,KAAK,EAAE,IAAI,CAACmL,SAAS,CAAC,CAAC;QACvBkF,MAAM,EAAE,IAAI,CAAC7J,YAAY,CAAC;MAC3B,CAAC;;MAED;MACA,IAAI8J,GAAG,GAAGrb,CAAC,CAACgT,IAAI,CAAE;QACjBzP,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;QACzBtB,IAAI,EAAEF,GAAG,CAACkZ,cAAc,CAAEH,QAAS,CAAC;QACpCrU,IAAI,EAAE,MAAM;QACZyU,QAAQ,EAAE,MAAM;QAChB1P,OAAO,EAAE,IAAI;QACb2P,OAAO,EAAE,SAAAA,CAAWgC,QAAQ,EAAG;UAC9B,IAAK,CAAEpb,GAAG,CAACqb,aAAa,CAAED,QAAS,CAAC,EAAG;YACtC;UACD;UAEA,IAAI,CAACL,qBAAqB,CAAEK,QAAQ,CAAClb,IAAK,CAAC;QAC5C,CAAC;QACDwX,QAAQ,EAAE,SAAAA,CAAA,EAAY;UACrB;UACAsD,QAAQ,CAAChU,MAAM,CAAC,CAAC;UACjB,IAAI,CAACrC,GAAG,CAAE,MAAM,EAAE0V,OAAQ,CAAC;UAC3B;QACD;MACD,CAAE,CAAC;;MAEH;MACA,IAAI,CAAC1V,GAAG,CAAE,KAAK,EAAEwW,GAAI,CAAC;IACvB,CAAC;IAEDJ,qBAAqB,EAAE,SAAAA,CAAWO,QAAQ,EAAG;MAC5C,IAAK,QAAQ,KAAK,OAAOA,QAAQ,EAAG;QACnC;MACD;MAEA,MAAM3Y,IAAI,GAAG,IAAI;MACjB,MAAM4Y,IAAI,GAAG9Z,MAAM,CAACoQ,IAAI,CAAEyJ,QAAS,CAAC;MAEpCC,IAAI,CAAC/Y,OAAO,CAAImY,GAAG,IAAM;QACxB,MAAMa,IAAI,GAAG7Y,IAAI,CAACjC,GAAG,CAACM,IAAI,CACzB,2BAA2B,GAAG2Z,GAAG,CAAC7C,OAAO,CAAE,GAAG,EAAE,GAAI,CAAC,GAAG,2BACzD,CAAC;QACD,IAAI2D,UAAU,GAAG,EAAE;QAEnB,IAAK,CAAE,QAAQ,EAAE,QAAQ,CAAE,CAAC3Z,QAAQ,CAAE,OAAOwZ,QAAQ,CAAEX,GAAG,CAAG,CAAC,EAAG;UAChEc,UAAU,GAAGH,QAAQ,CAAEX,GAAG,CAAE;QAC7B;QAEAa,IAAI,CAACE,OAAO,CAAED,UAAW,CAAC;QAC1Bzb,GAAG,CAACkB,QAAQ,CAAE,QAAQ,EAAEsa,IAAK,CAAC;MAC/B,CAAE,CAAC;MAEH,IAAI,CAAC5F,aAAa,CAAC,CAAC;IACrB,CAAC;IAED+F,YAAY,EAAE,SAAAA,CAAA,EAAY;MACzB;MACA,IAAIC,EAAE,GAAG5b,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;;MAE7B;MACA,IAAI2C,MAAM,GAAG,IAAI,CAAC8M,SAAS,CAAC,CAAC;MAC7B,IAAK9M,MAAM,EAAG;QACbyX,EAAE,GAAGrH,QAAQ,CAAEpQ,MAAM,CAAC+D,IAAI,CAAE,IAAK,CAAE,CAAC,IAAI/D,MAAM,CAAC+D,IAAI,CAAE,KAAM,CAAC;MAC7D;;MAEA;MACA,IAAI,CAACA,IAAI,CAAE,QAAQ,EAAE0T,EAAG,CAAC;IAC1B,CAAC;IAEDhG,aAAa,EAAE,SAAAA,CAAA,EAAY;MAC1B,MAAMrN,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;MAClC,MAAM7F,KAAK,GAAG6F,SAAS,CAACvH,IAAI,CAAE,sDAAuD,CAAC;MAEtF0B,KAAK,CAACE,IAAI,CAAE,YAAY;QACvB,MAAMiZ,WAAW,GAAG/b,CAAC,CAAE,IAAK,CAAC;QAC7B,MAAMgc,OAAO,GAAGD,WAAW,CAAC7a,IAAI,CAAE,gCAAiC,CAAC,CAACd,IAAI,CAAE,WAAY,CAAC;QACxF,MAAM6b,QAAQ,GAAGxT,SAAS,CAACvH,IAAI,CAAE,qBAAqB,GAAG8a,OAAQ,CAAC,CAACzV,KAAK,CAAC,CAAC;QAE1E,IAAKvG,CAAC,CAACqG,IAAI,CAAE0V,WAAW,CAAC9X,IAAI,CAAC,CAAE,CAAC,KAAK,EAAE,EAAG;UAC1CgY,QAAQ,CAAC7X,IAAI,CAAC,CAAC;QAChB,CAAC,MAAM,IAAK6X,QAAQ,CAACvN,EAAE,CAAE,SAAU,CAAC,EAAG;UACtCuN,QAAQ,CAAC9X,IAAI,CAAC,CAAC;QAChB;MACD,CAAE,CAAC;IACJ;EACD,CAAE,CAAC;AACJ,CAAC,EAAIoD,MAAO,CAAC;;;;;;;;;;ACljCb,CAAE,UAAWvH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECC,GAAG,CAAC+N,eAAe,GAAG,UAAWjH,GAAG,EAAG;IACtC,OAAO9G,GAAG,CAACuX,gBAAgB,CAAE;MAC5BzQ,GAAG,EAAEA,GAAG;MACRoK,KAAK,EAAE;IACR,CAAE,CAAC;EACJ,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEClR,GAAG,CAACuX,gBAAgB,GAAG,UAAW9T,IAAI,EAAG;IACxC;IACAA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAImF,QAAQ,GAAG,mBAAmB;IAClC,IAAI0O,OAAO,GAAG,KAAK;;IAEnB;IACA7T,IAAI,GAAGzD,GAAG,CAAC0D,SAAS,CAAED,IAAI,EAAE;MAC3ByJ,EAAE,EAAE,EAAE;MACNpG,GAAG,EAAE,EAAE;MACPpC,IAAI,EAAE,EAAE;MACRwM,KAAK,EAAE,KAAK;MACZ8K,IAAI,EAAE,IAAI;MACV7X,MAAM,EAAE,KAAK;MACbqT,OAAO,EAAE,KAAK;MACdnH,KAAK,EAAE;IACR,CAAE,CAAC;;IAEH;IACA,IAAK5M,IAAI,CAACyJ,EAAE,EAAG;MACdtE,QAAQ,IAAI,YAAY,GAAGnF,IAAI,CAACyJ,EAAE,GAAG,IAAI;IAC1C;;IAEA;IACA,IAAKzJ,IAAI,CAACqD,GAAG,EAAG;MACf8B,QAAQ,IAAI,aAAa,GAAGnF,IAAI,CAACqD,GAAG,GAAG,IAAI;IAC5C;;IAEA;IACA,IAAKrD,IAAI,CAACiB,IAAI,EAAG;MAChBkE,QAAQ,IAAI,cAAc,GAAGnF,IAAI,CAACiB,IAAI,GAAG,IAAI;IAC9C;;IAEA;IACA,IAAKjB,IAAI,CAACuY,IAAI,EAAG;MAChB1E,OAAO,GAAG7T,IAAI,CAACuY,IAAI,CAAC/L,QAAQ,CAAErH,QAAS,CAAC;IACzC,CAAC,MAAM,IAAKnF,IAAI,CAACU,MAAM,EAAG;MACzBmT,OAAO,GAAG7T,IAAI,CAACU,MAAM,CAACnD,IAAI,CAAE4H,QAAS,CAAC;IACvC,CAAC,MAAM,IAAKnF,IAAI,CAAC+T,OAAO,EAAG;MAC1BF,OAAO,GAAG7T,IAAI,CAAC+T,OAAO,CAAC5H,QAAQ,CAAEhH,QAAS,CAAC;IAC5C,CAAC,MAAM,IAAKnF,IAAI,CAAC4M,KAAK,EAAG;MACxBiH,OAAO,GAAG7T,IAAI,CAAC4M,KAAK,CAACuD,OAAO,CAAEhL,QAAS,CAAC;IACzC,CAAC,MAAM;MACN0O,OAAO,GAAGxX,CAAC,CAAE8I,QAAS,CAAC;IACxB;;IAEA;IACA,IAAKnF,IAAI,CAACyN,KAAK,EAAG;MACjBoG,OAAO,GAAGA,OAAO,CAACrS,KAAK,CAAE,CAAC,EAAExB,IAAI,CAACyN,KAAM,CAAC;IACzC;;IAEA;IACA,OAAOoG,OAAO;EACf,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECtX,GAAG,CAAC2H,cAAc,GAAG,UAAWD,MAAM,EAAG;IACxC;IACA,IAAK,OAAOA,MAAM,KAAK,QAAQ,EAAG;MACjCA,MAAM,GAAG1H,GAAG,CAAC+N,eAAe,CAAErG,MAAO,CAAC;IACvC;;IAEA;IACA,IAAImD,KAAK,GAAGnD,MAAM,CAACxH,IAAI,CAAE,KAAM,CAAC;IAChC,IAAK,CAAE2K,KAAK,EAAG;MACdA,KAAK,GAAG7K,GAAG,CAACic,cAAc,CAAEvU,MAAO,CAAC;IACrC;;IAEA;IACA,OAAOmD,KAAK;EACb,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC7K,GAAG,CAAC+M,eAAe,GAAG,UAAWtJ,IAAI,EAAG;IACvC;IACA,IAAI6T,OAAO,GAAGtX,GAAG,CAACuX,gBAAgB,CAAE9T,IAAK,CAAC;;IAE1C;IACA,IAAIyY,MAAM,GAAG,EAAE;IACf5E,OAAO,CAAC1U,IAAI,CAAE,YAAY;MACzB,IAAIiI,KAAK,GAAG7K,GAAG,CAAC2H,cAAc,CAAE7H,CAAC,CAAE,IAAK,CAAE,CAAC;MAC3Coc,MAAM,CAACrO,IAAI,CAAEhD,KAAM,CAAC;IACrB,CAAE,CAAC;;IAEH;IACA,OAAOqR,MAAM;EACd,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEClc,GAAG,CAACic,cAAc,GAAG,UAAWvU,MAAM,EAAG;IACxC;IACA,IAAImD,KAAK,GAAG,IAAI7K,GAAG,CAACuQ,WAAW,CAAE7I,MAAO,CAAC;;IAEzC;IACA1H,GAAG,CAACkB,QAAQ,CAAE,kBAAkB,EAAE2J,KAAM,CAAC;;IAEzC;IACA,OAAOA,KAAK;EACb,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIsR,YAAY,GAAG,IAAInc,GAAG,CAACgK,KAAK,CAAE;IACjCoS,QAAQ,EAAE,CAAC;IAEXvb,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAIoJ,OAAO,GAAG,CAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAE;;MAExD;MACAA,OAAO,CAAC+C,GAAG,CAAE,UAAWgM,MAAM,EAAG;QAChC,IAAI,CAACqD,eAAe,CAAErD,MAAO,CAAC;MAC/B,CAAC,EAAE,IAAK,CAAC;IACV,CAAC;IAEDqD,eAAe,EAAE,SAAAA,CAAWrD,MAAM,EAAG;MACpC;MACA,IAAIsD,YAAY,GAAGtD,MAAM,GAAG,gBAAgB,CAAC,CAAC;MAC9C,IAAIuD,YAAY,GAAGvD,MAAM,GAAG,eAAe,CAAC,CAAC;MAC7C,IAAIwD,WAAW,GAAGxD,MAAM,GAAG,aAAa,CAAC,CAAC;;MAE1C;MACA,IAAIjQ,QAAQ,GAAG,SAAAA,CAAWrI,GAAG,CAAC,uBAAwB;QACrD;QACA,IAAI+b,YAAY,GAAGzc,GAAG,CAAC+M,eAAe,CAAE;UAAE5I,MAAM,EAAEzD;QAAI,CAAE,CAAC;;QAEzD;QACA,IAAK+b,YAAY,CAACla,MAAM,EAAG;UAC1B;UACA,IAAIkB,IAAI,GAAGzD,GAAG,CAAC0c,SAAS,CAAExT,SAAU,CAAC;;UAErC;UACAzF,IAAI,CAACiF,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE4T,YAAY,EAAEG,YAAa,CAAC;UAC/Czc,GAAG,CAACkB,QAAQ,CAAC+H,KAAK,CAAE,IAAI,EAAExF,IAAK,CAAC;QACjC;MACD,CAAC;;MAED;MACA,IAAIkZ,cAAc,GAAG,SAAAA,CACpBF,YAAY,CAAC,uBACZ;QACD;QACA,IAAIhZ,IAAI,GAAGzD,GAAG,CAAC0c,SAAS,CAAExT,SAAU,CAAC;;QAErC;QACAzF,IAAI,CAACmZ,OAAO,CAAEL,YAAa,CAAC;;QAE5B;QACAE,YAAY,CAACzP,GAAG,CAAE,UAAWvI,WAAW,EAAG;UAC1C;UACAhB,IAAI,CAAE,CAAC,CAAE,GAAGgB,WAAW;UACvBzE,GAAG,CAACkB,QAAQ,CAAC+H,KAAK,CAAE,IAAI,EAAExF,IAAK,CAAC;QACjC,CAAE,CAAC;MACJ,CAAC;;MAED;MACA,IAAIoZ,cAAc,GAAG,SAAAA,CACpBpY,WAAW,CAAC,uBACX;QACD;QACA,IAAIhB,IAAI,GAAGzD,GAAG,CAAC0c,SAAS,CAAExT,SAAU,CAAC;;QAErC;QACAzF,IAAI,CAACmZ,OAAO,CAAEL,YAAa,CAAC;;QAE5B;QACA,IAAIO,UAAU,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAE;QAC1CA,UAAU,CAAC9P,GAAG,CAAE,UAAW+P,SAAS,EAAG;UACtCtZ,IAAI,CAAE,CAAC,CAAE,GACR8Y,YAAY,GACZ,GAAG,GACHQ,SAAS,GACT,GAAG,GACHtY,WAAW,CAACjD,GAAG,CAAEub,SAAU,CAAC;UAC7B/c,GAAG,CAACkB,QAAQ,CAAC+H,KAAK,CAAE,IAAI,EAAExF,IAAK,CAAC;QACjC,CAAE,CAAC;;QAEH;QACAA,IAAI,CAACiF,MAAM,CAAE,CAAC,EAAE,CAAE,CAAC;;QAEnB;QACAjE,WAAW,CAACgB,OAAO,CAAE+W,WAAW,EAAE/Y,IAAK,CAAC;MACzC,CAAC;;MAED;MACAzD,GAAG,CAAC2O,SAAS,CAAEqK,MAAM,EAAEjQ,QAAQ,EAAE,CAAE,CAAC;MACpC/I,GAAG,CAAC2O,SAAS,CAAE2N,YAAY,EAAEK,cAAc,EAAE,CAAE,CAAC;MAChD3c,GAAG,CAAC2O,SAAS,CAAE4N,YAAY,EAAEM,cAAc,EAAE,CAAE,CAAC;IACjD;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIG,YAAY,GAAG,IAAIhd,GAAG,CAACgK,KAAK,CAAE;IACjCkD,EAAE,EAAE,cAAc;IAElB5M,MAAM,EAAE;MACP,cAAc,EAAE,UAAU;MAC1B,4BAA4B,EAAE,iBAAiB;MAC/C,kBAAkB,EAAE;IACrB,CAAC;IAED2J,OAAO,EAAE;MACRgT,oBAAoB,EAAE,gBAAgB;MACtCtS,qBAAqB,EAAE,gBAAgB;MACvCL,mBAAmB,EAAE,eAAe;MACpCC,wBAAwB,EAAE,mBAAmB;MAC7CF,sBAAsB,EAAE;IACzB,CAAC;IAED6S,QAAQ,EAAE,SAAAA,CAAWrX,CAAC,EAAEnF,GAAG,EAAG;MAC7B;MACA,IAAIwb,MAAM,GAAGlc,GAAG,CAAC+M,eAAe,CAAC,CAAC;;MAElC;MACAmP,MAAM,CAAClP,GAAG,CAAE,UAAWnC,KAAK,EAAG;QAC9BA,KAAK,CAACoL,MAAM,CAAC,CAAC;MACf,CAAE,CAAC;IACJ,CAAC;IAEDkH,iBAAiB,EAAE,SAAAA,CAAWtS,KAAK,EAAG;MACrC,IAAI,CAACuS,YAAY,CAAEvS,KAAK,CAACnK,GAAG,CAACyD,MAAM,CAAC,CAAE,CAAC;IACxC,CAAC;IAEDkZ,eAAe,EAAE,SAAAA,CAAWxX,CAAC,EAAEnF,GAAG,EAAG;MACpC;MACA,IAAKA,GAAG,CAAC4R,QAAQ,CAAE,aAAc,CAAC,EAAG;;MAErC;MACA5R,GAAG,CAAC4c,QAAQ,CAAE;QACbC,MAAM,EAAE,SAAAA,CAAUjU,KAAK,EAAEgK,OAAO,EAAG;UAClC;UACA,OAAOA,OAAO,CAAC/E,KAAK,CAAC,CAAC,CACpBvN,IAAI,CAAE,QAAS,CAAC,CACfgD,IAAI,CAAE,MAAM,EAAE,UAAUkU,CAAC,EAAEsF,WAAW,EAAG;YACxC,OAAO,OAAO,GAAGjJ,QAAQ,CAAEkJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAM,EAAE,EAAG,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAGH,WAAW;UACxF,CAAE,CAAC,CACHzF,GAAG,CAAC,CAAC;QACR,CAAC;QACD6F,MAAM,EAAE,sBAAsB;QAC9BC,WAAW,EAAE,iBAAiB;QAC9BC,KAAK,EAAE,SAAAA,CAAWjY,CAAC,EAAEkY,EAAE,EAAG;UACzB,IAAIlT,KAAK,GAAG7K,GAAG,CAAC2H,cAAc,CAAEoW,EAAE,CAACC,IAAK,CAAC;UACzCD,EAAE,CAACE,WAAW,CAACC,MAAM,CAAEH,EAAE,CAACC,IAAI,CAACE,MAAM,CAAC,CAAE,CAAC;UACzCle,GAAG,CAACkB,QAAQ,CAAE,wBAAwB,EAAE2J,KAAK,EAAEnK,GAAI,CAAC;QACrD,CAAC;QACDyd,MAAM,EAAE,SAAAA,CAAWtY,CAAC,EAAEkY,EAAE,EAAG;UAC1B,IAAIlT,KAAK,GAAG7K,GAAG,CAAC2H,cAAc,CAAEoW,EAAE,CAACC,IAAK,CAAC;UACzChe,GAAG,CAACkB,QAAQ,CAAE,uBAAuB,EAAE2J,KAAK,EAAEnK,GAAI,CAAC;QACpD;MACD,CAAE,CAAC;IACJ,CAAC;IAED0d,cAAc,EAAE,SAAAA,CAAWvT,KAAK,EAAEwM,KAAK,EAAG;MACzC,IAAI,CAAC+F,YAAY,CAAE/F,KAAM,CAAC;IAC3B,CAAC;IAEDgH,cAAc,EAAE,SAAAA,CAAWxT,KAAK,EAAEwM,KAAK,EAAG;MACzCxM,KAAK,CAAC8Q,YAAY,CAAC,CAAC;MACpB,IAAI,CAACyB,YAAY,CAAE/F,KAAM,CAAC;IAC3B,CAAC;IAEDiH,aAAa,EAAE,SAAAA,CAAWzT,KAAK,EAAG;MACjC;MACAA,KAAK,CAACuG,SAAS,CAAC,CAAC,CAACpE,GAAG,CAAE,UAAWqD,KAAK,EAAG;QACzCA,KAAK,CAACtI,MAAM,CAAE;UAAED,OAAO,EAAE;QAAM,CAAE,CAAC;MACnC,CAAE,CAAC;IACJ,CAAC;IAED7E,iBAAiB,EAAE,SAAAA,CAAW4H,KAAK,EAAG;MACrC;MACAA,KAAK,CAACnK,GAAG,CAACM,IAAI,CAAE,sBAAuB,CAAC,CAACkH,IAAI,CAAE,UAAU,EAAE,KAAM,CAAC;IACnE,CAAC;IAEDqW,gBAAgB,EAAE,SAAAA,CAAW1T,KAAK,EAAEqF,QAAQ,EAAG;MAC9C;MACA,IAAID,QAAQ,GAAGC,QAAQ,CAACkB,SAAS,CAAC,CAAC;MACnC,IAAKnB,QAAQ,CAAC1N,MAAM,EAAG;QACtB;QACA0N,QAAQ,CAACjD,GAAG,CAAE,UAAWqD,KAAK,EAAG;UAChC;UACAA,KAAK,CAAC+H,IAAI,CAAC,CAAC;;UAEZ;UACA,IAAK/H,KAAK,CAACsE,MAAM,CAAC,CAAC,EAAG;YACrBtE,KAAK,CAACvP,IAAI,CAAC,CAAC;UACb;;UAEA;UACAuP,KAAK,CAACsL,YAAY,CAAC,CAAC;QACrB,CAAE,CAAC;;QAEH;QACA3b,GAAG,CAACkB,QAAQ,CACX,yBAAyB,EACzB+O,QAAQ,EACRC,QAAQ,EACRrF,KACD,CAAC;MACF;;MAEA;MACA,IAAI,CAACsS,iBAAiB,CAAEjN,QAAS,CAAC;IACnC,CAAC;IAEDkN,YAAY,EAAE,SAAAA,CAAW/F,KAAK,EAAG;MAChC;MACA,IAAI6E,MAAM,GAAGlc,GAAG,CAAC+M,eAAe,CAAE;QACjCiP,IAAI,EAAE3E;MACP,CAAE,CAAC;;MAEH;MACA,IAAK,CAAE6E,MAAM,CAAC3Z,MAAM,EAAG;QACtB8U,KAAK,CAAC1R,QAAQ,CAAE,QAAS,CAAC;QAC1B0R,KAAK,CACHzD,OAAO,CAAE,sBAAuB,CAAC,CACjCvN,KAAK,CAAC,CAAC,CACPV,QAAQ,CAAE,QAAS,CAAC;QACtB;MACD;;MAEA;MACA0R,KAAK,CAAC3R,WAAW,CAAE,QAAS,CAAC;MAC7B2R,KAAK,CACHzD,OAAO,CAAE,sBAAuB,CAAC,CACjCvN,KAAK,CAAC,CAAC,CACPX,WAAW,CAAE,QAAS,CAAC;;MAEzB;MACAwW,MAAM,CAAClP,GAAG,CAAE,UAAWnC,KAAK,EAAEqN,CAAC,EAAG;QACjCrN,KAAK,CAAC3C,IAAI,CAAE,YAAY,EAAEgQ,CAAE,CAAC;MAC9B,CAAE,CAAC;IACJ,CAAC;IAEDxI,UAAU,EAAE,SAAAA,CAAW7J,CAAC,EAAEnF,GAAG,EAAG;MAC/B,IAAI2W,KAAK;MAET,IAAK3W,GAAG,CAAC4R,QAAQ,CAAE,iBAAkB,CAAC,EAAG;QACxC+E,KAAK,GAAG3W,GAAG,CAACkT,OAAO,CAAE,iBAAkB,CAAC,CAAC4K,EAAE,CAAE,CAAE,CAAC;MACjD,CAAC,MAAM,IACN9d,GAAG,CAACyD,MAAM,CAAC,CAAC,CAACmO,QAAQ,CAAE,uBAAwB,CAAC,IAChD5R,GAAG,CAACyD,MAAM,CAAC,CAAC,CAACmO,QAAQ,CAAE,yBAA0B,CAAC,EACjD;QACD+E,KAAK,GAAGvX,CAAC,CAAE,uBAAwB,CAAC;MACrC,CAAC,MAAM,IAAKY,GAAG,CAACyD,MAAM,CAAC,CAAC,CAACmO,QAAQ,CAAE,2BAA4B,CAAC,EAAG;QAClE+E,KAAK,GAAG3W,GAAG,CACTkT,OAAO,CAAE,kBAAmB,CAAC,CAC7B5S,IAAI,CAAE,uBAAwB,CAAC;MAClC,CAAC,MAAM;QACNqW,KAAK,GAAG3W,GAAG,CACTkJ,OAAO,CAAE,YAAa,CAAC,CACvBgG,QAAQ,CAAE,iBAAkB,CAAC;MAChC;MAEA,IAAI,CAAC6O,QAAQ,CAAEpH,KAAM,CAAC;IACvB,CAAC;IAEDoH,QAAQ,EAAE,SAAAA,CAAWpH,KAAK,EAAG;MAC5B;MACA,IAAIlW,IAAI,GAAGrB,CAAC,CAAE,iBAAkB,CAAC,CAACqB,IAAI,CAAC,CAAC;MACxC,IAAIT,GAAG,GAAGZ,CAAC,CAAEqB,IAAK,CAAC;MACnB,IAAIkX,MAAM,GAAG3X,GAAG,CAACR,IAAI,CAAE,IAAK,CAAC;MAC7B,IAAIyX,MAAM,GAAG3X,GAAG,CAAC4X,MAAM,CAAE,QAAS,CAAC;;MAEnC;MACA,IAAIC,SAAS,GAAG7X,GAAG,CAACoP,SAAS,CAAE;QAC9BuE,MAAM,EAAEjT,GAAG;QACXY,MAAM,EAAE+W,MAAM;QACdP,OAAO,EAAEH,MAAM;QACf9U,MAAM,EAAE,SAAAA,CAAWnC,GAAG,EAAEge,IAAI,EAAG;UAC9BrH,KAAK,CAACxU,MAAM,CAAE6b,IAAK,CAAC;QACrB;MACD,CAAE,CAAC;;MAEH;MACA,IAAIxO,QAAQ,GAAGlQ,GAAG,CAAC2H,cAAc,CAAEkQ,SAAU,CAAC;;MAE9C;MACA3H,QAAQ,CAAChI,IAAI,CAAE,KAAK,EAAEyP,MAAO,CAAC;MAC9BzH,QAAQ,CAAChI,IAAI,CAAE,IAAI,EAAE,CAAE,CAAC;MACxBgI,QAAQ,CAAChI,IAAI,CAAE,OAAO,EAAE,EAAG,CAAC;MAC5BgI,QAAQ,CAAChI,IAAI,CAAE,MAAM,EAAE,EAAG,CAAC;;MAE3B;MACA2P,SAAS,CAAC7T,IAAI,CAAE,UAAU,EAAE2T,MAAO,CAAC;MACpCE,SAAS,CAAC7T,IAAI,CAAE,SAAS,EAAE2T,MAAO,CAAC;;MAEnC;MACAzH,QAAQ,CAACyL,YAAY,CAAC,CAAC;;MAEvB;MACA,IAAIgD,KAAK,GAAGzO,QAAQ,CAACvE,MAAM,CAAE,MAAO,CAAC;MACrCxG,UAAU,CAAE,YAAY;QACvB,IAAKkS,KAAK,CAAC/E,QAAQ,CAAE,oBAAqB,CAAC,EAAG;UAC7C+E,KAAK,CAAC3R,WAAW,CAAE,oBAAqB,CAAC;QAC1C,CAAC,MAAM;UACNiZ,KAAK,CAAClZ,OAAO,CAAE,OAAQ,CAAC;QACzB;MACD,CAAC,EAAE,GAAI,CAAC;;MAER;MACAyK,QAAQ,CAACpP,IAAI,CAAC,CAAC;;MAEf;MACA,IAAI,CAACsc,YAAY,CAAE/F,KAAM,CAAC;;MAE1B;MACArX,GAAG,CAACkB,QAAQ,CAAE,kBAAkB,EAAEgP,QAAS,CAAC;MAC5ClQ,GAAG,CAACkB,QAAQ,CAAE,qBAAqB,EAAEgP,QAAS,CAAC;IAChD;EACD,CAAE,CAAC;AACJ,CAAC,EAAI7I,MAAO,CAAC;;;;;;;;;;AChfb,CAAE,UAAWvH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI6e,eAAe,GAAG,IAAI5e,GAAG,CAACgK,KAAK,CAAE;IACpCkD,EAAE,EAAE,iBAAiB;IACrB2R,IAAI,EAAE,OAAO;IAEbve,MAAM,EAAE;MACP,0BAA0B,EAAE,gBAAgB;MAC5C,2BAA2B,EAAE,iBAAiB;MAC9C,6BAA6B,EAAE,mBAAmB;MAClD,+BAA+B,EAAE;IAClC,CAAC;IAEDO,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACH,GAAG,GAAGZ,CAAC,CAAE,0BAA2B,CAAC;MAC1C,IAAI,CAACgf,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDD,eAAe,EAAE,SAAAA,CAAA,EAAY;MAC5B;MACA,IAAK9e,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,IAAIxB,GAAG,CAACwB,GAAG,CAAE,iBAAkB,CAAC,EAAG;QAC1D;MACD;;MAEA;MACA,MAAMwd,gBAAgB,GAAGhf,GAAG,CAACwB,GAAG,CAAE,kBAAmB,CAAC;MACtD,IAAK,OAAOwd,gBAAgB,KAAK,QAAQ,EAAG;MAE5C,MAAMC,WAAW,GAAG,IAAI,CAACve,GAAG,CAC1BM,IAAI,CAAE,8BAA+B,CAAC,CACtCA,IAAI,CAAE,yBAA0B,CAAC;MAEnC,MAAMke,WAAW,GAAG,KAAKlf,GAAG,CAACqN,EAAE,CAAE,UAAW,CAAC,GAAG;MAEhD,KAAM,MAAM,CAAEvG,GAAG,EAAE/E,IAAI,CAAE,IAAIN,MAAM,CAACyS,OAAO,CAAE8K,gBAAiB,CAAC,EAAG;QACjE,IAAK,CAAEhf,GAAG,CAACwB,GAAG,CAAE,QAAS,CAAC,EAAG;UAC5Byd,WAAW,CAACpc,MAAM,CACjB,4CAA4C7C,GAAG,CAACyT,SAAS,CAAE1R,IAAK,CAAC,GAAG/B,GAAG,CAACyT,SAAS,CAAEyL,WAAY,CAAC,WACjG,CAAC;QACF,CAAC,MAAM;UACND,WAAW,CACTje,IAAI,CAAE,eAAe,GAAG8F,GAAG,GAAG,GAAI,CAAC,CAACuI,GAAG,CAAE,WAAY,CAAC,CACtDnH,IAAI,CAAE,UAAU,EAAE,UAAW,CAAC,CAC9BnE,IAAI,CAAE,GAAG/D,GAAG,CAACyT,SAAS,CAAE1R,IAAK,CAAC,GAAG/B,GAAG,CAACyT,SAAS,CAAEyL,WAAY,CAAC,EAAG,CAAC;QACpE;MACD;MAEA,MAAMC,kBAAkB,GAAG,IAAI,CAACze,GAAG,CAACM,IAAI,CAAE,+DAAgE,CAAC;MAC3G,IAAKme,kBAAkB,CAAC5c,MAAM,EAAG;QAChC4c,kBAAkB,CAACnb,IAAI,CAAE,UAAU,EAAE,UAAW,CAAC;MAClD;IACD,CAAC;IAEDob,cAAc,EAAE,SAAAA,CAAWvZ,CAAC,EAAEnF,GAAG,EAAG;MACnC,IAAI,CAAC2e,OAAO,CAAE3e,GAAG,CAACkJ,OAAO,CAAE,IAAK,CAAE,CAAC;IACpC,CAAC;IAED0V,iBAAiB,EAAE,SAAAA,CAAWzZ,CAAC,EAAEnF,GAAG,EAAG;MACtC,IAAI,CAAC6e,UAAU,CAAE7e,GAAG,CAACkJ,OAAO,CAAE,IAAK,CAAE,CAAC;IACvC,CAAC;IAED4V,kBAAkB,EAAE,SAAAA,CAAW3Z,CAAC,EAAEnF,GAAG,EAAG;MACvC,IAAI,CAAC+e,UAAU,CAAE/e,GAAG,CAACkJ,OAAO,CAAE,IAAK,CAAE,CAAC;IACvC,CAAC;IAEDoF,eAAe,EAAE,SAAAA,CAAWnJ,CAAC,EAAEnF,GAAG,EAAG;MACpC,IAAI,CAACuO,QAAQ,CAAC,CAAC;IAChB,CAAC;IAEDoQ,OAAO,EAAE,SAAAA,CAAW/P,GAAG,EAAG;MACzBtP,GAAG,CAACoP,SAAS,CAAEE,GAAI,CAAC;MACpB,IAAI,CAACyP,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDQ,UAAU,EAAE,SAAAA,CAAWjQ,GAAG,EAAG;MAC5B,IAAKA,GAAG,CAACM,QAAQ,CAAE,IAAK,CAAC,CAACrN,MAAM,IAAI,CAAC,EAAG;QACvC+M,GAAG,CAAC1F,OAAO,CAAE,aAAc,CAAC,CAAC5C,MAAM,CAAC,CAAC;MACtC,CAAC,MAAM;QACNsI,GAAG,CAACtI,MAAM,CAAC,CAAC;MACb;;MAEA;MACA,IAAIkI,MAAM,GAAG,IAAI,CAACpP,CAAC,CAAE,mBAAoB,CAAC;MAC1CoP,MAAM,CAAClO,IAAI,CAAE,IAAK,CAAC,CAAC+C,IAAI,CAAE/D,GAAG,CAACqN,EAAE,CAAE,0BAA2B,CAAE,CAAC;MAEhE,IAAI,CAAC0R,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDU,UAAU,EAAE,SAAAA,CAAWjU,KAAK,EAAG;MAC9B;MACA,IAAI0D,MAAM,GAAG1D,KAAK,CAAC5B,OAAO,CAAE,aAAc,CAAC;MAC3C,IAAIsR,MAAM,GAAG1P,KAAK,CAChBxK,IAAI,CAAE,iBAAkB,CAAC,CACzBgD,IAAI,CAAE,MAAO,CAAC,CACd8T,OAAO,CAAE,SAAS,EAAE,EAAG,CAAC;;MAE1B;MACA,IAAI4H,QAAQ,GAAG,CAAC,CAAC;MACjBA,QAAQ,CAAC1G,MAAM,GAAG,sCAAsC;MACxD0G,QAAQ,CAACC,IAAI,GAAG3f,GAAG,CAACgW,SAAS,CAAExK,KAAK,EAAE0P,MAAO,CAAC;MAC9CwE,QAAQ,CAACC,IAAI,CAACzS,EAAE,GAAG1B,KAAK,CAACtL,IAAI,CAAE,IAAK,CAAC;MACrCwf,QAAQ,CAACC,IAAI,CAACC,KAAK,GAAG1Q,MAAM,CAAChP,IAAI,CAAE,IAAK,CAAC;;MAEzC;MACAF,GAAG,CAACqM,OAAO,CAAEb,KAAK,CAACxK,IAAI,CAAE,UAAW,CAAE,CAAC;MAEvC,MAAM2B,IAAI,GAAG,IAAI;;MAEjB;MACA7C,CAAC,CAACgT,IAAI,CAAE;QACPzP,GAAG,EAAErD,GAAG,CAACwB,GAAG,CAAE,SAAU,CAAC;QACzBtB,IAAI,EAAEF,GAAG,CAACkZ,cAAc,CAAEwG,QAAS,CAAC;QACpChb,IAAI,EAAE,MAAM;QACZyU,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,SAAAA,CAAWjY,IAAI,EAAG;UAC1B,IAAK,CAAEA,IAAI,EAAG;UACdqK,KAAK,CAACqU,WAAW,CAAE1e,IAAK,CAAC;UACzBwB,IAAI,CAACmc,eAAe,CAAC,CAAC;QACvB;MACD,CAAE,CAAC;IACJ,CAAC;IAED7P,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACrB;MACA,IAAIC,MAAM,GAAG,IAAI,CAACpP,CAAC,CAAE,kBAAmB,CAAC;;MAEzC;MACAqP,OAAO,GAAGnP,GAAG,CAACoP,SAAS,CAAEF,MAAO,CAAC;;MAEjC;MACAC,OAAO,CAACnO,IAAI,CAAE,IAAK,CAAC,CAAC+C,IAAI,CAAE/D,GAAG,CAACqN,EAAE,CAAE,IAAK,CAAE,CAAC;;MAE3C;MACA8B,OAAO,CAACnO,IAAI,CAAE,IAAK,CAAC,CAACqO,GAAG,CAAE,QAAS,CAAC,CAACrI,MAAM,CAAC,CAAC;;MAE7C;MACA,IAAI,CAAC+X,iBAAiB,CAAC,CAAC;IACzB,CAAC;IAEDA,iBAAiB,EAAE,SAAAA,CAAA,EAAY;MAC9B,IAAI7P,MAAM,GAAG,IAAI,CAACpP,CAAC,CAAE,kBAAmB,CAAC;MAEzC,IAAIggB,WAAW,GAAG5Q,MAAM,CAACtF,OAAO,CAAE,cAAe,CAAC;MAElD,IAAImW,UAAU,GAAGD,WAAW,CAAC9e,IAAI,CAAE,eAAgB,CAAC,CAACuB,MAAM;MAE3D,IAAKwd,UAAU,GAAG,CAAC,EAAG;QACrBD,WAAW,CAACna,QAAQ,CAAE,sBAAuB,CAAC;MAC/C,CAAC,MAAM;QACNma,WAAW,CAACpa,WAAW,CAAE,sBAAuB,CAAC;MAClD;IACD;EACD,CAAE,CAAC;AACJ,CAAC,EAAI2B,MAAO,CAAC;;;;;;;;;;ACrKb,CAAE,UAAWvH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIigB,OAAO,GAAG,SAAAA,CAAWtb,IAAI,EAAG;IAC/B,OAAO1E,GAAG,CAACigB,aAAa,CAAEvb,IAAI,IAAI,EAAG,CAAC,GAAG,cAAc;EACxD,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC1E,GAAG,CAAC6P,oBAAoB,GAAG,UAAWxH,KAAK,EAAG;IAC7C,IAAI6X,KAAK,GAAG7X,KAAK,CAAC2F,SAAS;IAC3B,IAAImS,GAAG,GAAGH,OAAO,CAAEE,KAAK,CAACxb,IAAI,GAAG,GAAG,GAAGwb,KAAK,CAACne,IAAK,CAAC;IAClD,IAAI,CAACkF,MAAM,CAAEkZ,GAAG,CAAE,GAAG9X,KAAK;EAC3B,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECrI,GAAG,CAACogB,eAAe,GAAG,UAAWvV,KAAK,EAAG;IACxC;IACA,IAAInG,IAAI,GAAGmG,KAAK,CAACrJ,GAAG,CAAE,SAAU,CAAC,IAAI,EAAE;IACvC,IAAIO,IAAI,GAAG8I,KAAK,CAACrJ,GAAG,CAAE,MAAO,CAAC,IAAI,EAAE;IACpC,IAAI2e,GAAG,GAAGH,OAAO,CAAEtb,IAAI,GAAG,GAAG,GAAG3C,IAAK,CAAC;IACtC,IAAIsG,KAAK,GAAGrI,GAAG,CAACiH,MAAM,CAAEkZ,GAAG,CAAE,IAAI,IAAI;;IAErC;IACA,IAAK9X,KAAK,KAAK,IAAI,EAAG,OAAO,KAAK;;IAElC;IACA,IAAIyB,OAAO,GAAG,IAAIzB,KAAK,CAAEwC,KAAM,CAAC;;IAEhC;IACA,OAAOf,OAAO;EACf,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC9J,GAAG,CAACqgB,eAAe,GAAG,UAAWxV,KAAK,EAAG;IACxC;IACA,IAAKA,KAAK,YAAYxD,MAAM,EAAG;MAC9BwD,KAAK,GAAG7K,GAAG,CAACsgB,QAAQ,CAAEzV,KAAM,CAAC;IAC9B;;IAEA;IACA,OAAOA,KAAK,CAACf,OAAO;EACrB,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIyW,eAAe,GAAG,IAAIvgB,GAAG,CAACgK,KAAK,CAAE;IACpCC,OAAO,EAAE;MACRuW,SAAS,EAAE;IACZ,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAW5V,KAAK,EAAG;MAC9BA,KAAK,CAACf,OAAO,GAAG9J,GAAG,CAACogB,eAAe,CAAEvV,KAAM,CAAC;IAC7C;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC7K,GAAG,CAACuL,YAAY,GAAGvL,GAAG,CAACgK,KAAK,CAACvJ,MAAM,CAAE;IACpCoK,KAAK,EAAE,KAAK;IACZnG,IAAI,EAAE,EAAE;IACR3C,IAAI,EAAE,EAAE;IACR8c,IAAI,EAAE,OAAO;IACbrO,UAAU,EAAE,YAAY;IAExBlQ,MAAM,EAAE;MACPoQ,MAAM,EAAE;IACT,CAAC;IAEDnQ,KAAK,EAAE,SAAAA,CAAWsK,KAAK,EAAG;MACzB;MACA,IAAInD,MAAM,GAAGmD,KAAK,CAACnK,GAAG;;MAEtB;MACA,IAAI,CAACA,GAAG,GAAGgH,MAAM;MACjB,IAAI,CAACmD,KAAK,GAAGA,KAAK;MAClB,IAAI,CAAC6V,YAAY,GAAGhZ,MAAM,CAACkC,OAAO,CAAE,mBAAoB,CAAC;MACzD,IAAI,CAACnF,WAAW,GAAGzE,GAAG,CAAC2H,cAAc,CAAE,IAAI,CAAC+Y,YAAa,CAAC;;MAE1D;MACA5gB,CAAC,CAACW,MAAM,CAAE,IAAI,CAACP,IAAI,EAAE2K,KAAK,CAAC3K,IAAK,CAAC;IAClC,CAAC;IAEDW,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB,IAAI,CAACD,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB;IAAA;EAEF,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAI+f,oBAAoB,GAAG3gB,GAAG,CAACuL,YAAY,CAAC9K,MAAM,CAAE;IACnDiE,IAAI,EAAE,EAAE;IACR3C,IAAI,EAAE,EAAE;IACRnB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAIggB,iBAAiB,GAAG,IAAI,CAACnc,WAAW,CAACuM,QAAQ,CAAE,UAAW,CAAC;MAC/D,IAAI6P,eAAe,GAAGD,iBAAiB,CAAC5f,IAAI,CAC3C,8BACD,CAAC;MACD,IAAK6f,eAAe,CAACrS,EAAE,CAAE,UAAW,CAAC,EAAG;QACvC,IAAI,CAAC/J,WAAW,CAAC/D,GAAG,CAACiF,QAAQ,CAAE,uBAAwB,CAAC;MACzD,CAAC,MAAM;QACN,IAAI,CAAClB,WAAW,CAAC/D,GAAG,CAACgF,WAAW,CAAE,uBAAwB,CAAC;MAC5D;IACD;EACD,CAAE,CAAC;EAEH,IAAIob,6BAA6B,GAAGH,oBAAoB,CAAClgB,MAAM,CAAE;IAChEiE,IAAI,EAAE,WAAW;IACjB3C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH,IAAIgf,uBAAuB,GAAGJ,oBAAoB,CAAClgB,MAAM,CAAE;IAC1DiE,IAAI,EAAE,KAAK;IACX3C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH/B,GAAG,CAAC6P,oBAAoB,CAAEiR,6BAA8B,CAAC;EACzD9gB,GAAG,CAAC6P,oBAAoB,CAAEkR,uBAAwB,CAAC;;EAEnD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,yBAAyB,GAAGhhB,GAAG,CAACuL,YAAY,CAAC9K,MAAM,CAAE;IACxDiE,IAAI,EAAE,EAAE;IACR3C,IAAI,EAAE,EAAE;IACRnB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAI+K,MAAM,GAAG,IAAI,CAAC7L,CAAC,CAAE,6BAA8B,CAAC;MACpD,IAAK6L,MAAM,CAACpG,GAAG,CAAC,CAAC,IAAI,OAAO,EAAG;QAC9B,IAAI,CAACzF,CAAC,CAAE,oBAAqB,CAAC,CAACyF,GAAG,CAAEoG,MAAM,CAACpG,GAAG,CAAC,CAAE,CAAC;MACnD;IACD;EACD,CAAE,CAAC;EAEH,IAAI0b,mCAAmC,GAAGD,yBAAyB,CAACvgB,MAAM,CACzE;IACCiE,IAAI,EAAE,aAAa;IACnB3C,IAAI,EAAE;EACP,CACD,CAAC;EAED,IAAImf,kCAAkC,GAAGF,yBAAyB,CAACvgB,MAAM,CAAE;IAC1EiE,IAAI,EAAE,aAAa;IACnB3C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH/B,GAAG,CAAC6P,oBAAoB,CAAEoR,mCAAoC,CAAC;EAC/DjhB,GAAG,CAAC6P,oBAAoB,CAAEqR,kCAAmC,CAAC;;EAE9D;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,uCAAuC,GAC1CH,yBAAyB,CAACvgB,MAAM,CAAE;IACjCiE,IAAI,EAAE,kBAAkB;IACxB3C,IAAI,EAAE;EACP,CAAE,CAAC;EAEJ,IAAIqf,sCAAsC,GACzCJ,yBAAyB,CAACvgB,MAAM,CAAE;IACjCiE,IAAI,EAAE,kBAAkB;IACxB3C,IAAI,EAAE;EACP,CAAE,CAAC;EAEJ/B,GAAG,CAAC6P,oBAAoB,CAAEsR,uCAAwC,CAAC;EACnEnhB,GAAG,CAAC6P,oBAAoB,CAAEuR,sCAAuC,CAAC;;EAElE;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,mCAAmC,GAAGL,yBAAyB,CAACvgB,MAAM,CACzE;IACCiE,IAAI,EAAE,aAAa;IACnB3C,IAAI,EAAE;EACP,CACD,CAAC;EAED,IAAIuf,kCAAkC,GAAGN,yBAAyB,CAACvgB,MAAM,CAAE;IAC1EiE,IAAI,EAAE,aAAa;IACnB3C,IAAI,EAAE;EACP,CAAE,CAAC;EAEH/B,GAAG,CAAC6P,oBAAoB,CAAEwR,mCAAoC,CAAC;EAC/DrhB,GAAG,CAAC6P,oBAAoB,CAAEyR,kCAAmC,CAAC;;EAE9D;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,uBAAuB,GAAGvhB,GAAG,CAACuL,YAAY,CAAC9K,MAAM,CAAE;IACtDiE,IAAI,EAAE,cAAc;IACpB3C,IAAI,EAAE,gBAAgB;IACtBnB,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAI4gB,sBAAsB,GACzB,IAAI,CAAC/c,WAAW,CAACuM,QAAQ,CAAE,eAAgB,CAAC;MAC7C,IAAIyQ,sBAAsB,GACzB,IAAI,CAAChd,WAAW,CAACuM,QAAQ,CAAE,eAAgB,CAAC;MAC7C,IAAI0Q,UAAU,GAAGF,sBAAsB,CACrCxgB,IAAI,CAAE,qCAAsC,CAAC,CAC7CmD,MAAM,CAAE,OAAQ,CAAC,CACjBwd,QAAQ,CAAC,CAAC,CACVC,IAAI,CAAC,CAAC;MACR,IAAIC,mBAAmB,GACtBJ,sBAAsB,CAACzgB,IAAI,CAAE,oBAAqB,CAAC;MACpD,IAAI8gB,IAAI,GAAG9hB,GAAG,CAACwB,GAAG,CAAE,iBAAkB,CAAC;MAEvC,IAAK,IAAI,CAACqJ,KAAK,CAACtF,GAAG,CAAC,CAAC,EAAG;QACvBmc,UAAU,CAAC7B,WAAW,CAAEiC,IAAI,CAACC,WAAY,CAAC;QAC1CF,mBAAmB,CAAC7d,IAAI,CACvB,aAAa,EACb,uBACD,CAAC;MACF,CAAC,MAAM;QACN0d,UAAU,CAAC7B,WAAW,CAAEiC,IAAI,CAACE,UAAW,CAAC;QACzCH,mBAAmB,CAAC7d,IAAI,CAAE,aAAa,EAAE,SAAU,CAAC;MACrD;IACD;EACD,CAAE,CAAC;EACHhE,GAAG,CAAC6P,oBAAoB,CAAE0R,uBAAwB,CAAC;AACpD,CAAC,EAAIla,MAAO,CAAC;;;;;;;;;;ACtTb,CAAE,UAAWvH,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIkiB,iBAAiB,GAAG,IAAIjiB,GAAG,CAACgK,KAAK,CAAE;IACtCkD,EAAE,EAAE,mBAAmB;IAEvB5M,MAAM,EAAE;MACP,cAAc,EAAE,UAAU;MAC1B,mBAAmB,EAAE,SAAS;MAC9B,+BAA+B,EAAE,yBAAyB;MAC1D,kBAAkB,EAAE,eAAe;MACnC,mBAAmB,EAAE;IACtB,CAAC;IAED4hB,OAAO,EAAE;MACRC,gBAAgB,EAAE,qBAAqB;MACvCC,oBAAoB,EAAE;IACvB,CAAC;IAEDvhB,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvBb,GAAG,CAAC2O,SAAS,CAAE,SAAS,EAAE,IAAI,CAAC0T,sBAAuB,CAAC;MACvDriB,GAAG,CAACoJ,UAAU,CAAE,cAAc,EAAE,IAAI,CAACkZ,2BAA4B,CAAC;MAClEtiB,GAAG,CAACoJ,UAAU,CACb,mBAAmB,EACnB,IAAI,CAACmZ,mCACN,CAAC;IACF,CAAC;IAEDD,2BAA2B,EAAE,SAAAA,CAC5B7e,IAAI,EACJqJ,OAAO,EACPwO,QAAQ,EACRzQ,KAAK,EACL2X,QAAQ,EACP;MAAA,IAAAC,WAAA;MACD,IAAK,CAAA5X,KAAK,aAALA,KAAK,gBAAA4X,WAAA,GAAL5X,KAAK,CAAE3K,IAAI,cAAAuiB,WAAA,uBAAXA,WAAA,CAAAC,IAAA,CAAA7X,KAAK,EAAU,KAAM,CAAC,MAAK,sBAAsB,EAAG,OAAOpH,IAAI;MAEpEA,IAAI,CAACyP,gBAAgB,GAAG,2BAA2B;;MAEnD;MACA,IAAI;QACHpT,CAAC,CAACyS,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,OAAO,CAAE,4BAA6B,CAAC;MACzD,CAAC,CAAC,OAAQC,GAAG,EAAG;QACfC,OAAO,CAACC,IAAI,CACX,sLACD,CAAC;QACD,OAAOpP,IAAI,CAACyP,gBAAgB;MAC7B;MAEAzP,IAAI,CAAC0P,cAAc,GAAG,UAAWC,SAAS,EAAG;QAC5C,IAAK,WAAW,KAAK,OAAOA,SAAS,CAACE,OAAO,EAAG;UAC/C,OAAOF,SAAS;QACjB;QAEA,IAAKA,SAAS,CAACnD,QAAQ,EAAG;UACzB,OAAOmD,SAAS,CAACrP,IAAI;QACtB;QAEA,IACCqP,SAAS,CAACC,OAAO,IACfD,SAAS,CAACE,OAAO,IAClBF,SAAS,CAACE,OAAO,CAACC,QAAQ,KAAK,UAAY,EAC3C;UACD,IAAIC,UAAU,GAAG1T,CAAC,CAAE,qCAAsC,CAAC;UAC3D0T,UAAU,CAACrS,IAAI,CAAEnB,GAAG,CAAC2iB,OAAO,CAAEvP,SAAS,CAACrP,IAAK,CAAE,CAAC;UAChD,OAAOyP,UAAU;QAClB;QAEA,IACC,WAAW,KAAK,OAAOJ,SAAS,CAACwP,gBAAgB,IACjD,WAAW,KAAK,OAAOxP,SAAS,CAACyP,UAAU,IAC3C,WAAW,KAAK,OAAOzP,SAAS,CAAC0P,UAAU,EAC1C;UACD,OAAO1P,SAAS,CAACrP,IAAI;QACtB;QAEA,IAAIyP,UAAU,GAAG1T,CAAC,CACjB,YAAY,GACXE,GAAG,CAAC2iB,OAAO,CAAEvP,SAAS,CAACwP,gBAAiB,CAAC,GACzC,2CAA2C,GAC3C5iB,GAAG,CAAC2iB,OAAO,CACVvP,SAAS,CAACyP,UAAU,CAAC1f,UAAU,CAAE,GAAG,EAAE,GAAI,CAC3C,CAAC,GACD,6CAA6C,GAC7CnD,GAAG,CAAC2iB,OAAO,CAAEvP,SAAS,CAACrP,IAAK,CAAC,GAC7B,SACF,CAAC;QACD,IAAKqP,SAAS,CAAC0P,UAAU,EAAG;UAC3BtP,UAAU,CACRoO,IAAI,CAAC,CAAC,CACN/e,MAAM,CACN,yCAAyC,GACxC7C,GAAG,CAACqN,EAAE,CAAE,YAAa,CAAC,GACtB,SACF,CAAC;QACH;QACAmG,UAAU,CAACtT,IAAI,CAAE,SAAS,EAAEkT,SAAS,CAACE,OAAQ,CAAC;QAC/C,OAAOE,UAAU;MAClB,CAAC;MAED,OAAO/P,IAAI;IACZ,CAAC;IAED8e,mCAAmC,EAAE,SAAAA,CACpCriB,IAAI,EACJuD,IAAI,EACJkI,MAAM,EACNd,KAAK,EACL2X,QAAQ,EACP;MACD,IAAKtiB,IAAI,CAAC6iB,SAAS,KAAK,sBAAsB,EAAG,OAAO7iB,IAAI;MAE5D,MAAMwgB,YAAY,GAAG1gB,GAAG,CAACuX,gBAAgB,CAAE;QAAElH,KAAK,EAAExF;MAAM,CAAE,CAAC;MAC7D,MAAMpG,WAAW,GAAGzE,GAAG,CAAC2H,cAAc,CAAE+Y,YAAa,CAAC;MACtDxgB,IAAI,CAAC6iB,SAAS,GAAG,2BAA2B;MAC5C7iB,IAAI,CAAC8iB,UAAU,GAAGve,WAAW,CAACjD,GAAG,CAAE,KAAM,CAAC;MAC1CtB,IAAI,CAAC2iB,UAAU,GAAGpe,WAAW,CAACjD,GAAG,CAAE,MAAO,CAAC;;MAE3C;MACAtB,IAAI,CAAC+iB,SAAS,GAAGjjB,GAAG,CAClBsgB,QAAQ,CACRtgB,GAAG,CAACkjB,UAAU,CAAE;QAAE/e,MAAM,EAAEuc,YAAY;QAAE5Z,GAAG,EAAE;MAAY,CAAE,CAC5D,CAAC,CACAvB,GAAG,CAAC,CAAC;MAEP,OAAOrF,IAAI;IACZ,CAAC;IAEDmiB,sBAAsB,EAAE,SAAAA,CAAA,EAAY;MACnC,IAAIc,mBAAmB,GAAGrjB,CAAC,CAC1B,6EACD,CAAC;MAED,IAAKqjB,mBAAmB,CAAC5gB,MAAM,EAAG;QACjCzC,CAAC,CAAE,mCAAoC,CAAC,CAAC2F,OAAO,CAAE,OAAQ,CAAC;QAC3D3F,CAAC,CAAE,wBAAyB,CAAC,CAAC2F,OAAO,CAAE,OAAQ,CAAC;MACjD;IACD,CAAC;IAEDyX,QAAQ,EAAE,SAAAA,CAAWrX,CAAC,EAAEnF,GAAG,EAAG;MAC7B;MACA,IAAI0iB,MAAM,GAAGtjB,CAAC,CAAE,wBAAyB,CAAC;;MAE1C;MACA,IAAK,CAAEsjB,MAAM,CAAC7d,GAAG,CAAC,CAAC,EAAG;QACrB;QACAM,CAAC,CAAC2T,cAAc,CAAC,CAAC;;QAElB;QACAxZ,GAAG,CAACqjB,UAAU,CAAE3iB,GAAI,CAAC;;QAErB;QACA0iB,MAAM,CAAC3d,OAAO,CAAE,OAAQ,CAAC;MAC1B;IACD,CAAC;IAED6d,OAAO,EAAE,SAAAA,CAAWzd,CAAC,EAAG;MACvBA,CAAC,CAAC2T,cAAc,CAAC,CAAC;IACnB,CAAC;IAED+J,uBAAuB,EAAE,SAAAA,CAAW1d,CAAC,EAAEnF,GAAG,EAAG;MAC5CmF,CAAC,CAAC2T,cAAc,CAAC,CAAC;MAClB9Y,GAAG,CAACiF,QAAQ,CAAE,QAAS,CAAC;;MAExB;MACA3F,GAAG,CAACiX,UAAU,CAAE;QACfE,OAAO,EAAE,IAAI;QACbxD,MAAM,EAAEjT,GAAG;QACX+I,OAAO,EAAE,IAAI;QACb1F,IAAI,EAAE/D,GAAG,CAACqN,EAAE,CAAE,4BAA6B,CAAC;QAC5C8J,OAAO,EAAE,SAAAA,CAAA,EAAY;UACpB/P,MAAM,CAACoc,QAAQ,CAACC,IAAI,GAAG/iB,GAAG,CAACsD,IAAI,CAAE,MAAO,CAAC;QAC1C,CAAC;QACDoT,MAAM,EAAE,SAAAA,CAAA,EAAY;UACnB1W,GAAG,CAACgF,WAAW,CAAE,QAAS,CAAC;QAC5B;MACD,CAAE,CAAC;IACJ,CAAC;IAEDge,aAAa,EAAE,SAAAA,CAAW7d,CAAC,EAAEnF,GAAG,EAAG;MAClC,IAAIijB,aAAa,GAAG7jB,CAAC,CAAE,cAAe,CAAC;MAEvC,IAAK,CAAEY,GAAG,CAAC6E,GAAG,CAAC,CAAC,EAAG;QAClB7E,GAAG,CAACiF,QAAQ,CAAE,iBAAkB,CAAC;QACjCge,aAAa,CAAChe,QAAQ,CAAE,UAAW,CAAC;QACpC7F,CAAC,CAAE,cAAe,CAAC,CAAC6F,QAAQ,CAAE,UAAW,CAAC;MAC3C,CAAC,MAAM;QACNjF,GAAG,CAACgF,WAAW,CAAE,iBAAkB,CAAC;QACpCie,aAAa,CAACje,WAAW,CAAE,UAAW,CAAC;QACvC5F,CAAC,CAAE,cAAe,CAAC,CAAC4F,WAAW,CAAE,UAAW,CAAC;MAC9C;IACD,CAAC;IAEDke,mBAAmB,EAAE,SAAAA,CAAWngB,IAAI,EAAG;MACtCA,IAAI,CAACogB,OAAO,GAAG,IAAI;MAEnB,IACCpgB,IAAI,CAACU,MAAM,KACTV,IAAI,CAACU,MAAM,CAACmO,QAAQ,CAAE,kBAAmB,CAAC,IAC3C7O,IAAI,CAACU,MAAM,CAACmO,QAAQ,CAAE,8BAA+B,CAAC,IACtD7O,IAAI,CAACU,MAAM,CAACyP,OAAO,CAAE,mBAAoB,CAAC,CAACrR,MAAM,CAAE,EACnD;QACDkB,IAAI,CAACogB,OAAO,GAAG,KAAK;QACpBpgB,IAAI,CAACqgB,gBAAgB,GAAG,IAAI;MAC7B;;MAEA;MACA,IACCrgB,IAAI,CAACU,MAAM,IACXV,IAAI,CAACU,MAAM,CAACnD,IAAI,CAAE,wBAAyB,CAAC,CAACuB,MAAM,EAClD;QACDkB,IAAI,CAACqgB,gBAAgB,GAAG,KAAK;MAC9B;MAEA,OAAOrgB,IAAI;IACZ,CAAC;IAEDsgB,wBAAwB,EAAE,SAAAA,CAAWnb,QAAQ,EAAG;MAC/C,OAAOA,QAAQ,GAAG,4CAA4C;IAC/D;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIob,oBAAoB,GAAG,IAAIhkB,GAAG,CAACgK,KAAK,CAAE;IACzCkD,EAAE,EAAE,sBAAsB;IAC1B2R,IAAI,EAAE,SAAS;IAEfve,MAAM,EAAE;MACP,4BAA4B,EAAE,mBAAmB;MACjD,iCAAiC,EAAE,2BAA2B;MAC9D,gCAAgC,EAAE;IACnC,CAAC;IAEDO,UAAU,EAAE,SAAAA,CAAA,EAAY;MACvB;MACA,IAAIsL,IAAI,GAAGrM,CAAC,CAAE,eAAgB,CAAC;MAC/B,IAAImkB,OAAO,GAAGnkB,CAAC,CAAE,4BAA6B,CAAC;;MAE/C;MACAqM,IAAI,CAACnL,IAAI,CAAE,gBAAiB,CAAC,CAAC6B,MAAM,CAAEohB,OAAO,CAAC9iB,IAAI,CAAC,CAAE,CAAC;MACtDgL,IAAI,CAACnL,IAAI,CAAE,mBAAoB,CAAC,CAACgG,MAAM,CAAC,CAAC;;MAEzC;MACAid,OAAO,CAACjd,MAAM,CAAC,CAAC;;MAEhB;MACA,IAAI,CAACtG,GAAG,GAAGZ,CAAC,CAAE,sBAAuB,CAAC;;MAEtC;MACA,IAAI,CAACc,MAAM,CAAC,CAAC;IACd,CAAC;IAEDsjB,kBAAkB,EAAE,SAAAA,CAAA,EAAY;MAC/B,OAAO,IAAI,CAACxjB,GAAG,CAACM,IAAI,CAAE,qBAAsB,CAAC,CAACkH,IAAI,CAAE,SAAU,CAAC;IAChE,CAAC;IAEDic,0BAA0B,EAAE,SAAAA,CAAA,EAAY;MACvC,MAAMxY,MAAM,GAAG,IAAI,CAACjL,GAAG,CAACM,IAAI,CAAE,0BAA2B,CAAC;;MAE1D;MACA,IAAK,CAAE2K,MAAM,CAACpJ,MAAM,EAAG;QACtB,OAAO,KAAK;MACb;MAEA,OAAOoJ,MAAM,CAACzD,IAAI,CAAE,SAAU,CAAC;IAChC,CAAC;IAEDkc,sBAAsB,EAAE,SAAAA,CAAA,EAAY;MACnC,OAAO,IAAI,CAAC1jB,GAAG,CACbM,IAAI,CAAE,sCAAuC,CAAC,CAC9CuE,GAAG,CAAC,CAAC;IACR,CAAC;IAED8e,iBAAiB,EAAE,SAAAA,CAAWxe,CAAC,EAAEnF,GAAG,EAAG;MACtC,IAAI6E,GAAG,GAAG,IAAI,CAAC2e,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MAC3ClkB,GAAG,CAACskB,iBAAiB,CAAE,iBAAiB,EAAE/e,GAAI,CAAC;MAC/C,IAAI,CAAC3E,MAAM,CAAC,CAAC;IACd,CAAC;IAED2jB,yBAAyB,EAAE,SAAAA,CAAA,EAAY;MACtC,MAAMhf,GAAG,GAAG,IAAI,CAAC4e,0BAA0B,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MACrDnkB,GAAG,CAACskB,iBAAiB,CAAE,0BAA0B,EAAE/e,GAAI,CAAC;MACxD,IAAI,CAAC3E,MAAM,CAAC,CAAC;IACd,CAAC;IAEDA,MAAM,EAAE,SAAAA,CAAA,EAAY;MACnB,IAAK,IAAI,CAACsjB,kBAAkB,CAAC,CAAC,EAAG;QAChCpkB,CAAC,CAAE,yBAA0B,CAAC,CAAC6F,QAAQ,CAAE,iBAAkB,CAAC;MAC7D,CAAC,MAAM;QACN7F,CAAC,CAAE,yBAA0B,CAAC,CAAC4F,WAAW,CAAE,iBAAkB,CAAC;MAChE;MAEA,IAAK,CAAE,IAAI,CAACye,0BAA0B,CAAC,CAAC,EAAG;QAC1CrkB,CAAC,CAAE,yBAA0B,CAAC,CAAC6F,QAAQ,CAAE,WAAY,CAAC;QACtD7F,CAAC,CAAE,0BAA2B,CAAC,CAC7B4F,WAAW,CAAE,YAAa,CAAC,CAC3BwC,IAAI,CAAE,QAAQ,EAAE,KAAM,CAAC;MAC1B,CAAC,MAAM;QACNpI,CAAC,CAAE,yBAA0B,CAAC,CAAC4F,WAAW,CAAE,WAAY,CAAC;QAEzD5F,CAAC,CAAE,mBAAoB,CAAC,CAAC8C,IAAI,CAAE,YAAY;UAC1C,MAAM4hB,SAAS,GAAGxkB,GAAG,CAACoR,SAAS,CAAE;YAChC1M,IAAI,EAAE,KAAK;YACXP,MAAM,EAAErE,CAAC,CAAE,IAAK,CAAC;YACjBgkB,gBAAgB,EAAE,IAAI;YACtB5S,KAAK,EAAE;UACR,CAAE,CAAC;UAEH,IAAKsT,SAAS,CAACjiB,MAAM,EAAG;YACvBiiB,SAAS,CAAE,CAAC,CAAE,CAACjJ,IAAI,CAAC5W,GAAG,CAAE,aAAa,EAAE,KAAM,CAAC;UAChD;UAEA3E,GAAG,CAACkB,QAAQ,CAAE,MAAM,EAAEpB,CAAC,CAAE,IAAK,CAAE,CAAC;QAClC,CAAE,CAAC;MACJ;MAEA,IAAK,IAAI,CAACskB,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAG;QACzCtkB,CAAC,CAAE,MAAO,CAAC,CAAC4F,WAAW,CAAE,WAAY,CAAC;QACtC5F,CAAC,CAAE,MAAO,CAAC,CAAC6F,QAAQ,CAAE,WAAY,CAAC;MACpC,CAAC,MAAM;QACN7F,CAAC,CAAE,MAAO,CAAC,CAAC4F,WAAW,CAAE,WAAY,CAAC;QACtC5F,CAAC,CAAE,MAAO,CAAC,CAAC6F,QAAQ,CAAE,WAAY,CAAC;MACpC;IACD;EACD,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI8e,kBAAkB,GAAG,IAAIzkB,GAAG,CAACgK,KAAK,CAAE;IACvCC,OAAO,EAAE;MACRuW,SAAS,EAAE;IACZ,CAAC;IAEDC,UAAU,EAAE,SAAAA,CAAW5V,KAAK,EAAG;MAC9B;MACA,IAAK,CAAEA,KAAK,CAAC6G,GAAG,CAAE,QAAS,CAAC,EAAG;;MAE/B;MACA,IAAI7O,MAAM,GAAGgI,KAAK,CAACrJ,GAAG,CAAE,QAAS,CAAC;MAClC,IAAIkjB,QAAQ,GAAG7Z,KAAK,CAACnK,GAAG,CACtBkP,QAAQ,CAAE,cAAc,GAAG/M,MAAM,GAAG,IAAK,CAAC,CAC1CwD,KAAK,CAAC,CAAC;;MAET;MACA,IAAK,CAAEqe,QAAQ,CAACniB,MAAM,EAAG;;MAEzB;MACA,IAAI4J,IAAI,GAAGuY,QAAQ,CAACzU,QAAQ,CAAE,YAAa,CAAC;MAC5C,IAAI0U,GAAG,GAAGxY,IAAI,CAAC8D,QAAQ,CAAE,IAAK,CAAC;;MAE/B;MACA,IAAK,CAAE0U,GAAG,CAACpiB,MAAM,EAAG;QACnB4J,IAAI,CAACyY,SAAS,CAAE,mCAAoC,CAAC;QACrDD,GAAG,GAAGxY,IAAI,CAAC8D,QAAQ,CAAE,IAAK,CAAC;MAC5B;;MAEA;MACA,IAAI9O,IAAI,GAAG0J,KAAK,CAAC/K,CAAC,CAAE,YAAa,CAAC,CAACqB,IAAI,CAAC,CAAC;MACzC,IAAI0jB,GAAG,GAAG/kB,CAAC,CAAE,MAAM,GAAGqB,IAAI,GAAG,OAAQ,CAAC;MACtCwjB,GAAG,CAAC9hB,MAAM,CAAEgiB,GAAI,CAAC;MACjBF,GAAG,CAAC3gB,IAAI,CAAE,WAAW,EAAE2gB,GAAG,CAAC1U,QAAQ,CAAC,CAAC,CAAC1N,MAAO,CAAC;;MAE9C;MACAsI,KAAK,CAAC7D,MAAM,CAAC,CAAC;IACf;EACD,CAAE,CAAC;AACJ,CAAC,EAAIK,MAAO,CAAC;;;;;;;;;;;;;;;;AC7YkC;AAC/C;AACA,cAAc,6DAAa;AAC3B;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;;;;;ACRkC;AAClC;AACA,kBAAkB,sDAAO;AACzB;AACA;AACA;AACA,oBAAoB,sDAAO;AAC3B;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACVkC;AACS;AAC3C;AACA,UAAU,2DAAW;AACrB,qBAAqB,sDAAO;AAC5B;;;;;;;;;;;;;;;;ACLA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA,GAAG;AACH;;;;;;;UCRA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN2B;AACM;AACG;AACE;AACJ;AACG;AACI", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_browse-fields-modal.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-compatibility.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-conditions.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-field.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-fields.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-locations.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-settings.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/defineProperty.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "webpack://advanced-custom-fields-pro/./node_modules/@babel/runtime/helpers/esm/typeof.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/acf-field-group.js"], "sourcesContent": ["/**\n * Extends acf.models.Modal to create the field browser.\n *\n * @package Advanced Custom Fields\n */\n\n( function ( $, undefined, acf ) {\n\tconst browseFieldsModal = {\n\t\tdata: {\n\t\t\topenedBy: null,\n\t\t\tcurrentFieldType: null,\n\t\t\tpopularFieldTypes: [\n\t\t\t\t'text',\n\t\t\t\t'textarea',\n\t\t\t\t'email',\n\t\t\t\t'url',\n\t\t\t\t'file',\n\t\t\t\t'gallery',\n\t\t\t\t'select',\n\t\t\t\t'true_false',\n\t\t\t\t'link',\n\t\t\t\t'post_object',\n\t\t\t\t'relationship',\n\t\t\t\t'repeater',\n\t\t\t\t'flexible_content',\n\t\t\t\t'clone',\n\t\t\t],\n\t\t},\n\n\t\tevents: {\n\t\t\t'click .acf-modal-close': 'onClickClose',\n\t\t\t'keydown .acf-browse-fields-modal': 'onPressEscapeClose',\n\t\t\t'click .acf-select-field': 'onClickSelectField',\n\t\t\t'click .acf-field-type': 'onClickFieldType',\n\t\t\t'changed:currentFieldType': 'onChangeFieldType',\n\t\t\t'input .acf-search-field-types': 'onSearchFieldTypes',\n\t\t\t'click .acf-browse-popular-fields': 'onClickBrowsePopular',\n\t\t},\n\n\t\tsetup: function ( props ) {\n\t\t\t$.extend( this.data, props );\n\t\t\tthis.$el = $( this.tmpl() );\n\t\t\tthis.render();\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.open();\n\t\t\tthis.lockFocusToModal( true );\n\t\t\tthis.$el.find( '.acf-modal-title' ).focus();\n\t\t\tacf.doAction( 'show', this.$el );\n\t\t},\n\n\t\ttmpl: function () {\n\t\t\treturn $( '#tmpl-acf-browse-fields-modal' ).html();\n\t\t},\n\n\t\tgetFieldTypes: function ( category, search ) {\n\t\t\tlet fieldTypes;\n\t\t\tif ( ! acf.get( 'is_pro' ) ) {\n\t\t\t\t// Add in the pro fields.\n\t\t\t\tfieldTypes = Object.values( {\n\t\t\t\t\t...acf.get( 'fieldTypes' ),\n\t\t\t\t\t...acf.get( 'PROFieldTypes' ),\n\t\t\t\t} );\n\t\t\t} else {\n\t\t\t\tfieldTypes = Object.values( acf.get( 'fieldTypes' ) );\n\t\t\t}\n\n\t\t\tif ( category ) {\n\t\t\t\tif ( 'popular' === category ) {\n\t\t\t\t\treturn fieldTypes.filter( ( fieldType ) =>\n\t\t\t\t\t\tthis.get( 'popularFieldTypes' ).includes(\n\t\t\t\t\t\t\tfieldType.name\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tif ( 'pro' === category ) {\n\t\t\t\t\treturn fieldTypes.filter( ( fieldType ) => fieldType.pro );\n\t\t\t\t}\n\n\t\t\t\tfieldTypes = fieldTypes.filter(\n\t\t\t\t\t( fieldType ) => fieldType.category === category\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif ( search ) {\n\t\t\t\tfieldTypes = fieldTypes.filter( ( fieldType ) => {\n\t\t\t\t\tconst label = fieldType.label.toLowerCase();\n\t\t\t\t\tconst labelParts = label.split( ' ' );\n\t\t\t\t\tlet match = false;\n\n\t\t\t\t\tif ( label.startsWith( search.toLowerCase() ) ) {\n\t\t\t\t\t\tmatch = true;\n\t\t\t\t\t} else if ( labelParts.length > 1 ) {\n\t\t\t\t\t\tlabelParts.forEach( ( part ) => {\n\t\t\t\t\t\t\tif ( part.startsWith( search.toLowerCase() ) ) {\n\t\t\t\t\t\t\t\tmatch = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\n\t\t\t\t\treturn match;\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\treturn fieldTypes;\n\t\t},\n\n\t\trender: function () {\n\t\t\tacf.doAction( 'append', this.$el );\n\n\t\t\tconst $tabs = this.$el.find( '.acf-field-types-tab' );\n\t\t\tconst self = this;\n\n\t\t\t$tabs.each( function () {\n\t\t\t\tconst category = $( this ).data( 'category' );\n\t\t\t\tconst fieldTypes = self.getFieldTypes( category );\n\t\t\t\tfieldTypes.forEach( ( fieldType ) => {\n\t\t\t\t\t$( this ).append( self.getFieldTypeHTML( fieldType ) );\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\tthis.initializeFieldLabel();\n\t\t\tthis.initializeFieldType();\n\t\t\tthis.onChangeFieldType();\n\t\t},\n\n\t\tgetFieldTypeHTML: function ( fieldType ) {\n\t\t\tconst iconName = fieldType.name.replaceAll( '_', '-' );\n\n\t\t\treturn `\n\t\t\t<a href=\"#\" class=\"acf-field-type\" data-field-type=\"${ fieldType.name }\">\n\t\t\t\t${\n\t\t\t\t\tfieldType.pro && ! acf.get( 'is_pro' )\n\t\t\t\t\t\t? '<span class=\"field-type-requires-pro not-pro\"></span>'\n\t\t\t\t\t\t: fieldType.pro\n\t\t\t\t\t\t? '<span class=\"field-type-requires-pro\"></span>'\n\t\t\t\t\t\t: ''\n\t\t\t\t}\n\t\t\t\t<i class=\"field-type-icon field-type-icon-${ iconName }\"></i>\n\t\t\t\t<span class=\"field-type-label\">${ fieldType.label }</span>\n\t\t\t</a>\n\t\t\t`;\n\t\t},\n\n\t\tdecodeFieldTypeURL: function ( url ) {\n\t\t\tif ( typeof url != 'string' ) return url;\n\t\t\treturn url.replaceAll( '&#038;', '&' );\n\t\t},\n\n\t\trenderFieldTypeDesc: function ( fieldType ) {\n\t\t\tconst fieldTypeInfo =\n\t\t\t\tthis.getFieldTypes().filter(\n\t\t\t\t\t( fieldTypeFilter ) => fieldTypeFilter.name === fieldType\n\t\t\t\t)[ 0 ] || {};\n\n\t\t\tconst args = acf.parseArgs( fieldTypeInfo, {\n\t\t\t\tlabel: '',\n\t\t\t\tdescription: '',\n\t\t\t\tdoc_url: false,\n\t\t\t\ttutorial_url: false,\n\t\t\t\tpreview_image: false,\n\t\t\t\tpro: false,\n\t\t\t} );\n\n\t\t\tthis.$el.find( '.field-type-name' ).text( args.label );\n\t\t\tthis.$el.find( '.field-type-desc' ).text( args.description );\n\n\t\t\tif ( args.doc_url ) {\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.field-type-doc' )\n\t\t\t\t\t.attr( 'href', this.decodeFieldTypeURL( args.doc_url ) )\n\t\t\t\t\t.show();\n\t\t\t} else {\n\t\t\t\tthis.$el.find( '.field-type-doc' ).hide();\n\t\t\t}\n\n\t\t\tif ( args.tutorial_url ) {\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.field-type-tutorial' )\n\t\t\t\t\t.attr(\n\t\t\t\t\t\t'href',\n\t\t\t\t\t\tthis.decodeFieldTypeURL( args.tutorial_url )\n\t\t\t\t\t)\n\t\t\t\t\t.parent()\n\t\t\t\t\t.show();\n\t\t\t} else {\n\t\t\t\tthis.$el.find( '.field-type-tutorial' ).parent().hide();\n\t\t\t}\n\n\t\t\tif ( args.preview_image ) {\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.field-type-image' )\n\t\t\t\t\t.attr( 'src', args.preview_image )\n\t\t\t\t\t.show();\n\t\t\t} else {\n\t\t\t\tthis.$el.find( '.field-type-image' ).hide();\n\t\t\t}\n\n\t\t\tconst isPro = acf.get( 'is_pro' );\n\t\t\tconst isActive = acf.get( 'isLicenseActive' );\n\t\t\tconst $upgateToProButton = this.$el.find( '.acf-btn-pro' );\n\t\t\tconst $upgradeToUnlockButton = this.$el.find(\n\t\t\t\t'.field-type-upgrade-to-unlock'\n\t\t\t);\n\n\t\t\tif ( args.pro && ( ! isPro || ! isActive ) ) {\n\t\t\t\t$upgateToProButton.show();\n\t\t\t\t$upgateToProButton.attr(\n\t\t\t\t\t'href',\n\t\t\t\t\t$upgateToProButton.data( 'urlBase' ) + fieldType\n\t\t\t\t);\n\n\t\t\t\t$upgradeToUnlockButton.show();\n\t\t\t\t$upgradeToUnlockButton.attr(\n\t\t\t\t\t'href',\n\t\t\t\t\t$upgradeToUnlockButton.data( 'urlBase' ) + fieldType\n\t\t\t\t);\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.acf-insert-field-label' )\n\t\t\t\t\t.attr( 'disabled', true );\n\t\t\t\tthis.$el.find( '.acf-select-field' ).hide();\n\t\t\t} else {\n\t\t\t\t$upgateToProButton.hide();\n\t\t\t\t$upgradeToUnlockButton.hide();\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.acf-insert-field-label' )\n\t\t\t\t\t.attr( 'disabled', false );\n\t\t\t\tthis.$el.find( '.acf-select-field' ).show();\n\t\t\t}\n\t\t},\n\n\t\tinitializeFieldType: function () {\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\t\t\tconst fieldType = fieldObject?.data?.type;\n\n\t\t\t// Select default field type\n\t\t\tif ( fieldType ) {\n\t\t\t\tthis.set( 'currentFieldType', fieldType );\n\t\t\t} else {\n\t\t\t\tthis.set( 'currentFieldType', 'text' );\n\t\t\t}\n\n\t\t\t// Select first tab with selected field type\n\t\t\t// If type selected is wthin Popular, select Popular Tab\n\t\t\t// Else select first tab the type belongs\n\t\t\tconst fieldTypes = this.getFieldTypes();\n\t\t\tconst isFieldTypePopular =\n\t\t\t\tthis.get( 'popularFieldTypes' ).includes( fieldType );\n\n\t\t\tlet category = '';\n\t\t\tif ( isFieldTypePopular ) {\n\t\t\t\tcategory = 'popular';\n\t\t\t} else {\n\t\t\t\tconst selectedFieldType = fieldTypes.find( ( x ) => {\n\t\t\t\t\treturn x.name === fieldType;\n\t\t\t\t} );\n\n\t\t\t\tcategory = selectedFieldType.category;\n\t\t\t}\n\n\t\t\tconst uppercaseCategory =\n\t\t\t\tcategory[ 0 ].toUpperCase() + category.slice( 1 );\n\t\t\tconst searchTabElement = `.acf-modal-content .acf-tab-wrap a:contains('${ uppercaseCategory }')`;\n\t\t\tsetTimeout( () => {\n\t\t\t\t$( searchTabElement ).click();\n\t\t\t}, 0 );\n\t\t},\n\n\t\tinitializeFieldLabel: function () {\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\t\t\tconst labelText = fieldObject.$fieldLabel().val();\n\t\t\tconst $fieldLabel = this.$el.find( '.acf-insert-field-label' );\n\t\t\tif ( labelText ) {\n\t\t\t\t$fieldLabel.val( labelText );\n\t\t\t} else {\n\t\t\t\t$fieldLabel.val( '' );\n\t\t\t}\n\t\t},\n\n\t\tupdateFieldObjectFieldLabel: function () {\n\t\t\tconst label = this.$el.find( '.acf-insert-field-label' ).val();\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\t\t\tfieldObject.$fieldLabel().val( label );\n\t\t\tfieldObject.$fieldLabel().trigger( 'blur' );\n\t\t},\n\n\t\tonChangeFieldType: function () {\n\t\t\tconst fieldType = this.get( 'currentFieldType' );\n\n\t\t\tthis.$el.find( '.selected' ).removeClass( 'selected' );\n\t\t\tthis.$el\n\t\t\t\t.find( '.acf-field-type[data-field-type=\"' + fieldType + '\"]' )\n\t\t\t\t.addClass( 'selected' );\n\n\t\t\tthis.renderFieldTypeDesc( fieldType );\n\t\t},\n\n\t\tonSearchFieldTypes: function ( e ) {\n\t\t\tconst $modal = this.$el.find( '.acf-browse-fields-modal' );\n\t\t\tconst inputVal = this.$el.find( '.acf-search-field-types' ).val();\n\t\t\tconst self = this;\n\t\t\tlet searchString,\n\t\t\t\tresultsHtml = '';\n\t\t\tlet matches = [];\n\n\t\t\tif ( 'string' === typeof inputVal ) {\n\t\t\t\tsearchString = inputVal.trim();\n\t\t\t\tmatches = this.getFieldTypes( false, searchString );\n\t\t\t}\n\n\t\t\tif ( searchString.length && matches.length ) {\n\t\t\t\t$modal.addClass( 'is-searching' );\n\t\t\t} else {\n\t\t\t\t$modal.removeClass( 'is-searching' );\n\t\t\t}\n\n\t\t\tif ( ! matches.length ) {\n\t\t\t\t$modal.addClass( 'no-results-found' );\n\t\t\t\tthis.$el\n\t\t\t\t\t.find( '.acf-invalid-search-term' )\n\t\t\t\t\t.text( searchString );\n\t\t\t\treturn;\n\t\t\t} else {\n\t\t\t\t$modal.removeClass( 'no-results-found' );\n\t\t\t}\n\n\t\t\tmatches.forEach( ( fieldType ) => {\n\t\t\t\tresultsHtml = resultsHtml + self.getFieldTypeHTML( fieldType );\n\t\t\t} );\n\n\t\t\t$( '.acf-field-type-search-results' ).html( resultsHtml );\n\n\t\t\tthis.set( 'currentFieldType', matches[ 0 ].name );\n\t\t\tthis.onChangeFieldType();\n\t\t},\n\n\t\tonClickBrowsePopular: function () {\n\t\t\tthis.$el\n\t\t\t\t.find( '.acf-search-field-types' )\n\t\t\t\t.val( '' )\n\t\t\t\t.trigger( 'input' );\n\t\t\tthis.$el.find( '.acf-tab-wrap a' ).first().trigger( 'click' );\n\t\t},\n\n\t\tonClickSelectField: function ( e ) {\n\t\t\tconst fieldObject = this.get( 'openedBy' );\n\n\t\t\tfieldObject\n\t\t\t\t.$fieldTypeSelect()\n\t\t\t\t.val( this.get( 'currentFieldType' ) );\n\t\t\tfieldObject.$fieldTypeSelect().trigger( 'change' );\n\n\t\t\tthis.updateFieldObjectFieldLabel();\n\n\t\t\tthis.close();\n\t\t},\n\n\t\tonClickFieldType: function ( e ) {\n\t\t\tconst $fieldType = $( e.currentTarget );\n\t\t\tthis.set( 'currentFieldType', $fieldType.data( 'field-type' ) );\n\t\t},\n\n\t\tonClickClose: function () {\n\t\t\tthis.close();\n\t\t},\n\n\t\tonPressEscapeClose: function ( e ) {\n\t\t\tif ( e.key === 'Escape' ) {\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t},\n\n\t\tclose: function () {\n\t\t\tthis.lockFocusToModal( false );\n\t\t\tthis.returnFocusToOrigin();\n\t\t\tthis.remove();\n\t\t},\n\n\t\tfocus: function () {\n\t\t\tthis.$el.find( 'button' ).first().trigger( 'focus' );\n\t\t},\n\t};\n\n\tacf.models.browseFieldsModal = acf.models.Modal.extend( browseFieldsModal );\n\tacf.newBrowseFieldsModal = ( props ) =>\n\t\tnew acf.models.browseFieldsModal( props );\n} )( window.jQuery, undefined, window.acf );\n", "( function ( $, undefined ) {\n\tvar _acf = acf.getCompatibility( acf );\n\n\t/**\n\t *  fieldGroupCompatibility\n\t *\n\t *  Compatibility layer for extinct acf.field_group\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\t_acf.field_group = {\n\t\tsave_field: function ( $field, type ) {\n\t\t\ttype = type !== undefined ? type : 'settings';\n\t\t\tacf.getFieldObject( $field ).save( type );\n\t\t},\n\n\t\tdelete_field: function ( $field, animate ) {\n\t\t\tanimate = animate !== undefined ? animate : true;\n\t\t\tacf.getFieldObject( $field ).delete( {\n\t\t\t\tanimate: animate,\n\t\t\t} );\n\t\t},\n\n\t\tupdate_field_meta: function ( $field, name, value ) {\n\t\t\tacf.getFieldObject( $field ).prop( name, value );\n\t\t},\n\n\t\tdelete_field_meta: function ( $field, name ) {\n\t\t\tacf.getFieldObject( $field ).prop( name, null );\n\t\t},\n\t};\n\n\t/**\n\t *  fieldGroupCompatibility.field_object\n\t *\n\t *  Compatibility layer for extinct acf.field_group.field_object\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\t_acf.field_group.field_object = acf.model.extend( {\n\t\t// vars\n\t\ttype: '',\n\t\to: {},\n\t\t$field: null,\n\t\t$settings: null,\n\n\t\ttag: function ( tag ) {\n\t\t\t// vars\n\t\t\tvar type = this.type;\n\n\t\t\t// explode, add 'field' and implode\n\t\t\t// - open \t\t\t=> open_field\n\t\t\t// - change_type\t=> change_field_type\n\t\t\tvar tags = tag.split( '_' );\n\t\t\ttags.splice( 1, 0, 'field' );\n\t\t\ttag = tags.join( '_' );\n\n\t\t\t// add type\n\t\t\tif ( type ) {\n\t\t\t\ttag += '/type=' + type;\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn tag;\n\t\t},\n\n\t\tselector: function () {\n\t\t\t// vars\n\t\t\tvar selector = '.acf-field-object';\n\t\t\tvar type = this.type;\n\n\t\t\t// add type\n\t\t\tif ( type ) {\n\t\t\t\tselector += '-' + type;\n\t\t\t\tselector = acf.str_replace( '_', '-', selector );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn selector;\n\t\t},\n\n\t\t_add_action: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\n\t\t\t// add action\n\t\t\tacf.add_action( this.tag( name ), function ( $field ) {\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', $field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, arguments );\n\t\t\t} );\n\t\t},\n\n\t\t_add_filter: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\n\t\t\t// add action\n\t\t\tacf.add_filter( this.tag( name ), function ( $field ) {\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', $field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, arguments );\n\t\t\t} );\n\t\t},\n\n\t\t_add_event: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\t\t\tvar event = name.substr( 0, name.indexOf( ' ' ) );\n\t\t\tvar selector = name.substr( name.indexOf( ' ' ) + 1 );\n\t\t\tvar context = this.selector();\n\n\t\t\t// add event\n\t\t\t$( document ).on( event, context + ' ' + selector, function ( e ) {\n\t\t\t\t// append $el to event object\n\t\t\t\te.$el = $( this );\n\t\t\t\te.$field = e.$el.closest( '.acf-field-object' );\n\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', e.$field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, [ e ] );\n\t\t\t} );\n\t\t},\n\n\t\t_set_$field: function () {\n\t\t\t// vars\n\t\t\tthis.o = this.$field.data();\n\n\t\t\t// els\n\t\t\tthis.$settings = this.$field.find( '> .settings > table > tbody' );\n\n\t\t\t// focus\n\t\t\tthis.focus();\n\t\t},\n\n\t\tfocus: function () {\n\t\t\t// do nothing\n\t\t},\n\n\t\tsetting: function ( name ) {\n\t\t\treturn this.$settings.find( '> .acf-field-setting-' + name );\n\t\t},\n\t} );\n\n\t/*\n\t *  field\n\t *\n\t *  This model fires actions and filters for registered fields\n\t *\n\t *  @type\tfunction\n\t *  @date\t21/02/2014\n\t *  @since\t3.5.1\n\t *\n\t *  @param\tn/a\n\t *  @return\tn/a\n\t */\n\n\tvar actionManager = new acf.Model( {\n\t\tactions: {\n\t\t\topen_field_object: 'onOpenFieldObject',\n\t\t\tclose_field_object: 'onCloseFieldObject',\n\t\t\tadd_field_object: 'onAddFieldObject',\n\t\t\tduplicate_field_object: 'onDuplicateFieldObject',\n\t\t\tdelete_field_object: 'onDeleteFieldObject',\n\t\t\tchange_field_object_type: 'onChangeFieldObjectType',\n\t\t\tchange_field_object_label: 'onChangeFieldObjectLabel',\n\t\t\tchange_field_object_name: 'onChangeFieldObjectName',\n\t\t\tchange_field_object_parent: 'onChangeFieldObjectParent',\n\t\t\tsortstop_field_object: 'onChangeFieldObjectParent',\n\t\t},\n\n\t\tonOpenFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'open_field', field.$el );\n\t\t\tacf.doAction( 'open_field/type=' + field.get( 'type' ), field.$el );\n\n\t\t\tacf.doAction( 'render_field_settings', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'render_field_settings/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonCloseFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'close_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'close_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonAddFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'add_field', field.$el );\n\t\t\tacf.doAction( 'add_field/type=' + field.get( 'type' ), field.$el );\n\t\t},\n\n\t\tonDuplicateFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'duplicate_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'duplicate_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonDeleteFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'delete_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'delete_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectType: function ( field ) {\n\t\t\tacf.doAction( 'change_field_type', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_type/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\n\t\t\tacf.doAction( 'render_field_settings', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'render_field_settings/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectLabel: function ( field ) {\n\t\t\tacf.doAction( 'change_field_label', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_label/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectName: function ( field ) {\n\t\t\tacf.doAction( 'change_field_name', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_name/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectParent: function ( field ) {\n\t\t\tacf.doAction( 'update_field_parent', field.$el );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  ConditionalLogicFieldSetting\n\t *\n\t *  description\n\t *\n\t *  @date\t3/2/18\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar ConditionalLogicFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: 'conditional_logic',\n\t\tevents: {\n\t\t\t'change .conditions-toggle': 'onChangeToggle',\n\t\t\t'click .add-conditional-group': 'onClickAddGroup',\n\t\t\t'focus .condition-rule-field': 'onFocusField',\n\t\t\t'change .condition-rule-field': 'onChangeField',\n\t\t\t'change .condition-rule-operator': 'onChangeOperator',\n\t\t\t'click .add-conditional-rule': 'onClickAdd',\n\t\t\t'click .remove-conditional-rule': 'onClickRemove',\n\t\t},\n\n\t\t$rule: false,\n\t\tscope: function ( $rule ) {\n\t\t\tthis.$rule = $rule;\n\t\t\treturn this;\n\t\t},\n\n\t\truleData: function ( name, value ) {\n\t\t\treturn this.$rule.data.apply( this.$rule, arguments );\n\t\t},\n\n\t\t$input: function ( name ) {\n\t\t\treturn this.$rule.find( '.condition-rule-' + name );\n\t\t},\n\n\t\t$td: function ( name ) {\n\t\t\treturn this.$rule.find( 'td.' + name );\n\t\t},\n\n\t\t$toggle: function () {\n\t\t\treturn this.$( '.conditions-toggle' );\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.rule-groups' );\n\t\t},\n\n\t\t$groups: function () {\n\t\t\treturn this.$( '.rule-group' );\n\t\t},\n\n\t\t$rules: function () {\n\t\t\treturn this.$( '.rule' );\n\t\t},\n\n\t\t$tabLabel: function () {\n\t\t\treturn this.fieldObject.$el.find('.conditional-logic-badge');\n\t\t},\n\n\t\t$conditionalValueSelect: function () {\n\t\t\treturn this.$( '.condition-rule-value' );\n\t\t},\n\n\t\topen: function () {\n\t\t\tvar $div = this.$control();\n\t\t\t$div.show();\n\t\t\tacf.enable( $div );\n\t\t},\n\n\t\tclose: function () {\n\t\t\tvar $div = this.$control();\n\t\t\t$div.hide();\n\t\t\tacf.disable( $div );\n\t\t},\n\n\t\trender: function () {\n\t\t\t// show\n\t\t\tif ( this.$toggle().prop( 'checked' ) ) {\n\t\t\t\tthis.$tabLabel().addClass('is-enabled');\n\t\t\t\tthis.renderRules();\n\t\t\t\tthis.open();\n\n\t\t\t\t// hide\n\t\t\t} else {\n\t\t\t\tthis.$tabLabel().removeClass('is-enabled');\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t},\n\n\t\trenderRules: function () {\n\t\t\t// vars\n\t\t\tvar self = this;\n\n\t\t\t// loop\n\t\t\tthis.$rules().each( function () {\n\t\t\t\tself.renderRule( $( this ) );\n\t\t\t} );\n\t\t},\n\n\t\trenderRule: function ( $rule ) {\n\t\t\tthis.scope( $rule );\n\t\t\tthis.renderField();\n\t\t\tthis.renderOperator();\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\trenderField: function () {\n\t\t\t// vars\n\t\t\tvar choices = [];\n\t\t\tvar validFieldTypes = [];\n\t\t\tvar cid = this.fieldObject.cid;\n\t\t\tvar $select = this.$input( 'field' );\n\n\t\t\t// loop\n\t\t\tacf.getFieldObjects().map( function ( fieldObject ) {\n\t\t\t\t// vars\n\t\t\t\tvar choice = {\n\t\t\t\t\tid: fieldObject.getKey(),\n\t\t\t\t\ttext: fieldObject.getLabel(),\n\t\t\t\t};\n\n\t\t\t\t// bail early if is self\n\t\t\t\tif ( fieldObject.cid === cid ) {\n\t\t\t\t\tchoice.text += ' ' + acf.__( '(this field)' );\n\t\t\t\t\tchoice.disabled = true;\n\t\t\t\t}\n\n\t\t\t\t// get selected field conditions\n\t\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\t\tfieldType: fieldObject.getType(),\n\t\t\t\t} );\n\n\t\t\t\t// bail early if no types\n\t\t\t\tif ( ! conditionTypes.length ) {\n\t\t\t\t\tchoice.disabled = true;\n\t\t\t\t}\n\n\t\t\t\t// calulate indents\n\t\t\t\tvar indents = fieldObject.getParents().length;\n\t\t\t\tchoice.text = '- '.repeat( indents ) + choice.text;\n\n\t\t\t\t// append\n\t\t\t\tchoices.push( choice );\n\t\t\t} );\n\n\t\t\t// allow for scenario where only one field exists\n\t\t\tif ( ! choices.length ) {\n\t\t\t\tchoices.push( {\n\t\t\t\t\tid: '',\n\t\t\t\t\ttext: acf.__( 'No toggle fields available' ),\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// render\n\t\t\tacf.renderSelect( $select, choices );\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'field', $select.val() );\n\t\t},\n\n\t\trenderOperator: function () {\n\t\t\t// bail early if no field selected\n\t\t\tif ( ! this.ruleData( 'field' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar $select = this.$input( 'operator' );\n\t\t\tvar val = $select.val();\n\t\t\tvar choices = [];\n\n\t\t\t// set saved value on first render\n\t\t\t// - this allows the 2nd render to correctly select an option\n\t\t\tif ( $select.val() === null ) {\n\t\t\t\tacf.renderSelect( $select, [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: this.ruleData( 'operator' ),\n\t\t\t\t\t\ttext: '',\n\t\t\t\t\t},\n\t\t\t\t] );\n\t\t\t}\n\n\t\t\t// get selected field\n\t\t\tvar $field = acf.findFieldObject( this.ruleData( 'field' ) );\n\t\t\tvar field = acf.getFieldObject( $field );\n\n\t\t\t// get selected field conditions\n\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\tfieldType: field.getType(),\n\t\t\t} );\n\n\t\t\t// html\n\t\t\tconditionTypes.map( function ( model ) {\n\t\t\t\tchoices.push( {\n\t\t\t\t\tid: model.prototype.operator,\n\t\t\t\t\ttext: model.prototype.label,\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\t// render\n\t\t\tacf.renderSelect( $select, choices );\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'operator', $select.val() );\n\t\t},\n\n\t\trenderValue: function () {\n\t\t\t// bail early if no field selected\n\t\t\tif ( ! this.ruleData( 'field' ) || ! this.ruleData( 'operator' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar $select = this.$input( 'value' );\n\t\t\tvar $td = this.$td( 'value' );\n\t\t\tvar currentVal = $select.val();\n\t\t\tvar savedValue = this.$rule[0].getAttribute( 'data-value' );\n\n\t\t\t// get selected field\n\t\t\tvar $field = acf.findFieldObject( this.ruleData( 'field' ) );\n\t\t\tvar field = acf.getFieldObject( $field );\n\t\t\t// get selected field conditions\n\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\tfieldType: field.getType(),\n\t\t\t\toperator: this.ruleData( 'operator' ),\n\t\t\t} );\n\n\t\t\tvar conditionType = conditionTypes[ 0 ].prototype;\n\t\t\tvar choices = conditionType.choices( field );\n\t\t\tlet $newSelect;\n\t\t\tif ( choices instanceof jQuery && !! choices.data( 'acfSelect2Props' ) ) {\n\t\t\t\t$newSelect = $select.clone();\n\t\t\t\t// If converting from a disabled input, we need to convert it to an active select.\n\t\t\t\tif ( $newSelect.is( 'input' ) ) {\n\t\t\t\t\tvar classes = $select.attr( 'class' );\n\t\t\t\t\tconst $rebuiltSelect = $( '<select></select>' ).addClass( classes ).val( savedValue );\n\t\t\t\t\t$newSelect = $rebuiltSelect;\n\t\t\t\t}\n\n\t\t\t\tacf.addAction( 'acf_conditional_value_rendered', function() {\n\t\t\t\t\tacf.newSelect2( $newSelect, choices.data( 'acfSelect2Props' ) );\n\t\t\t\t});\n\t\t\t} else if ( choices instanceof Array ) {\n\t\t\t\tthis.$conditionalValueSelect().removeClass( 'select2-hidden-accessible' );\n\t\t\t\t$newSelect = $( '<select></select>' );\n\t\t\t\tacf.renderSelect( $newSelect, choices );\n\t\t\t} else {\n\t\t\t\tthis.$conditionalValueSelect().removeClass( 'select2-hidden-accessible' );\n\t\t\t\t$newSelect = $( choices );\n\t\t\t}\n\n\t\t\t// append\n\t\t\t$select.detach();\n\t\t\t$td.html( $newSelect );\n\n\t\t\t// timeout needed to avoid browser bug where \"disabled\" attribute is not applied\n\t\t\tsetTimeout( function () {\n\t\t\t\t[ 'class', 'name', 'id' ].map( function ( attr ) {\n\t\t\t\t\t$newSelect.attr( attr, $select.attr( attr ) );\n\t\t\t\t} );\n\t\t\t\t$select.val( savedValue );\n\t\t\t\tacf.doAction( 'acf_conditional_value_rendered' );\n\t\t\t}, 0 );\n\t\t\t// select existing value (if not a disabled input)\n\t\t\tif ( ! $newSelect.prop( 'disabled' ) ) {\n\t\t\t\tacf.val( $newSelect, currentVal, true );\n\t\t\t}\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'value', $newSelect.val() );\n\t\t},\n\n\t\tonChangeToggle: function () {\n\t\t\tthis.render();\n\t\t},\n\n\t\tonClickAddGroup: function ( e, $el ) {\n\t\t\tthis.addGroup();\n\t\t},\n\n\t\taddGroup: function () {\n\t\t\t// vars\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\t// duplicate\n\t\t\tvar $group2 = acf.duplicate( $group );\n\n\t\t\t// update h4\n\t\t\t$group2.find( 'h4' ).text( acf.__( 'or' ) );\n\n\t\t\t// remove all tr's except the first one\n\t\t\t$group2.find( 'tr' ).not( ':first' ).remove();\n\n\t\t\t// Find the remaining tr and render\n\t\t\tvar $tr = $group2.find( 'tr' );\n\t\t\tthis.renderRule( $tr );\n\n\t\t\t// save field\n\t\t\tthis.fieldObject.save();\n\t\t},\n\n\t\tonFocusField: function ( e, $el ) {\n\t\t\tthis.renderField();\n\t\t},\n\n\t\tonChangeField: function ( e, $el ) {\n\t\t\t// scope\n\t\t\tthis.scope( $el.closest( '.rule' ) );\n\n\t\t\t// set data\n\t\t\tthis.ruleData( 'field', $el.val() );\n\n\t\t\t// render\n\t\t\tthis.renderOperator();\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\tonChangeOperator: function ( e, $el ) {\n\t\t\t// scope\n\t\t\tthis.scope( $el.closest( '.rule' ) );\n\n\t\t\t// set data\n\t\t\tthis.ruleData( 'operator', $el.val() );\n\n\t\t\t// render\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// duplciate\n\t\t\tvar $rule = acf.duplicate( $el.closest( '.rule' ) );\n\n\t\t\t// render\n\t\t\tthis.renderRule( $rule );\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $rule = $el.closest( '.rule' );\n\n\t\t\t// save field\n\t\t\tthis.fieldObject.save();\n\n\t\t\t// remove group\n\t\t\tif ( $rule.siblings( '.rule' ).length == 0 ) {\n\t\t\t\t$rule.closest( '.rule-group' ).remove();\n\t\t\t}\n\n\t\t\t// remove\n\t\t\t$rule.remove();\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( ConditionalLogicFieldSetting );\n\n\t/**\n\t *  conditionalLogicHelper\n\t *\n\t *  description\n\t *\n\t *  @date\t20/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar conditionalLogicHelper = new acf.Model( {\n\t\tactions: {\n\t\t\tduplicate_field_objects: 'onDuplicateFieldObjects',\n\t\t},\n\n\t\tonDuplicateFieldObjects: function ( children, newField, prevField ) {\n\t\t\t// vars\n\t\t\tvar data = {};\n\t\t\tvar $selects = $();\n\n\t\t\t// reference change in key\n\t\t\tchildren.map( function ( child ) {\n\t\t\t\t// store reference of changed key\n\t\t\t\tdata[ child.get( 'prevKey' ) ] = child.get( 'key' );\n\n\t\t\t\t// append condition select\n\t\t\t\t$selects = $selects.add( child.$( '.condition-rule-field' ) );\n\t\t\t} );\n\n\t\t\t// loop\n\t\t\t$selects.each( function () {\n\t\t\t\t// vars\n\t\t\t\tvar $select = $( this );\n\t\t\t\tvar val = $select.val();\n\n\t\t\t\t// bail early if val is not a ref key\n\t\t\t\tif ( ! val || ! data[ val ] ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// modify selected option\n\t\t\t\t$select.find( 'option:selected' ).attr( 'value', data[ val ] );\n\n\t\t\t\t// set new val\n\t\t\t\t$select.val( data[ val ] );\n\t\t\t} );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\tacf.FieldObject = acf.Model.extend( {\n\t\t// class used to avoid nested event triggers\n\t\teventScope: '.acf-field-object',\n\n\t\t// variable for field type select2\n\t\tfieldTypeSelect2: false,\n\n\t\t// events\n\t\tevents: {\n\t\t\t'click .copyable': 'onClickCopy',\n\t\t\t'click .handle': 'onClickEdit',\n\t\t\t'click .close-field': 'onClickEdit',\n\t\t\t'click a[data-key=\"acf_field_settings_tabs\"]': 'onChangeSettingsTab',\n\t\t\t'click .delete-field': 'onClickDelete',\n\t\t\t'click .duplicate-field': 'duplicate',\n\t\t\t'click .move-field': 'move',\n\t\t\t'click .browse-fields': 'browseFields',\n\n\t\t\t'focus .edit-field': 'onFocusEdit',\n\t\t\t'blur .edit-field, .row-options a': 'onBlurEdit',\n\n\t\t\t'change .field-type': 'onChangeType',\n\t\t\t'change .field-required': 'onChangeRequired',\n\t\t\t'blur .field-label': 'onChangeLabel',\n\t\t\t'blur .field-name': 'onChangeName',\n\n\t\t\tchange: 'onChange',\n\t\t\tchanged: 'onChanged',\n\t\t},\n\n\t\t// data\n\t\tdata: {\n\t\t\t// Similar to ID, but used for HTML puposes.\n\t\t\t// It is possbile for a new field to have an ID of 0, but an id of 'field_123' */\n\t\t\tid: 0,\n\n\t\t\t// The field key ('field_123')\n\t\t\tkey: '',\n\n\t\t\t// The field type (text, image, etc)\n\t\t\ttype: '',\n\n\t\t\t// The $post->ID of this field\n\t\t\t//ID: 0,\n\n\t\t\t// The field's parent\n\t\t\t//parent: 0,\n\n\t\t\t// The menu order\n\t\t\t//menu_order: 0\n\t\t},\n\n\t\tsetup: function ( $field ) {\n\t\t\t// set $el\n\t\t\tthis.$el = $field;\n\n\t\t\t// inherit $field data (id, key, type)\n\t\t\tthis.inherit( $field );\n\n\t\t\t// load additional props\n\t\t\t// - this won't trigger 'changed'\n\t\t\tthis.prop( 'ID' );\n\t\t\tthis.prop( 'parent' );\n\t\t\tthis.prop( 'menu_order' );\n\t\t},\n\n\t\t$input: function ( name ) {\n\t\t\treturn $( '#' + this.getInputId() + '-' + name );\n\t\t},\n\n\t\t$meta: function () {\n\t\t\treturn this.$( '.meta:first' );\n\t\t},\n\n\t\t$handle: function () {\n\t\t\treturn this.$( '.handle:first' );\n\t\t},\n\n\t\t$settings: function () {\n\t\t\treturn this.$( '.settings:first' );\n\t\t},\n\n\t\t$setting: function ( name ) {\n\t\t\treturn this.$( '.acf-field-settings:first .acf-field-setting-' + name );\n\t\t},\n\n\t\t$fieldTypeSelect: function () {\n\t\t\treturn this.$( '.field-type' );\n\t\t},\n\n\t\t$fieldLabel: function () {\n\t\t\treturn this.$( '.field-label' );\n\t\t},\n\n\t\tgetParent: function () {\n\t\t\treturn acf.getFieldObjects( { child: this.$el, limit: 1 } ).pop();\n\t\t},\n\n\t\tgetParents: function () {\n\t\t\treturn acf.getFieldObjects( { child: this.$el } );\n\t\t},\n\n\t\tgetFields: function () {\n\t\t\treturn acf.getFieldObjects( { parent: this.$el } );\n\t\t},\n\n\t\tgetInputName: function () {\n\t\t\treturn 'acf_fields[' + this.get( 'id' ) + ']';\n\t\t},\n\n\t\tgetInputId: function () {\n\t\t\treturn 'acf_fields-' + this.get( 'id' );\n\t\t},\n\n\t\tnewInput: function ( name, value ) {\n\t\t\t// vars\n\t\t\tvar inputId = this.getInputId();\n\t\t\tvar inputName = this.getInputName();\n\n\t\t\t// append name\n\t\t\tif ( name ) {\n\t\t\t\tinputId += '-' + name;\n\t\t\t\tinputName += '[' + name + ']';\n\t\t\t}\n\n\t\t\t// create input (avoid HTML + JSON value issues)\n\t\t\tvar $input = $( '<input />' ).attr( {\n\t\t\t\tid: inputId,\n\t\t\t\tname: inputName,\n\t\t\t\tvalue: value,\n\t\t\t} );\n\t\t\tthis.$( '> .meta' ).append( $input );\n\n\t\t\t// return\n\t\t\treturn $input;\n\t\t},\n\n\t\tgetProp: function ( name ) {\n\t\t\t// check data\n\t\t\tif ( this.has( name ) ) {\n\t\t\t\treturn this.get( name );\n\t\t\t}\n\n\t\t\t// get input value\n\t\t\tvar $input = this.$input( name );\n\t\t\tvar value = $input.length ? $input.val() : null;\n\n\t\t\t// set data silently (cache)\n\t\t\tthis.set( name, value, true );\n\n\t\t\t// return\n\t\t\treturn value;\n\t\t},\n\n\t\tsetProp: function ( name, value ) {\n\t\t\t// get input\n\t\t\tvar $input = this.$input( name );\n\t\t\tvar prevVal = $input.val();\n\n\t\t\t// create if new\n\t\t\tif ( ! $input.length ) {\n\t\t\t\t$input = this.newInput( name, value );\n\t\t\t}\n\n\t\t\t// remove\n\t\t\tif ( value === null ) {\n\t\t\t\t$input.remove();\n\n\t\t\t\t// update\n\t\t\t} else {\n\t\t\t\t$input.val( value );\n\t\t\t}\n\n\t\t\t//console.log('setProp', name, value, this);\n\n\t\t\t// set data silently (cache)\n\t\t\tif ( ! this.has( name ) ) {\n\t\t\t\t//console.log('setting silently');\n\t\t\t\tthis.set( name, value, true );\n\n\t\t\t\t// set data allowing 'change' event to fire\n\t\t\t} else {\n\t\t\t\t//console.log('setting loudly!');\n\t\t\t\tthis.set( name, value );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn this;\n\t\t},\n\n\t\tprop: function ( name, value ) {\n\t\t\tif ( value !== undefined ) {\n\t\t\t\treturn this.setProp( name, value );\n\t\t\t} else {\n\t\t\t\treturn this.getProp( name );\n\t\t\t}\n\t\t},\n\n\t\tprops: function ( props ) {\n\t\t\tObject.keys( props ).map( function ( key ) {\n\t\t\t\tthis.setProp( key, props[ key ] );\n\t\t\t}, this );\n\t\t},\n\n\t\tgetLabel: function () {\n\t\t\t// get label with empty default\n\t\t\tvar label = this.prop( 'label' );\n\t\t\tif ( label === '' ) {\n\t\t\t\tlabel = acf.__( '(no label)' );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn label;\n\t\t},\n\n\t\tgetName: function () {\n\t\t\treturn this.prop( 'name' );\n\t\t},\n\n\t\tgetType: function () {\n\t\t\treturn this.prop( 'type' );\n\t\t},\n\n\t\tgetTypeLabel: function () {\n\t\t\tvar type = this.prop( 'type' );\n\t\t\tvar types = acf.get( 'fieldTypes' );\n\t\t\treturn types[ type ] ? types[ type ].label : type;\n\t\t},\n\n\t\tgetKey: function () {\n\t\t\treturn this.prop( 'key' );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.checkCopyable();\n\t\t},\n\n\t\tmakeCopyable: function ( text ) {\n\t\t\tif ( ! navigator.clipboard ) return '<span class=\"copyable copy-unsupported\">' + text + '</span>';\n\t\t\treturn '<span class=\"copyable\">' + text + '</span>';\n\t\t},\n\n\t\tcheckCopyable: function () {\n\t\t\tif ( ! navigator.clipboard ) {\n\t\t\t\tthis.$el.find( '.copyable' ).addClass( 'copy-unsupported' );\n\t\t\t}\n\t\t},\n\n\t\tinitializeFieldTypeSelect2: function () {\n\t\t\tif ( this.fieldTypeSelect2 ) return;\n\n\t\t\t// Support disabling via filter.\n\t\t\tif ( this.$fieldTypeSelect().hasClass( 'disable-select2' ) ) return;\n\n\t\t\t// Check for a full modern version of select2, bail loading if not found with a console warning.\n\t\t\ttry {\n\t\t\t\t$.fn.select2.amd.require( 'select2/compat/dropdownCss' );\n\t\t\t} catch ( err ) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'ACF was not able to load the full version of select2 due to a conflicting version provided by another plugin or theme taking precedence. Select2 fields may not work as expected.'\n\t\t\t\t);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.fieldTypeSelect2 = acf.newSelect2( this.$fieldTypeSelect(), {\n\t\t\t\tfield: false,\n\t\t\t\tajax: false,\n\t\t\t\tmultiple: false,\n\t\t\t\tallowNull: false,\n\t\t\t\tsuppressFilters: true,\n\t\t\t\tdropdownCssClass: 'field-type-select-results',\n\t\t\t\ttemplateResult: function ( selection ) {\n\t\t\t\t\tif ( selection.loading || ( selection.element && selection.element.nodeName === 'OPTGROUP' ) ) {\n\t\t\t\t\t\tvar $selection = $( '<span class=\"acf-selection\"></span>' );\n\t\t\t\t\t\t$selection.html( acf.strEscape( selection.text ) );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar $selection = $(\n\t\t\t\t\t\t\t'<i class=\"field-type-icon field-type-icon-' +\n\t\t\t\t\t\t\t\tselection.id.replaceAll( '_', '-' ) +\n\t\t\t\t\t\t\t\t'\"></i><span class=\"acf-selection has-icon\">' +\n\t\t\t\t\t\t\t\tacf.strEscape( selection.text ) +\n\t\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t$selection.data( 'element', selection.element );\n\t\t\t\t\treturn $selection;\n\t\t\t\t},\n\t\t\t\ttemplateSelection: function ( selection ) {\n\t\t\t\t\tvar $selection = $(\n\t\t\t\t\t\t'<i class=\"field-type-icon field-type-icon-' +\n\t\t\t\t\t\t\tselection.id.replaceAll( '_', '-' ) +\n\t\t\t\t\t\t\t'\"></i><span class=\"acf-selection has-icon\">' +\n\t\t\t\t\t\t\tacf.strEscape( selection.text ) +\n\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t);\n\t\t\t\t\t$selection.data( 'element', selection.element );\n\t\t\t\t\treturn $selection;\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\tthis.fieldTypeSelect2.on( 'select2:open', function () {\n\t\t\t\t$( '.field-type-select-results input.select2-search__field' ).attr(\n\t\t\t\t\t'placeholder',\n\t\t\t\t\tacf.__( 'Type to search...' )\n\t\t\t\t);\n\t\t\t} );\n\n\t\t\tthis.fieldTypeSelect2.on( 'change', function ( e ) {\n\t\t\t\t$( e.target ).parents( 'ul:first' ).find( 'button.browse-fields' ).prop( 'disabled', true );\n\t\t\t} );\n\n\t\t\t// When typing happens on the li element above the select2.\n\t\t\tthis.fieldTypeSelect2.$el\n\t\t\t\t.parent()\n\t\t\t\t.on( 'keydown', '.select2-selection.select2-selection--single', this.onKeyDownSelect );\n\t\t},\n\n\t\taddProFields: function () {\n\t\t\t// Don't run if we have a valid license.\n\t\t\tif ( acf.get( 'is_pro' ) && acf.get( 'isLicenseActive' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Make sure we haven't appended these fields before.\n\t\t\tvar $fieldTypeSelect = this.$fieldTypeSelect();\n\t\t\tif ( $fieldTypeSelect.hasClass( 'acf-free-field-type' ) ) return;\n\n\t\t\t// Loop over each pro field type and append it to the select.\n\t\t\tconst PROFieldTypes = acf.get( 'PROFieldTypes' );\n\t\t\tif ( typeof PROFieldTypes !== 'object' ) return;\n\n\t\t\tconst $layoutGroup = $fieldTypeSelect.find( 'optgroup option[value=\"group\"]' ).parent();\n\n\t\t\tconst $contentGroup = $fieldTypeSelect.find( 'optgroup option[value=\"image\"]' ).parent();\n\n\t\t\tfor ( const [ name, field ] of Object.entries( PROFieldTypes ) ) {\n\t\t\t\tconst $useGroup = field.category === 'content' ? $contentGroup : $layoutGroup;\n\t\t\t\tconst $existing = $useGroup.children( '[value=\"' + name + '\"]' );\n\t\t\t\tconst label = `${ acf.strEscape( field.label ) } (${ acf.strEscape( acf.__( 'PRO Only' ) ) })`;\n\n\t\t\t\tif ( $existing.length ) {\n\t\t\t\t\t// Already added by pro, update existing option.\n\t\t\t\t\t$existing.text( label );\n\n\t\t\t\t\t// Don't disable if already selected (prevents re-save from overriding field type).\n\t\t\t\t\tif ( $fieldTypeSelect.val() !== name ) {\n\t\t\t\t\t\t$existing.attr( 'disabled', 'disabled' );\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// Append new disabled option.\n\t\t\t\t\t$useGroup.append( `<option value=\"null\" disabled=\"disabled\">${ label }</option>` );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t$fieldTypeSelect.addClass( 'acf-free-field-type' );\n\t\t},\n\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar $handle = this.$( '.handle:first' );\n\t\t\tvar menu_order = this.prop( 'menu_order' );\n\t\t\tvar label = this.getLabel();\n\t\t\tvar name = this.prop( 'name' );\n\t\t\tvar type = this.getTypeLabel();\n\t\t\tvar key = this.prop( 'key' );\n\t\t\tvar required = this.$input( 'required' ).prop( 'checked' );\n\n\t\t\t// update menu order\n\t\t\t$handle.find( '.acf-icon' ).html( parseInt( menu_order ) + 1 );\n\n\t\t\t// update required\n\t\t\tif ( required ) {\n\t\t\t\tlabel += ' <span class=\"acf-required\">*</span>';\n\t\t\t}\n\n\t\t\t// update label\n\t\t\t$handle.find( '.li-field-label strong a' ).html( label );\n\n\t\t\t// update name\n\t\t\t$handle.find( '.li-field-name' ).html( this.makeCopyable( acf.strSanitize( name ) ) );\n\n\t\t\t// update type\n\t\t\tconst iconName = acf.strSlugify( this.getType() );\n\t\t\t$handle.find( '.field-type-label' ).text( ' ' + type );\n\t\t\t$handle\n\t\t\t\t.find( '.field-type-icon' )\n\t\t\t\t.removeClass()\n\t\t\t\t.addClass( 'field-type-icon field-type-icon-' + iconName );\n\n\t\t\t// update key\n\t\t\t$handle.find( '.li-field-key' ).html( this.makeCopyable( key ) );\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'render_field_object', this );\n\t\t},\n\n\t\trefresh: function () {\n\t\t\tacf.doAction( 'refresh_field_object', this );\n\t\t},\n\n\t\tisOpen: function () {\n\t\t\treturn this.$el.hasClass( 'open' );\n\t\t},\n\n\t\tonClickCopy: function ( e ) {\n\t\t\te.stopPropagation();\n\t\t\tif ( ! navigator.clipboard || $( e.target ).is( 'input' ) ) return;\n\n\t\t\t// Find the value to copy depending on input or text elements.\n\t\t\tlet copyValue;\n\t\t\tif ( $( e.target ).hasClass( 'acf-input-wrap' ) ) {\n\t\t\t\tcopyValue = $( e.target ).find( 'input' ).first().val();\n\t\t\t} else {\n\t\t\t\tcopyValue = $( e.target ).text().trim();\n\t\t\t}\n\n\t\t\tnavigator.clipboard.writeText( copyValue ).then( () => {\n\t\t\t\t$( e.target ).closest( '.copyable' ).addClass( 'copied' );\n\t\t\t\tsetTimeout( function () {\n\t\t\t\t\t$( e.target ).closest( '.copyable' ).removeClass( 'copied' );\n\t\t\t\t}, 2000 );\n\t\t\t} );\n\t\t},\n\n\t\tonClickEdit: function ( e ) {\n\t\t\tconst $target = $( e.target );\n\n\t\t\t// Bail out if a pro field without a license.\n\t\t\tif (\n\t\t\t\tacf.get( 'is_pro' ) &&\n\t\t\t\t! acf.get( 'isLicenseActive' ) &&\n\t\t\t\t! acf.get( 'isLicenseExpired' ) &&\n\t\t\t\tacf.get( 'PROFieldTypes' ).hasOwnProperty( this.getType() )\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( $target.parent().hasClass( 'row-options' ) && ! $target.hasClass( 'edit-field' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.isOpen() ? this.close() : this.open();\n\t\t},\n\n\t\tonChangeSettingsTab: function () {\n\t\t\tconst $settings = this.$el.children( '.settings' );\n\t\t\tacf.doAction( 'show', $settings );\n\t\t},\n\n\t\t/**\n\t\t * Adds 'active' class to row options nearest to the target.\n\t\t */\n\t\tonFocusEdit: function ( e ) {\n\t\t\tvar $rowOptions = $( e.target ).closest( 'li' ).find( '.row-options' );\n\t\t\t$rowOptions.addClass( 'active' );\n\t\t},\n\n\t\t/**\n\t\t * Removes 'active' class from row options if links in same row options area are no longer in focus.\n\t\t */\n\t\tonBlurEdit: function ( e ) {\n\t\t\tvar focusDelayMilliseconds = 50;\n\t\t\tvar $rowOptionsBlurElement = $( e.target ).closest( 'li' ).find( '.row-options' );\n\n\t\t\t// Timeout so that `activeElement` gives the new element in focus instead of the body.\n\t\t\tsetTimeout( function () {\n\t\t\t\tvar $rowOptionsFocusElement = $( document.activeElement ).closest( 'li' ).find( '.row-options' );\n\t\t\t\tif ( ! $rowOptionsBlurElement.is( $rowOptionsFocusElement ) ) {\n\t\t\t\t\t$rowOptionsBlurElement.removeClass( 'active' );\n\t\t\t\t}\n\t\t\t}, focusDelayMilliseconds );\n\t\t},\n\n\t\topen: function () {\n\t\t\t// vars\n\t\t\tvar $settings = this.$el.children( '.settings' );\n\n\t\t\t// initialise field type select\n\t\t\tthis.addProFields();\n\t\t\tthis.initializeFieldTypeSelect2();\n\n\t\t\t// action (open)\n\t\t\tacf.doAction( 'open_field_object', this );\n\t\t\tthis.trigger( 'openFieldObject' );\n\n\t\t\t// action (show)\n\t\t\tacf.doAction( 'show', $settings );\n\n\t\t\tthis.hideEmptyTabs();\n\n\t\t\t// open\n\t\t\t$settings.slideDown();\n\t\t\tthis.$el.addClass( 'open' );\n\t\t},\n\n\t\tonKeyDownSelect: function ( e ) {\n\t\t\t// Omit events from special keys.\n\t\t\tif (\n\t\t\t\t! (\n\t\t\t\t\t( e.which >= 186 && e.which <= 222 ) || // punctuation and special characters\n\t\t\t\t\t[\n\t\t\t\t\t\t8, 9, 13, 16, 17, 18, 19, 20, 27, 32, 33, 34, 35, 36, 37, 38, 39, 40, 45, 46, 91, 92, 93, 144,\n\t\t\t\t\t\t145,\n\t\t\t\t\t].includes( e.which ) || // Special keys\n\t\t\t\t\t( e.which >= 112 && e.which <= 123 )\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\t// Function keys\n\t\t\t\t$( this ).closest( '.select2-container' ).siblings( 'select:enabled' ).select2( 'open' );\n\t\t\t\treturn;\n\t\t\t}\n\t\t},\n\n\t\tclose: function () {\n\t\t\t// vars\n\t\t\tvar $settings = this.$el.children( '.settings' );\n\n\t\t\t// close\n\t\t\t$settings.slideUp();\n\t\t\tthis.$el.removeClass( 'open' );\n\n\t\t\t// action (close)\n\t\t\tacf.doAction( 'close_field_object', this );\n\t\t\tthis.trigger( 'closeFieldObject' );\n\n\t\t\t// action (hide)\n\t\t\tacf.doAction( 'hide', $settings );\n\t\t},\n\n\t\tserialize: function () {\n\t\t\treturn acf.serialize( this.$el, this.getInputName() );\n\t\t},\n\n\t\tsave: function ( type ) {\n\t\t\t// defaults\n\t\t\ttype = type || 'settings'; // meta, settings\n\n\t\t\t// vars\n\t\t\tvar save = this.getProp( 'save' );\n\n\t\t\t// bail if already saving settings\n\t\t\tif ( save === 'settings' ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// prop\n\t\t\tthis.setProp( 'save', type );\n\n\t\t\t// debug\n\t\t\tthis.$el.attr( 'data-save', type );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'save_field_object', this, type );\n\t\t},\n\n\t\tsubmit: function () {\n\t\t\t// vars\n\t\t\tvar inputName = this.getInputName();\n\t\t\tvar save = this.get( 'save' );\n\n\t\t\t// close\n\t\t\tif ( this.isOpen() ) {\n\t\t\t\tthis.close();\n\t\t\t}\n\n\t\t\t// allow all inputs to save\n\t\t\tif ( save == 'settings' ) {\n\t\t\t\t// do nothing\n\t\t\t\t// allow only meta inputs to save\n\t\t\t} else if ( save == 'meta' ) {\n\t\t\t\tthis.$( '> .settings [name^=\"' + inputName + '\"]' ).remove();\n\n\t\t\t\t// prevent all inputs from saving\n\t\t\t} else {\n\t\t\t\tthis.$( '[name^=\"' + inputName + '\"]' ).remove();\n\t\t\t}\n\n\t\t\t// action\n\t\t\tacf.doAction( 'submit_field_object', this );\n\t\t},\n\n\t\tonChange: function ( e, $el ) {\n\t\t\t// save settings\n\t\t\tthis.save();\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'change_field_object', this );\n\t\t},\n\n\t\tonChanged: function ( e, $el, name, value ) {\n\t\t\tif ( this.getType() === $el.attr( 'data-type' ) ) {\n\t\t\t\t$( 'button.acf-btn.browse-fields' ).prop( 'disabled', false );\n\t\t\t}\n\n\t\t\t// ignore 'save'\n\t\t\tif ( name == 'save' ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// save meta\n\t\t\tif ( [ 'menu_order', 'parent' ].indexOf( name ) > -1 ) {\n\t\t\t\tthis.save( 'meta' );\n\n\t\t\t\t// save field\n\t\t\t} else {\n\t\t\t\tthis.save();\n\t\t\t}\n\n\t\t\t// render\n\t\t\tif ( [ 'menu_order', 'label', 'required', 'name', 'type', 'key' ].indexOf( name ) > -1 ) {\n\t\t\t\tthis.render();\n\t\t\t}\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'change_field_object_' + name, this, value );\n\t\t},\n\n\t\tonChangeLabel: function ( e, $el ) {\n\t\t\t// set\n\t\t\tconst label = $el.val();\n\t\t\tconst safeLabel = acf.encode( label );\n\t\t\tthis.set( 'label', safeLabel );\n\n\t\t\t// render name\n\t\t\tif ( this.prop( 'name' ) == '' ) {\n\t\t\t\tvar name = acf.applyFilters( 'generate_field_object_name', acf.strSanitize( label ), this );\n\t\t\t\tthis.prop( 'name', name );\n\t\t\t}\n\t\t},\n\n\t\tonChangeName: function ( e, $el ) {\n\t\t\tconst sanitizedName = acf.strSanitize( $el.val(), false );\n\n\t\t\t$el.val( sanitizedName );\n\t\t\tthis.set( 'name', sanitizedName );\n\n\t\t\tif ( sanitizedName.startsWith( 'field_' ) ) {\n\t\t\t\talert( acf.__( 'The string \"field_\" may not be used at the start of a field name' ) );\n\t\t\t}\n\t\t},\n\n\t\tonChangeRequired: function ( e, $el ) {\n\t\t\t// set\n\t\t\tvar required = $el.prop( 'checked' ) ? 1 : 0;\n\t\t\tthis.set( 'required', required );\n\t\t},\n\n\t\tdelete: function ( args ) {\n\t\t\t// defaults\n\t\t\targs = acf.parseArgs( args, {\n\t\t\t\tanimate: true,\n\t\t\t} );\n\n\t\t\t// add to remove list\n\t\t\tvar id = this.prop( 'ID' );\n\n\t\t\tif ( id ) {\n\t\t\t\tvar $input = $( '#_acf_delete_fields' );\n\t\t\t\tvar newVal = $input.val() + '|' + id;\n\t\t\t\t$input.val( newVal );\n\t\t\t}\n\n\t\t\t// action\n\t\t\tacf.doAction( 'delete_field_object', this );\n\n\t\t\t// animate\n\t\t\tif ( args.animate ) {\n\t\t\t\tthis.removeAnimate();\n\t\t\t} else {\n\t\t\t\tthis.remove();\n\t\t\t}\n\t\t},\n\n\t\tonClickDelete: function ( e, $el ) {\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.delete();\n\t\t\t}\n\n\t\t\t// add class\n\t\t\tthis.$el.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.delete();\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\tthis.$el.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tremoveAnimate: function () {\n\t\t\t// vars\n\t\t\tvar field = this;\n\t\t\tvar $list = this.$el.parent();\n\t\t\tvar $fields = acf.findFieldObjects( {\n\t\t\t\tsibling: this.$el,\n\t\t\t} );\n\n\t\t\t// remove\n\t\t\tacf.remove( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tendHeight: $fields.length ? 0 : 50,\n\t\t\t\tcomplete: function () {\n\t\t\t\t\tfield.remove();\n\t\t\t\t\tacf.doAction( 'removed_field_object', field, $list );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'remove_field_object', field, $list );\n\t\t},\n\n\t\tduplicate: function () {\n\t\t\t// vars\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// duplicate\n\t\t\tvar $newField = acf.duplicate( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tsearch: this.get( 'id' ),\n\t\t\t\treplace: newKey,\n\t\t\t} );\n\n\t\t\t// set new key\n\t\t\t$newField.attr( 'data-key', newKey );\n\n\t\t\t// get instance\n\t\t\tvar newField = acf.getFieldObject( $newField );\n\n\t\t\t// update newField label / name\n\t\t\tvar label = newField.prop( 'label' );\n\t\t\tvar name = newField.prop( 'name' );\n\t\t\tvar end = name.split( '_' ).pop();\n\t\t\tvar copy = acf.__( 'copy' );\n\n\t\t\t// increase suffix \"1\"\n\t\t\tif ( acf.isNumeric( end ) ) {\n\t\t\t\tvar i = end * 1 + 1;\n\t\t\t\tlabel = label.replace( end, i );\n\t\t\t\tname = name.replace( end, i );\n\n\t\t\t\t// increase suffix \"(copy1)\"\n\t\t\t} else if ( end.indexOf( copy ) === 0 ) {\n\t\t\t\tvar i = end.replace( copy, '' ) * 1;\n\t\t\t\ti = i ? i + 1 : 2;\n\n\t\t\t\t// replace\n\t\t\t\tlabel = label.replace( end, copy + i );\n\t\t\t\tname = name.replace( end, copy + i );\n\n\t\t\t\t// add default \"(copy)\"\n\t\t\t} else {\n\t\t\t\tlabel += ' (' + copy + ')';\n\t\t\t\tname += '_' + copy;\n\t\t\t}\n\n\t\t\tnewField.prop( 'ID', 0 );\n\t\t\tnewField.prop( 'label', label );\n\t\t\tnewField.prop( 'name', name );\n\t\t\tnewField.prop( 'key', newKey );\n\n\t\t\t// close the current field if it's open.\n\t\t\tif ( this.isOpen() ) {\n\t\t\t\tthis.close();\n\t\t\t}\n\n\t\t\t// open the new field and initialise correctly.\n\t\t\tnewField.open();\n\n\t\t\t// focus label\n\t\t\tvar $label = newField.$setting( 'label input' );\n\t\t\tsetTimeout( function () {\n\t\t\t\t$label.trigger( 'focus' );\n\t\t\t}, 251 );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'duplicate_field_object', this, newField );\n\t\t\tacf.doAction( 'append_field_object', newField );\n\t\t},\n\n\t\twipe: function () {\n\t\t\t// vars\n\t\t\tvar prevId = this.get( 'id' );\n\t\t\tvar prevKey = this.get( 'key' );\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// rename\n\t\t\tacf.rename( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tsearch: prevId,\n\t\t\t\treplace: newKey,\n\t\t\t} );\n\n\t\t\t// data\n\t\t\tthis.set( 'id', newKey );\n\t\t\tthis.set( 'prevId', prevId );\n\t\t\tthis.set( 'prevKey', prevKey );\n\n\t\t\t// props\n\t\t\tthis.prop( 'key', newKey );\n\t\t\tthis.prop( 'ID', 0 );\n\n\t\t\t// attr\n\t\t\tthis.$el.attr( 'data-key', newKey );\n\t\t\tthis.$el.attr( 'data-id', newKey );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'wipe_field_object', this );\n\t\t},\n\n\t\tmove: function () {\n\t\t\t// helper\n\t\t\tvar hasChanged = function ( field ) {\n\t\t\t\treturn field.get( 'save' ) == 'settings';\n\t\t\t};\n\n\t\t\t// vars\n\t\t\tvar changed = hasChanged( this );\n\n\t\t\t// has sub fields changed\n\t\t\tif ( ! changed ) {\n\t\t\t\tacf.getFieldObjects( {\n\t\t\t\t\tparent: this.$el,\n\t\t\t\t} ).map( function ( field ) {\n\t\t\t\t\tchanged = hasChanged( field ) || field.changed;\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// bail early if changed\n\t\t\tif ( changed ) {\n\t\t\t\talert( acf.__( 'This field cannot be moved until its changes have been saved' ) );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// step 1.\n\t\t\tvar id = this.prop( 'ID' );\n\t\t\tvar field = this;\n\t\t\tvar popup = false;\n\t\t\tvar step1 = function () {\n\t\t\t\t// popup\n\t\t\t\tpopup = acf.newPopup( {\n\t\t\t\t\ttitle: acf.__( 'Move Custom Field' ),\n\t\t\t\t\tloading: true,\n\t\t\t\t\twidth: '300px',\n\t\t\t\t\topenedBy: field.$el.find( '.move-field' ),\n\t\t\t\t} );\n\n\t\t\t\t// ajax\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/field_group/move_field',\n\t\t\t\t\tfield_id: id,\n\t\t\t\t};\n\n\t\t\t\t// get HTML\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tsuccess: step2,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tvar step2 = function ( html ) {\n\t\t\t\t// update popup\n\t\t\t\tpopup.loading( false );\n\t\t\t\tpopup.content( html );\n\n\t\t\t\t// submit form\n\t\t\t\tpopup.on( 'submit', 'form', step3 );\n\t\t\t};\n\n\t\t\tvar step3 = function ( e, $el ) {\n\t\t\t\t// prevent\n\t\t\t\te.preventDefault();\n\n\t\t\t\t// disable\n\t\t\t\tacf.startButtonLoading( popup.$( '.button' ) );\n\n\t\t\t\t// ajax\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/field_group/move_field',\n\t\t\t\t\tfield_id: id,\n\t\t\t\t\tfield_group_id: popup.$( 'select' ).val(),\n\t\t\t\t};\n\n\t\t\t\t// get HTML\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tsuccess: step4,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tvar step4 = function ( html ) {\n\t\t\t\tpopup.content( html );\n\n\t\t\t\tif ( wp.a11y && wp.a11y.speak && acf.__ ) {\n\t\t\t\t\twp.a11y.speak( acf.__( 'Field moved to other group' ), 'polite' );\n\t\t\t\t}\n\n\t\t\t\tpopup.$( '.acf-close-popup' ).focus();\n\n\t\t\t\tfield.removeAnimate();\n\t\t\t};\n\n\t\t\t// start\n\t\t\tstep1();\n\t\t},\n\n\t\tbrowseFields: function ( e, $el ) {\n\t\t\te.preventDefault();\n\n\t\t\tconst modal = acf.newBrowseFieldsModal( {\n\t\t\t\topenedBy: this,\n\t\t\t} );\n\t\t},\n\n\t\tonChangeType: function ( e, $el ) {\n\t\t\t// clea previous timout\n\t\t\tif ( this.changeTimeout ) {\n\t\t\t\tclearTimeout( this.changeTimeout );\n\t\t\t}\n\n\t\t\t// set new timeout\n\t\t\t// - prevents changing type multiple times whilst user types in newType\n\t\t\tthis.changeTimeout = this.setTimeout( function () {\n\t\t\t\tthis.changeType( $el.val() );\n\t\t\t}, 300 );\n\t\t},\n\n\t\tchangeType: function ( newType ) {\n\t\t\tvar prevType = this.prop( 'type' );\n\t\t\tvar prevClass = acf.strSlugify( 'acf-field-object-' + prevType );\n\t\t\tvar newClass = acf.strSlugify( 'acf-field-object-' + newType );\n\n\t\t\t// Update props.\n\t\t\tthis.$el.removeClass( prevClass ).addClass( newClass );\n\t\t\tthis.$el.attr( 'data-type', newType );\n\t\t\tthis.$el.data( 'type', newType );\n\n\t\t\t// Abort XHR if this field is already loading AJAX data.\n\t\t\tif ( this.has( 'xhr' ) ) {\n\t\t\t\tthis.get( 'xhr' ).abort();\n\t\t\t}\n\n\t\t\t// Store old settings so they can be reused later.\n\t\t\tconst $oldSettings = {};\n\n\t\t\tthis.$el\n\t\t\t\t.find( '.acf-field-settings:first > .acf-field-settings-main > .acf-field-type-settings' )\n\t\t\t\t.each( function () {\n\t\t\t\t\tlet tab = $( this ).data( 'parent-tab' );\n\t\t\t\t\tlet $tabSettings = $( this ).children().removeData();\n\n\t\t\t\t\t$oldSettings[ tab ] = $tabSettings;\n\n\t\t\t\t\t$tabSettings.detach();\n\t\t\t\t} );\n\n\t\t\tthis.set( 'settings-' + prevType, $oldSettings );\n\n\t\t\t// Show the settings if we already have them cached.\n\t\t\tif ( this.has( 'settings-' + newType ) ) {\n\t\t\t\tlet $newSettings = this.get( 'settings-' + newType );\n\n\t\t\t\tthis.showFieldTypeSettings( $newSettings );\n\t\t\t\tthis.set( 'type', newType );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Add loading spinner.\n\t\t\tconst $loading = $(\n\t\t\t\t'<div class=\"acf-field\"><div class=\"acf-input\"><div class=\"acf-loading\"></div></div></div>'\n\t\t\t);\n\t\t\tthis.$el.find( '.acf-field-settings-main-general .acf-field-type-settings' ).before( $loading );\n\n\t\t\tconst ajaxData = {\n\t\t\t\taction: 'acf/field_group/render_field_settings',\n\t\t\t\tfield: this.serialize(),\n\t\t\t\tprefix: this.getInputName(),\n\t\t\t};\n\n\t\t\t// Get the settings for this field type over AJAX.\n\t\t\tvar xhr = $.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'json',\n\t\t\t\tcontext: this,\n\t\t\t\tsuccess: function ( response ) {\n\t\t\t\t\tif ( ! acf.isAjaxSuccess( response ) ) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.showFieldTypeSettings( response.data );\n\t\t\t\t},\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t// also triggered by xhr.abort();\n\t\t\t\t\t$loading.remove();\n\t\t\t\t\tthis.set( 'type', newType );\n\t\t\t\t\t//this.refresh();\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// set\n\t\t\tthis.set( 'xhr', xhr );\n\t\t},\n\n\t\tshowFieldTypeSettings: function ( settings ) {\n\t\t\tif ( 'object' !== typeof settings ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = this;\n\t\t\tconst tabs = Object.keys( settings );\n\n\t\t\ttabs.forEach( ( tab ) => {\n\t\t\t\tconst $tab = self.$el.find(\n\t\t\t\t\t'.acf-field-settings-main-' + tab.replace( '_', '-' ) + ' .acf-field-type-settings'\n\t\t\t\t);\n\t\t\t\tlet tabContent = '';\n\n\t\t\t\tif ( [ 'object', 'string' ].includes( typeof settings[ tab ] ) ) {\n\t\t\t\t\ttabContent = settings[ tab ];\n\t\t\t\t}\n\n\t\t\t\t$tab.prepend( tabContent );\n\t\t\t\tacf.doAction( 'append', $tab );\n\t\t\t} );\n\n\t\t\tthis.hideEmptyTabs();\n\t\t},\n\n\t\tupdateParent: function () {\n\t\t\t// vars\n\t\t\tvar ID = acf.get( 'post_id' );\n\n\t\t\t// check parent\n\t\t\tvar parent = this.getParent();\n\t\t\tif ( parent ) {\n\t\t\t\tID = parseInt( parent.prop( 'ID' ) ) || parent.prop( 'key' );\n\t\t\t}\n\n\t\t\t// update\n\t\t\tthis.prop( 'parent', ID );\n\t\t},\n\n\t\thideEmptyTabs: function () {\n\t\t\tconst $settings = this.$settings();\n\t\t\tconst $tabs = $settings.find( '.acf-field-settings:first > .acf-field-settings-main' );\n\n\t\t\t$tabs.each( function () {\n\t\t\t\tconst $tabContent = $( this );\n\t\t\t\tconst tabName = $tabContent.find( '.acf-field-type-settings:first' ).data( 'parentTab' );\n\t\t\t\tconst $tabLink = $settings.find( '.acf-settings-type-' + tabName ).first();\n\n\t\t\t\tif ( $.trim( $tabContent.text() ) === '' ) {\n\t\t\t\t\t$tabLink.hide();\n\t\t\t\t} else if ( $tabLink.is( ':hidden' ) ) {\n\t\t\t\t\t$tabLink.show();\n\t\t\t\t}\n\t\t\t} );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  acf.findFieldObject\n\t *\n\t *  Returns a single fieldObject $el for a given field key\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tstring key The field key\n\t *  @return\tjQuery\n\t */\n\n\tacf.findFieldObject = function ( key ) {\n\t\treturn acf.findFieldObjects( {\n\t\t\tkey: key,\n\t\t\tlimit: 1,\n\t\t} );\n\t};\n\n\t/**\n\t *  acf.findFieldObjects\n\t *\n\t *  Returns an array of fieldObject $el for the given args\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tobject args\n\t *  @return\tjQuery\n\t */\n\n\tacf.findFieldObjects = function ( args ) {\n\t\t// vars\n\t\targs = args || {};\n\t\tvar selector = '.acf-field-object';\n\t\tvar $fields = false;\n\n\t\t// args\n\t\targs = acf.parseArgs( args, {\n\t\t\tid: '',\n\t\t\tkey: '',\n\t\t\ttype: '',\n\t\t\tlimit: false,\n\t\t\tlist: null,\n\t\t\tparent: false,\n\t\t\tsibling: false,\n\t\t\tchild: false,\n\t\t} );\n\n\t\t// id\n\t\tif ( args.id ) {\n\t\t\tselector += '[data-id=\"' + args.id + '\"]';\n\t\t}\n\n\t\t// key\n\t\tif ( args.key ) {\n\t\t\tselector += '[data-key=\"' + args.key + '\"]';\n\t\t}\n\n\t\t// type\n\t\tif ( args.type ) {\n\t\t\tselector += '[data-type=\"' + args.type + '\"]';\n\t\t}\n\n\t\t// query\n\t\tif ( args.list ) {\n\t\t\t$fields = args.list.children( selector );\n\t\t} else if ( args.parent ) {\n\t\t\t$fields = args.parent.find( selector );\n\t\t} else if ( args.sibling ) {\n\t\t\t$fields = args.sibling.siblings( selector );\n\t\t} else if ( args.child ) {\n\t\t\t$fields = args.child.parents( selector );\n\t\t} else {\n\t\t\t$fields = $( selector );\n\t\t}\n\n\t\t// limit\n\t\tif ( args.limit ) {\n\t\t\t$fields = $fields.slice( 0, args.limit );\n\t\t}\n\n\t\t// return\n\t\treturn $fields;\n\t};\n\n\t/**\n\t *  acf.getFieldObject\n\t *\n\t *  Returns a single fieldObject instance for a given $el|key\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tstring|jQuery $field The field $el or key\n\t *  @return\tjQuery\n\t */\n\n\tacf.getFieldObject = function ( $field ) {\n\t\t// allow key\n\t\tif ( typeof $field === 'string' ) {\n\t\t\t$field = acf.findFieldObject( $field );\n\t\t}\n\n\t\t// instantiate\n\t\tvar field = $field.data( 'acf' );\n\t\tif ( ! field ) {\n\t\t\tfield = acf.newFieldObject( $field );\n\t\t}\n\n\t\t// return\n\t\treturn field;\n\t};\n\n\t/**\n\t *  acf.getFieldObjects\n\t *\n\t *  Returns an array of fieldObject instances for the given args\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tobject args\n\t *  @return\tarray\n\t */\n\n\tacf.getFieldObjects = function ( args ) {\n\t\t// query\n\t\tvar $fields = acf.findFieldObjects( args );\n\n\t\t// loop\n\t\tvar fields = [];\n\t\t$fields.each( function () {\n\t\t\tvar field = acf.getFieldObject( $( this ) );\n\t\t\tfields.push( field );\n\t\t} );\n\n\t\t// return\n\t\treturn fields;\n\t};\n\n\t/**\n\t *  acf.newFieldObject\n\t *\n\t *  Initializes and returns a new FieldObject instance\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tjQuery $field The field $el\n\t *  @return\tobject\n\t */\n\n\tacf.newFieldObject = function ( $field ) {\n\t\t// instantiate\n\t\tvar field = new acf.FieldObject( $field );\n\n\t\t// action\n\t\tacf.doAction( 'new_field_object', field );\n\n\t\t// return\n\t\treturn field;\n\t};\n\n\t/**\n\t *  actionManager\n\t *\n\t *  description\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar eventManager = new acf.Model( {\n\t\tpriority: 5,\n\n\t\tinitialize: function () {\n\t\t\t// actions\n\t\t\tvar actions = [ 'prepare', 'ready', 'append', 'remove' ];\n\n\t\t\t// loop\n\t\t\tactions.map( function ( action ) {\n\t\t\t\tthis.addFieldActions( action );\n\t\t\t}, this );\n\t\t},\n\n\t\taddFieldActions: function ( action ) {\n\t\t\t// vars\n\t\t\tvar pluralAction = action + '_field_objects'; // ready_field_objects\n\t\t\tvar singleAction = action + '_field_object'; // ready_field_object\n\t\t\tvar singleEvent = action + 'FieldObject'; // readyFieldObject\n\n\t\t\t// global action\n\t\t\tvar callback = function ( $el /*, arg1, arg2, etc*/ ) {\n\t\t\t\t// vars\n\t\t\t\tvar fieldObjects = acf.getFieldObjects( { parent: $el } );\n\n\t\t\t\t// call plural\n\t\t\t\tif ( fieldObjects.length ) {\n\t\t\t\t\t/// get args [$el, arg1]\n\t\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t\t// modify args [pluralAction, fields, arg1]\n\t\t\t\t\targs.splice( 0, 1, pluralAction, fieldObjects );\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t}\n\t\t\t};\n\n\t\t\t// plural action\n\t\t\tvar pluralCallback = function (\n\t\t\t\tfieldObjects /*, arg1, arg2, etc*/\n\t\t\t) {\n\t\t\t\t/// get args [fields, arg1]\n\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t// modify args [singleAction, fields, arg1]\n\t\t\t\targs.unshift( singleAction );\n\n\t\t\t\t// loop\n\t\t\t\tfieldObjects.map( function ( fieldObject ) {\n\t\t\t\t\t// modify args [singleAction, field, arg1]\n\t\t\t\t\targs[ 1 ] = fieldObject;\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\t// single action\n\t\t\tvar singleCallback = function (\n\t\t\t\tfieldObject /*, arg1, arg2, etc*/\n\t\t\t) {\n\t\t\t\t/// get args [$field, arg1]\n\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t// modify args [singleAction, $field, arg1]\n\t\t\t\targs.unshift( singleAction );\n\n\t\t\t\t// action variations (ready_field/type=image)\n\t\t\t\tvar variations = [ 'type', 'name', 'key' ];\n\t\t\t\tvariations.map( function ( variation ) {\n\t\t\t\t\targs[ 0 ] =\n\t\t\t\t\t\tsingleAction +\n\t\t\t\t\t\t'/' +\n\t\t\t\t\t\tvariation +\n\t\t\t\t\t\t'=' +\n\t\t\t\t\t\tfieldObject.get( variation );\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t} );\n\n\t\t\t\t// modify args [arg1]\n\t\t\t\targs.splice( 0, 2 );\n\n\t\t\t\t// event\n\t\t\t\tfieldObject.trigger( singleEvent, args );\n\t\t\t};\n\n\t\t\t// add actions\n\t\t\tacf.addAction( action, callback, 5 );\n\t\t\tacf.addAction( pluralAction, pluralCallback, 5 );\n\t\t\tacf.addAction( singleAction, singleCallback, 5 );\n\t\t},\n\t} );\n\n\t/**\n\t *  fieldManager\n\t *\n\t *  description\n\t *\n\t *  @date\t4/1/18\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar fieldManager = new acf.Model( {\n\t\tid: 'fieldManager',\n\n\t\tevents: {\n\t\t\t'submit #post': 'onSubmit',\n\t\t\t'mouseenter .acf-field-list': 'onHoverSortable',\n\t\t\t'click .add-field': 'onClickAdd',\n\t\t},\n\n\t\tactions: {\n\t\t\tremoved_field_object: 'onRemovedField',\n\t\t\tsortstop_field_object: 'onReorderField',\n\t\t\tdelete_field_object: 'onDeleteField',\n\t\t\tchange_field_object_type: 'onChangeFieldType',\n\t\t\tduplicate_field_object: 'onDuplicateField',\n\t\t},\n\n\t\tonSubmit: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar fields = acf.getFieldObjects();\n\n\t\t\t// loop\n\t\t\tfields.map( function ( field ) {\n\t\t\t\tfield.submit();\n\t\t\t} );\n\t\t},\n\n\t\tsetFieldMenuOrder: function ( field ) {\n\t\t\tthis.renderFields( field.$el.parent() );\n\t\t},\n\n\t\tonHoverSortable: function ( e, $el ) {\n\t\t\t// bail early if already sortable\n\t\t\tif ( $el.hasClass( 'ui-sortable' ) ) return;\n\n\t\t\t// sortable\n\t\t\t$el.sortable( {\n\t\t\t\thelper: function( event, element ) {\n\t\t\t\t\t// https://core.trac.wordpress.org/ticket/16972#comment:22\n\t\t\t\t\treturn element.clone()\n\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t\t.attr( 'name', function( i, currentName ) {\n\t\t\t\t\t\t\t\t\treturn 'sort_' + parseInt( Math.random() * 100000, 10 ).toString() + '_' + currentName;\n\t\t\t\t\t\t\t} )\n\t\t\t\t\t\t.end();\n\t\t\t\t},\n\t\t\t\thandle: '.acf-sortable-handle',\n\t\t\t\tconnectWith: '.acf-field-list',\n\t\t\t\tstart: function ( e, ui ) {\n\t\t\t\t\tvar field = acf.getFieldObject( ui.item );\n\t\t\t\t\tui.placeholder.height( ui.item.height() );\n\t\t\t\t\tacf.doAction( 'sortstart_field_object', field, $el );\n\t\t\t\t},\n\t\t\t\tupdate: function ( e, ui ) {\n\t\t\t\t\tvar field = acf.getFieldObject( ui.item );\n\t\t\t\t\tacf.doAction( 'sortstop_field_object', field, $el );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonRemovedField: function ( field, $list ) {\n\t\t\tthis.renderFields( $list );\n\t\t},\n\n\t\tonReorderField: function ( field, $list ) {\n\t\t\tfield.updateParent();\n\t\t\tthis.renderFields( $list );\n\t\t},\n\n\t\tonDeleteField: function ( field ) {\n\t\t\t// delete children\n\t\t\tfield.getFields().map( function ( child ) {\n\t\t\t\tchild.delete( { animate: false } );\n\t\t\t} );\n\t\t},\n\n\t\tonChangeFieldType: function ( field ) {\n\t\t\t// enable browse field modal button\n\t\t\tfield.$el.find( 'button.browse-fields' ).prop( 'disabled', false );\n\t\t},\n\n\t\tonDuplicateField: function ( field, newField ) {\n\t\t\t// check for children\n\t\t\tvar children = newField.getFields();\n\t\t\tif ( children.length ) {\n\t\t\t\t// loop\n\t\t\t\tchildren.map( function ( child ) {\n\t\t\t\t\t// wipe field\n\t\t\t\t\tchild.wipe();\n\n\t\t\t\t\t// if the child is open, re-fire the open method to ensure it's initialised correctly.\n\t\t\t\t\tif ( child.isOpen() ) {\n\t\t\t\t\t\tchild.open();\n\t\t\t\t\t}\n\n\t\t\t\t\t// update parent\n\t\t\t\t\tchild.updateParent();\n\t\t\t\t} );\n\n\t\t\t\t// action\n\t\t\t\tacf.doAction(\n\t\t\t\t\t'duplicate_field_objects',\n\t\t\t\t\tchildren,\n\t\t\t\t\tnewField,\n\t\t\t\t\tfield\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// set menu order\n\t\t\tthis.setFieldMenuOrder( newField );\n\t\t},\n\n\t\trenderFields: function ( $list ) {\n\t\t\t// vars\n\t\t\tvar fields = acf.getFieldObjects( {\n\t\t\t\tlist: $list,\n\t\t\t} );\n\n\t\t\t// no fields\n\t\t\tif ( ! fields.length ) {\n\t\t\t\t$list.addClass( '-empty' );\n\t\t\t\t$list\n\t\t\t\t\t.parents( '.acf-field-list-wrap' )\n\t\t\t\t\t.first()\n\t\t\t\t\t.addClass( '-empty' );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// has fields\n\t\t\t$list.removeClass( '-empty' );\n\t\t\t$list\n\t\t\t\t.parents( '.acf-field-list-wrap' )\n\t\t\t\t.first()\n\t\t\t\t.removeClass( '-empty' );\n\n\t\t\t// prop\n\t\t\tfields.map( function ( field, i ) {\n\t\t\t\tfield.prop( 'menu_order', i );\n\t\t\t} );\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\tlet $list;\n\n\t\t\tif ( $el.hasClass( 'add-first-field' ) ) {\n\t\t\t\t$list = $el.parents( '.acf-field-list' ).eq( 0 );\n\t\t\t} else if (\n\t\t\t\t$el.parent().hasClass( 'acf-headerbar-actions' ) ||\n\t\t\t\t$el.parent().hasClass( 'no-fields-message-inner' )\n\t\t\t) {\n\t\t\t\t$list = $( '.acf-field-list:first' );\n\t\t\t} else if ( $el.parent().hasClass( 'acf-sub-field-list-header' ) ) {\n\t\t\t\t$list = $el\n\t\t\t\t\t.parents( '.acf-input:first' )\n\t\t\t\t\t.find( '.acf-field-list:first' );\n\t\t\t} else {\n\t\t\t\t$list = $el\n\t\t\t\t\t.closest( '.acf-tfoot' )\n\t\t\t\t\t.siblings( '.acf-field-list' );\n\t\t\t}\n\n\t\t\tthis.addField( $list );\n\t\t},\n\n\t\taddField: function ( $list ) {\n\t\t\t// vars\n\t\t\tvar html = $( '#tmpl-acf-field' ).html();\n\t\t\tvar $el = $( html );\n\t\t\tvar prevId = $el.data( 'id' );\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// duplicate\n\t\t\tvar $newField = acf.duplicate( {\n\t\t\t\ttarget: $el,\n\t\t\t\tsearch: prevId,\n\t\t\t\treplace: newKey,\n\t\t\t\tappend: function ( $el, $el2 ) {\n\t\t\t\t\t$list.append( $el2 );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// get instance\n\t\t\tvar newField = acf.getFieldObject( $newField );\n\n\t\t\t// props\n\t\t\tnewField.prop( 'key', newKey );\n\t\t\tnewField.prop( 'ID', 0 );\n\t\t\tnewField.prop( 'label', '' );\n\t\t\tnewField.prop( 'name', '' );\n\n\t\t\t// attr\n\t\t\t$newField.attr( 'data-key', newKey );\n\t\t\t$newField.attr( 'data-id', newKey );\n\n\t\t\t// update parent prop\n\t\t\tnewField.updateParent();\n\n\t\t\t// focus type\n\t\t\tvar $type = newField.$input( 'type' );\n\t\t\tsetTimeout( function () {\n\t\t\t\tif ( $list.hasClass( 'acf-auto-add-field' ) ) {\n\t\t\t\t\t$list.removeClass( 'acf-auto-add-field' );\n\t\t\t\t} else {\n\t\t\t\t\t$type.trigger( 'focus' );\n\t\t\t\t}\n\t\t\t}, 251 );\n\n\t\t\t// open\n\t\t\tnewField.open();\n\n\t\t\t// set menu order\n\t\t\tthis.renderFields( $list );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'add_field_object', newField );\n\t\t\tacf.doAction( 'append_field_object', newField );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  locationManager\n\t *\n\t *  Field group location rules functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar locationManager = new acf.Model( {\n\t\tid: 'locationManager',\n\t\twait: 'ready',\n\n\t\tevents: {\n\t\t\t'click .add-location-rule': 'onClickAddRule',\n\t\t\t'click .add-location-group': 'onClickAddGroup',\n\t\t\t'click .remove-location-rule': 'onClickRemoveRule',\n\t\t\t'change .refresh-location-rule': 'onChangeRemoveRule',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.$el = $( '#acf-field-group-options' );\n\t\t\tthis.addProLocations();\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\taddProLocations: function () {\n\t\t\t// Make sure we're only running if we don't have a valid license.\n\t\t\tif ( acf.get( 'is_pro' ) && acf.get( 'isLicenseActive' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Loop over each pro field type and append it to the select.\n\t\t\tconst PROLocationTypes = acf.get( 'PROLocationTypes' );\n\t\t\tif ( typeof PROLocationTypes !== 'object' ) return;\n\n\t\t\tconst $formsGroup = this.$el\n\t\t\t\t.find( 'select.refresh-location-rule' )\n\t\t\t\t.find( 'optgroup[label=\"Forms\"]' )\n\n\t\t\tconst proOnlyText = ` (${acf.__( 'PRO Only' )})`;\n\n\t\t\tfor ( const [ key, name ] of Object.entries( PROLocationTypes ) ) {\n\t\t\t\tif ( ! acf.get( 'is_pro' ) ) {\n\t\t\t\t\t$formsGroup.append(\n\t\t\t\t\t\t`<option value=\"null\" disabled=\"disabled\">${acf.strEscape( name )}${acf.strEscape( proOnlyText )}</option>`\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\t$formsGroup\n\t\t\t\t\t\t.find( 'option[value=' + key + ']' ).not( ':selected' )\n\t\t\t\t\t\t.prop( 'disabled', 'disabled' )\n\t\t\t\t\t\t.text( `${acf.strEscape( name )}${acf.strEscape( proOnlyText )}` );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst $addNewOptionsPage = this.$el.find( 'select.location-rule-value option[value=add_new_options_page]' );\n\t\t\tif ( $addNewOptionsPage.length ) {\n\t\t\t\t$addNewOptionsPage.attr( 'disabled', 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tonClickAddRule: function ( e, $el ) {\n\t\t\tthis.addRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonClickRemoveRule: function ( e, $el ) {\n\t\t\tthis.removeRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonChangeRemoveRule: function ( e, $el ) {\n\t\t\tthis.changeRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonClickAddGroup: function ( e, $el ) {\n\t\t\tthis.addGroup();\n\t\t},\n\n\t\taddRule: function ( $tr ) {\n\t\t\tacf.duplicate( $tr );\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tremoveRule: function ( $tr ) {\n\t\t\tif ( $tr.siblings( 'tr' ).length == 0 ) {\n\t\t\t\t$tr.closest( '.rule-group' ).remove();\n\t\t\t} else {\n\t\t\t\t$tr.remove();\n\t\t\t}\n\n\t\t\t// Update h4\n\t\t\tvar $group = this.$( '.rule-group:first' );\n\t\t\t$group.find( 'h4' ).text( acf.__( 'Show this field group if' ) );\n\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tchangeRule: function ( $rule ) {\n\t\t\t// vars\n\t\t\tvar $group = $rule.closest( '.rule-group' );\n\t\t\tvar prefix = $rule\n\t\t\t\t.find( 'td.param select' )\n\t\t\t\t.attr( 'name' )\n\t\t\t\t.replace( '[param]', '' );\n\n\t\t\t// ajaxdata\n\t\t\tvar ajaxdata = {};\n\t\t\tajaxdata.action = 'acf/field_group/render_location_rule';\n\t\t\tajaxdata.rule = acf.serialize( $rule, prefix );\n\t\t\tajaxdata.rule.id = $rule.data( 'id' );\n\t\t\tajaxdata.rule.group = $group.data( 'id' );\n\n\t\t\t// temp disable\n\t\t\tacf.disable( $rule.find( 'td.value' ) );\n\n\t\t\tconst self = this;\n\n\t\t\t// ajax\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxdata ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'html',\n\t\t\t\tsuccess: function ( html ) {\n\t\t\t\t\tif ( ! html ) return;\n\t\t\t\t\t$rule.replaceWith( html );\n\t\t\t\t\tself.addProLocations();\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\taddGroup: function () {\n\t\t\t// vars\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\t// duplicate\n\t\t\t$group2 = acf.duplicate( $group );\n\n\t\t\t// update h4\n\t\t\t$group2.find( 'h4' ).text( acf.__( 'or' ) );\n\n\t\t\t// remove all tr's except the first one\n\t\t\t$group2.find( 'tr' ).not( ':first' ).remove();\n\n\t\t\t// update the groups class\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tupdateGroupsClass: function () {\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\tvar $ruleGroups = $group.closest( '.rule-groups' );\n\n\t\t\tvar rows_count = $ruleGroups.find( '.acf-table tr' ).length;\n\n\t\t\tif ( rows_count > 1 ) {\n\t\t\t\t$ruleGroups.addClass( 'rule-groups-multiple' );\n\t\t\t} else {\n\t\t\t\t$ruleGroups.removeClass( 'rule-groups-multiple' );\n\t\t\t}\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  mid\n\t *\n\t *  Calculates the model ID for a field type\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\tstring type\n\t *  @return\tstring\n\t */\n\n\tvar modelId = function ( type ) {\n\t\treturn acf.strPascalCase( type || '' ) + 'FieldSetting';\n\t};\n\n\t/**\n\t *  registerFieldType\n\t *\n\t *  description\n\t *\n\t *  @date\t14/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.registerFieldSetting = function ( model ) {\n\t\tvar proto = model.prototype;\n\t\tvar mid = modelId( proto.type + ' ' + proto.name );\n\t\tthis.models[ mid ] = model;\n\t};\n\n\t/**\n\t *  newField\n\t *\n\t *  description\n\t *\n\t *  @date\t14/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.newFieldSetting = function ( field ) {\n\t\t// vars\n\t\tvar type = field.get( 'setting' ) || '';\n\t\tvar name = field.get( 'name' ) || '';\n\t\tvar mid = modelId( type + ' ' + name );\n\t\tvar model = acf.models[ mid ] || null;\n\n\t\t// bail early if no setting\n\t\tif ( model === null ) return false;\n\n\t\t// instantiate\n\t\tvar setting = new model( field );\n\n\t\t// return\n\t\treturn setting;\n\t};\n\n\t/**\n\t *  acf.getFieldSetting\n\t *\n\t *  description\n\t *\n\t *  @date\t19/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.getFieldSetting = function ( field ) {\n\t\t// allow jQuery\n\t\tif ( field instanceof jQuery ) {\n\t\t\tfield = acf.getField( field );\n\t\t}\n\n\t\t// return\n\t\treturn field.setting;\n\t};\n\n\t/**\n\t * settingsManager\n\t *\n\t * @since\t5.6.5\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar settingsManager = new acf.Model( {\n\t\tactions: {\n\t\t\tnew_field: 'onNewField',\n\t\t},\n\t\tonNewField: function ( field ) {\n\t\t\tfield.setting = acf.newFieldSetting( field );\n\t\t},\n\t} );\n\n\t/**\n\t * acf.FieldSetting\n\t *\n\t * @since\t5.6.5\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tacf.FieldSetting = acf.Model.extend( {\n\t\tfield: false,\n\t\ttype: '',\n\t\tname: '',\n\t\twait: 'ready',\n\t\teventScope: '.acf-field',\n\n\t\tevents: {\n\t\t\tchange: 'render',\n\t\t},\n\n\t\tsetup: function ( field ) {\n\t\t\t// vars\n\t\t\tvar $field = field.$el;\n\n\t\t\t// set props\n\t\t\tthis.$el = $field;\n\t\t\tthis.field = field;\n\t\t\tthis.$fieldObject = $field.closest( '.acf-field-object' );\n\t\t\tthis.fieldObject = acf.getFieldObject( this.$fieldObject );\n\n\t\t\t// inherit data\n\t\t\t$.extend( this.data, field.data );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\t// do nothing\n\t\t},\n\t} );\n\n\t/**\n\t * Accordion and Tab Endpoint Settings\n\t *\n\t * The 'endpoint' setting on accordions and tabs requires an additional class on the\n\t * field object row when enabled.\n\t *\n\t * @since\t6.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar EndpointFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: '',\n\t\trender: function () {\n\t\t\tvar $endpoint_setting = this.fieldObject.$setting( 'endpoint' );\n\t\t\tvar $endpoint_field = $endpoint_setting.find(\n\t\t\t\t'input[type=\"checkbox\"]:first'\n\t\t\t);\n\t\t\tif ( $endpoint_field.is( ':checked' ) ) {\n\t\t\t\tthis.fieldObject.$el.addClass( 'acf-field-is-endpoint' );\n\t\t\t} else {\n\t\t\t\tthis.fieldObject.$el.removeClass( 'acf-field-is-endpoint' );\n\t\t\t}\n\t\t},\n\t} );\n\n\tvar AccordionEndpointFieldSetting = EndpointFieldSetting.extend( {\n\t\ttype: 'accordion',\n\t\tname: 'endpoint',\n\t} );\n\n\tvar TabEndpointFieldSetting = EndpointFieldSetting.extend( {\n\t\ttype: 'tab',\n\t\tname: 'endpoint',\n\t} );\n\n\tacf.registerFieldSetting( AccordionEndpointFieldSetting );\n\tacf.registerFieldSetting( TabEndpointFieldSetting );\n\n\t/**\n\t * Date Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar DisplayFormatFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: '',\n\t\trender: function () {\n\t\t\tvar $input = this.$( 'input[type=\"radio\"]:checked' );\n\t\t\tif ( $input.val() != 'other' ) {\n\t\t\t\tthis.$( 'input[type=\"text\"]' ).val( $input.val() );\n\t\t\t}\n\t\t},\n\t} );\n\n\tvar DatePickerDisplayFormatFieldSetting = DisplayFormatFieldSetting.extend(\n\t\t{\n\t\t\ttype: 'date_picker',\n\t\t\tname: 'display_format',\n\t\t}\n\t);\n\n\tvar DatePickerReturnFormatFieldSetting = DisplayFormatFieldSetting.extend( {\n\t\ttype: 'date_picker',\n\t\tname: 'return_format',\n\t} );\n\n\tacf.registerFieldSetting( DatePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( DatePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Date Time Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar DateTimePickerDisplayFormatFieldSetting =\n\t\tDisplayFormatFieldSetting.extend( {\n\t\t\ttype: 'date_time_picker',\n\t\t\tname: 'display_format',\n\t\t} );\n\n\tvar DateTimePickerReturnFormatFieldSetting =\n\t\tDisplayFormatFieldSetting.extend( {\n\t\t\ttype: 'date_time_picker',\n\t\t\tname: 'return_format',\n\t\t} );\n\n\tacf.registerFieldSetting( DateTimePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( DateTimePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Time Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar TimePickerDisplayFormatFieldSetting = DisplayFormatFieldSetting.extend(\n\t\t{\n\t\t\ttype: 'time_picker',\n\t\t\tname: 'display_format',\n\t\t}\n\t);\n\n\tvar TimePickerReturnFormatFieldSetting = DisplayFormatFieldSetting.extend( {\n\t\ttype: 'time_picker',\n\t\tname: 'return_format',\n\t} );\n\n\tacf.registerFieldSetting( TimePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( TimePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Color Picker Settings.\n\t *\n\t * @date\t16/12/20\n\t * @since\t5.9.4\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar ColorPickerReturnFormat = acf.FieldSetting.extend( {\n\t\ttype: 'color_picker',\n\t\tname: 'enable_opacity',\n\t\trender: function () {\n\t\t\tvar $return_format_setting =\n\t\t\t\tthis.fieldObject.$setting( 'return_format' );\n\t\t\tvar $default_value_setting =\n\t\t\t\tthis.fieldObject.$setting( 'default_value' );\n\t\t\tvar $labelText = $return_format_setting\n\t\t\t\t.find( 'input[type=\"radio\"][value=\"string\"]' )\n\t\t\t\t.parent( 'label' )\n\t\t\t\t.contents()\n\t\t\t\t.last();\n\t\t\tvar $defaultPlaceholder =\n\t\t\t\t$default_value_setting.find( 'input[type=\"text\"]' );\n\t\t\tvar l10n = acf.get( 'colorPickerL10n' );\n\n\t\t\tif ( this.field.val() ) {\n\t\t\t\t$labelText.replaceWith( l10n.rgba_string );\n\t\t\t\t$defaultPlaceholder.attr(\n\t\t\t\t\t'placeholder',\n\t\t\t\t\t'rgba(255,255,255,0.8)'\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t$labelText.replaceWith( l10n.hex_string );\n\t\t\t\t$defaultPlaceholder.attr( 'placeholder', '#FFFFFF' );\n\t\t\t}\n\t\t},\n\t} );\n\tacf.registerFieldSetting( ColorPickerReturnFormat );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  fieldGroupManager\n\t *\n\t *  Generic field group functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar fieldGroupManager = new acf.Model( {\n\t\tid: 'fieldGroupManager',\n\n\t\tevents: {\n\t\t\t'submit #post': 'onSubmit',\n\t\t\t'click a[href=\"#\"]': 'onClick',\n\t\t\t'click .acf-delete-field-group': 'onClickDeleteFieldGroup',\n\t\t\t'blur input#title': 'validateTitle',\n\t\t\t'input input#title': 'validateTitle',\n\t\t},\n\n\t\tfilters: {\n\t\t\tfind_fields_args: 'filterFindFieldArgs',\n\t\t\tfind_fields_selector: 'filterFindFieldsSelector',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tacf.addAction( 'prepare', this.maybeInitNewFieldGroup );\n\t\t\tacf.add_filter( 'select2_args', this.setBidirectionalSelect2Args );\n\t\t\tacf.add_filter(\n\t\t\t\t'select2_ajax_data',\n\t\t\t\tthis.setBidirectionalSelect2AjaxDataArgs\n\t\t\t);\n\t\t},\n\n\t\tsetBidirectionalSelect2Args: function (\n\t\t\targs,\n\t\t\t$select,\n\t\t\tsettings,\n\t\t\tfield,\n\t\t\tinstance\n\t\t) {\n\t\t\tif ( field?.data?.( 'key' ) !== 'bidirectional_target' ) return args;\n\n\t\t\targs.dropdownCssClass = 'field-type-select-results';\n\n\t\t\t// Check for a full modern version of select2 like the one provided by ACF.\n\t\t\ttry {\n\t\t\t\t$.fn.select2.amd.require( 'select2/compat/dropdownCss' );\n\t\t\t} catch ( err ) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'ACF was not able to load the full version of select2 due to a conflicting version provided by another plugin or theme taking precedence. Skipping styling of bidirectional settings.'\n\t\t\t\t);\n\t\t\t\tdelete args.dropdownCssClass;\n\t\t\t}\n\n\t\t\targs.templateResult = function ( selection ) {\n\t\t\t\tif ( 'undefined' !== typeof selection.element ) {\n\t\t\t\t\treturn selection;\n\t\t\t\t}\n\n\t\t\t\tif ( selection.children ) {\n\t\t\t\t\treturn selection.text;\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\tselection.loading ||\n\t\t\t\t\t( selection.element &&\n\t\t\t\t\t\tselection.element.nodeName === 'OPTGROUP' )\n\t\t\t\t) {\n\t\t\t\t\tvar $selection = $( '<span class=\"acf-selection\"></span>' );\n\t\t\t\t\t$selection.html( acf.escHtml( selection.text ) );\n\t\t\t\t\treturn $selection;\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t'undefined' === typeof selection.human_field_type ||\n\t\t\t\t\t'undefined' === typeof selection.field_type ||\n\t\t\t\t\t'undefined' === typeof selection.this_field\n\t\t\t\t) {\n\t\t\t\t\treturn selection.text;\n\t\t\t\t}\n\n\t\t\t\tvar $selection = $(\n\t\t\t\t\t'<i title=\"' +\n\t\t\t\t\t\tacf.escHtml( selection.human_field_type ) +\n\t\t\t\t\t\t'\" class=\"field-type-icon field-type-icon-' +\n\t\t\t\t\t\tacf.escHtml(\n\t\t\t\t\t\t\tselection.field_type.replaceAll( '_', '-' )\n\t\t\t\t\t\t) +\n\t\t\t\t\t\t'\"></i><span class=\"acf-selection has-icon\">' +\n\t\t\t\t\t\tacf.escHtml( selection.text ) +\n\t\t\t\t\t\t'</span>'\n\t\t\t\t);\n\t\t\t\tif ( selection.this_field ) {\n\t\t\t\t\t$selection\n\t\t\t\t\t\t.last()\n\t\t\t\t\t\t.append(\n\t\t\t\t\t\t\t'<span class=\"acf-select2-default-pill\">' +\n\t\t\t\t\t\t\t\tacf.__( 'This Field' ) +\n\t\t\t\t\t\t\t\t'</span>'\n\t\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\t$selection.data( 'element', selection.element );\n\t\t\t\treturn $selection;\n\t\t\t};\n\n\t\t\treturn args;\n\t\t},\n\n\t\tsetBidirectionalSelect2AjaxDataArgs: function (\n\t\t\tdata,\n\t\t\targs,\n\t\t\t$input,\n\t\t\tfield,\n\t\t\tinstance\n\t\t) {\n\t\t\tif ( data.field_key !== 'bidirectional_target' ) return data;\n\n\t\t\tconst $fieldObject = acf.findFieldObjects( { child: field } );\n\t\t\tconst fieldObject = acf.getFieldObject( $fieldObject );\n\t\t\tdata.field_key = '_acf_bidirectional_target';\n\t\t\tdata.parent_key = fieldObject.get( 'key' );\n\t\t\tdata.field_type = fieldObject.get( 'type' );\n\n\t\t\t// This might not be needed, but I wanted to figure out how to get a field setting in the JS API when the key isn't unique.\n\t\t\tdata.post_type = acf\n\t\t\t\t.getField(\n\t\t\t\t\tacf.findFields( { parent: $fieldObject, key: 'post_type' } )\n\t\t\t\t)\n\t\t\t\t.val();\n\n\t\t\treturn data;\n\t\t},\n\n\t\tmaybeInitNewFieldGroup: function () {\n\t\t\tlet $field_list_wrapper = $(\n\t\t\t\t'#acf-field-group-fields > .inside > .acf-field-list-wrap.acf-auto-add-field'\n\t\t\t);\n\n\t\t\tif ( $field_list_wrapper.length ) {\n\t\t\t\t$( '.acf-headerbar-actions .add-field' ).trigger( 'click' );\n\t\t\t\t$( '.acf-title-wrap #title' ).trigger( 'focus' );\n\t\t\t}\n\t\t},\n\n\t\tonSubmit: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $title = $( '.acf-title-wrap #title' );\n\n\t\t\t// empty\n\t\t\tif ( ! $title.val() ) {\n\t\t\t\t// prevent default\n\t\t\t\te.preventDefault();\n\n\t\t\t\t// unlock form\n\t\t\t\tacf.unlockForm( $el );\n\n\t\t\t\t// focus\n\t\t\t\t$title.trigger( 'focus' );\n\t\t\t}\n\t\t},\n\n\t\tonClick: function ( e ) {\n\t\t\te.preventDefault();\n\t\t},\n\n\t\tonClickDeleteFieldGroup: function ( e, $el ) {\n\t\t\te.preventDefault();\n\t\t\t$el.addClass( '-hover' );\n\n\t\t\t// Add confirmation tooltip.\n\t\t\tacf.newTooltip( {\n\t\t\t\tconfirm: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\ttext: acf.__( 'Move field group to trash?' ),\n\t\t\t\tconfirm: function () {\n\t\t\t\t\twindow.location.href = $el.attr( 'href' );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\t$el.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tvalidateTitle: function ( e, $el ) {\n\t\t\tlet $submitButton = $( '.acf-publish' );\n\n\t\t\tif ( ! $el.val() ) {\n\t\t\t\t$el.addClass( 'acf-input-error' );\n\t\t\t\t$submitButton.addClass( 'disabled' );\n\t\t\t\t$( '.acf-publish' ).addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$el.removeClass( 'acf-input-error' );\n\t\t\t\t$submitButton.removeClass( 'disabled' );\n\t\t\t\t$( '.acf-publish' ).removeClass( 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tfilterFindFieldArgs: function ( args ) {\n\t\t\targs.visible = true;\n\n\t\t\tif (\n\t\t\t\targs.parent &&\n\t\t\t\t( args.parent.hasClass( 'acf-field-object' ) ||\n\t\t\t\t\targs.parent.hasClass( 'acf-browse-fields-modal-wrap' ) ||\n\t\t\t\t\targs.parent.parents( '.acf-field-object' ).length )\n\t\t\t) {\n\t\t\t\targs.visible = false;\n\t\t\t\targs.excludeSubFields = true;\n\t\t\t}\n\n\t\t\t// If the field has any open subfields, don't exclude subfields as they're already being displayed.\n\t\t\tif (\n\t\t\t\targs.parent &&\n\t\t\t\targs.parent.find( '.acf-field-object.open' ).length\n\t\t\t) {\n\t\t\t\targs.excludeSubFields = false;\n\t\t\t}\n\n\t\t\treturn args;\n\t\t},\n\n\t\tfilterFindFieldsSelector: function ( selector ) {\n\t\t\treturn selector + ', .acf-field-acf-field-group-settings-tabs';\n\t\t},\n\t} );\n\n\t/**\n\t *  screenOptionsManager\n\t *\n\t *  Screen options functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar screenOptionsManager = new acf.Model( {\n\t\tid: 'screenOptionsManager',\n\t\twait: 'prepare',\n\n\t\tevents: {\n\t\t\t'change #acf-field-key-hide': 'onFieldKeysChange',\n\t\t\t'change #acf-field-settings-tabs': 'onFieldSettingsTabsChange',\n\t\t\t'change [name=\"screen_columns\"]': 'render',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// vars\n\t\t\tvar $div = $( '#adv-settings' );\n\t\t\tvar $append = $( '#acf-append-show-on-screen' );\n\n\t\t\t// append\n\t\t\t$div.find( '.metabox-prefs' ).append( $append.html() );\n\t\t\t$div.find( '.metabox-prefs br' ).remove();\n\n\t\t\t// clean up\n\t\t\t$append.remove();\n\n\t\t\t// initialize\n\t\t\tthis.$el = $( '#screen-options-wrap' );\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\tisFieldKeysChecked: function () {\n\t\t\treturn this.$el.find( '#acf-field-key-hide' ).prop( 'checked' );\n\t\t},\n\n\t\tisFieldSettingsTabsChecked: function () {\n\t\t\tconst $input = this.$el.find( '#acf-field-settings-tabs' );\n\n\t\t\t// Screen option is hidden by filter.\n\t\t\tif ( ! $input.length ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\treturn $input.prop( 'checked' );\n\t\t},\n\n\t\tgetSelectedColumnCount: function () {\n\t\t\treturn this.$el\n\t\t\t\t.find( 'input[name=\"screen_columns\"]:checked' )\n\t\t\t\t.val();\n\t\t},\n\n\t\tonFieldKeysChange: function ( e, $el ) {\n\t\t\tvar val = this.isFieldKeysChecked() ? 1 : 0;\n\t\t\tacf.updateUserSetting( 'show_field_keys', val );\n\t\t\tthis.render();\n\t\t},\n\n\t\tonFieldSettingsTabsChange: function () {\n\t\t\tconst val = this.isFieldSettingsTabsChecked() ? 1 : 0;\n\t\t\tacf.updateUserSetting( 'show_field_settings_tabs', val );\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\tif ( this.isFieldKeysChecked() ) {\n\t\t\t\t$( '#acf-field-group-fields' ).addClass( 'show-field-keys' );\n\t\t\t} else {\n\t\t\t\t$( '#acf-field-group-fields' ).removeClass( 'show-field-keys' );\n\t\t\t}\n\n\t\t\tif ( ! this.isFieldSettingsTabsChecked() ) {\n\t\t\t\t$( '#acf-field-group-fields' ).addClass( 'hide-tabs' );\n\t\t\t\t$( '.acf-field-settings-main' )\n\t\t\t\t\t.removeClass( 'acf-hidden' )\n\t\t\t\t\t.prop( 'hidden', false );\n\t\t\t} else {\n\t\t\t\t$( '#acf-field-group-fields' ).removeClass( 'hide-tabs' );\n\n\t\t\t\t$( '.acf-field-object' ).each( function () {\n\t\t\t\t\tconst tabFields = acf.getFields( {\n\t\t\t\t\t\ttype: 'tab',\n\t\t\t\t\t\tparent: $( this ),\n\t\t\t\t\t\texcludeSubFields: true,\n\t\t\t\t\t\tlimit: 1,\n\t\t\t\t\t} );\n\n\t\t\t\t\tif ( tabFields.length ) {\n\t\t\t\t\t\ttabFields[ 0 ].tabs.set( 'initialized', false );\n\t\t\t\t\t}\n\n\t\t\t\t\tacf.doAction( 'show', $( this ) );\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\tif ( this.getSelectedColumnCount() == 1 ) {\n\t\t\t\t$( 'body' ).removeClass( 'columns-2' );\n\t\t\t\t$( 'body' ).addClass( 'columns-1' );\n\t\t\t} else {\n\t\t\t\t$( 'body' ).removeClass( 'columns-1' );\n\t\t\t\t$( 'body' ).addClass( 'columns-2' );\n\t\t\t}\n\t\t},\n\t} );\n\n\t/**\n\t *  appendFieldManager\n\t *\n\t *  Appends fields together\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar appendFieldManager = new acf.Model( {\n\t\tactions: {\n\t\t\tnew_field: 'onNewField',\n\t\t},\n\n\t\tonNewField: function ( field ) {\n\t\t\t// bail early if not append\n\t\t\tif ( ! field.has( 'append' ) ) return;\n\n\t\t\t// vars\n\t\t\tvar append = field.get( 'append' );\n\t\t\tvar $sibling = field.$el\n\t\t\t\t.siblings( '[data-name=\"' + append + '\"]' )\n\t\t\t\t.first();\n\n\t\t\t// bail early if no sibling\n\t\t\tif ( ! $sibling.length ) return;\n\n\t\t\t// ul\n\t\t\tvar $div = $sibling.children( '.acf-input' );\n\t\t\tvar $ul = $div.children( 'ul' );\n\n\t\t\t// create ul\n\t\t\tif ( ! $ul.length ) {\n\t\t\t\t$div.wrapInner( '<ul class=\"acf-hl\"><li></li></ul>' );\n\t\t\t\t$ul = $div.children( 'ul' );\n\t\t\t}\n\n\t\t\t// li\n\t\t\tvar html = field.$( '.acf-input' ).html();\n\t\t\tvar $li = $( '<li>' + html + '</li>' );\n\t\t\t$ul.append( $li );\n\t\t\t$ul.attr( 'data-cols', $ul.children().length );\n\n\t\t\t// clean up\n\t\t\tfield.remove();\n\t\t},\n\t} );\n} )( jQuery );\n", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nexport { _defineProperty as default };", "import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nexport { _typeof as default };", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_field-group.js';\nimport './_field-group-field.js';\nimport './_field-group-settings.js';\nimport './_field-group-conditions.js';\nimport './_field-group-fields.js';\nimport './_field-group-locations.js';\nimport './_field-group-compatibility.js';\nimport './_browse-fields-modal.js';\n"], "names": ["$", "undefined", "acf", "browseFieldsModal", "data", "openedBy", "currentFieldType", "popularFieldTypes", "events", "setup", "props", "extend", "$el", "tmpl", "render", "initialize", "open", "lockFocusToModal", "find", "focus", "doAction", "html", "getFieldTypes", "category", "search", "fieldTypes", "get", "Object", "values", "_objectSpread", "filter", "fieldType", "includes", "name", "pro", "label", "toLowerCase", "labelParts", "split", "match", "startsWith", "length", "for<PERSON>ach", "part", "$tabs", "self", "each", "append", "getFieldTypeHTML", "initializeFieldLabel", "initializeFieldType", "onChangeFieldType", "iconName", "replaceAll", "decodeFieldTypeURL", "url", "renderFieldTypeDesc", "fieldTypeInfo", "fieldTypeFilter", "args", "parseArgs", "description", "doc_url", "tutorial_url", "preview_image", "text", "attr", "show", "hide", "parent", "isPro", "isActive", "$upgateToProButton", "$upgradeToUnlockButton", "_fieldObject$data", "fieldObject", "type", "set", "isFieldTypePopular", "selectedFieldType", "x", "uppercaseCategory", "toUpperCase", "slice", "searchTabElement", "setTimeout", "click", "labelText", "$fieldLabel", "val", "updateFieldObjectFieldLabel", "trigger", "removeClass", "addClass", "onSearchFieldTypes", "e", "$modal", "inputVal", "searchString", "resultsHtml", "matches", "trim", "onClickBrowsePopular", "first", "onClickSelectField", "$fieldTypeSelect", "close", "onClickFieldType", "$fieldType", "currentTarget", "onClickClose", "onPressEscapeClose", "key", "returnFocusToOrigin", "remove", "models", "Modal", "newBrowseFieldsModal", "window", "j<PERSON><PERSON><PERSON>", "_acf", "getCompatibility", "field_group", "save_field", "$field", "getFieldObject", "save", "delete_field", "animate", "delete", "update_field_meta", "value", "prop", "delete_field_meta", "field_object", "model", "o", "$settings", "tag", "tags", "splice", "join", "selector", "str_replace", "_add_action", "callback", "add_action", "apply", "arguments", "_add_filter", "add_filter", "_add_event", "event", "substr", "indexOf", "context", "document", "on", "closest", "_set_$field", "setting", "actionManager", "Model", "actions", "open_field_object", "close_field_object", "add_field_object", "duplicate_field_object", "delete_field_object", "change_field_object_type", "change_field_object_label", "change_field_object_name", "change_field_object_parent", "sortstop_field_object", "onOpenFieldObject", "field", "onCloseFieldObject", "onAddFieldObject", "onDuplicateFieldObject", "onDeleteFieldObject", "onChangeFieldObjectType", "onChangeFieldObjectLabel", "onChangeFieldObjectName", "onChangeFieldObjectParent", "ConditionalLogicFieldSetting", "FieldSetting", "$rule", "scope", "ruleData", "$input", "$td", "$toggle", "$control", "$groups", "$rules", "$tabLabel", "$conditionalValueSelect", "$div", "enable", "disable", "renderRules", "renderRule", "renderField", "renderOperator", "renderValue", "choices", "validFieldTypes", "cid", "$select", "getFieldObjects", "map", "choice", "id", "<PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "__", "disabled", "conditionTypes", "getConditionTypes", "getType", "indents", "getParents", "repeat", "push", "renderSelect", "findFieldObject", "prototype", "operator", "currentVal", "savedValue", "getAttribute", "conditionType", "$newSelect", "clone", "is", "classes", "$rebuiltSelect", "addAction", "newSelect2", "Array", "detach", "onChangeToggle", "onClickAddGroup", "addGroup", "$group", "$group2", "duplicate", "not", "$tr", "onFocusField", "onChangeField", "onChangeOperator", "onClickAdd", "onClickRemove", "siblings", "registerFieldSetting", "conditionalLog<PERSON><PERSON><PERSON><PERSON>", "duplicate_field_objects", "onDuplicateFieldObjects", "children", "newField", "prevField", "$selects", "child", "add", "FieldObject", "eventScope", "fieldTypeSelect2", "change", "changed", "inherit", "getInputId", "$meta", "$handle", "$setting", "getParent", "limit", "pop", "getFields", "getInputName", "newInput", "inputId", "inputName", "getProp", "has", "setProp", "prevVal", "keys", "getName", "getTypeLabel", "types", "checkCopyable", "makeCopyable", "navigator", "clipboard", "initializeFieldTypeSelect2", "hasClass", "fn", "select2", "amd", "require", "err", "console", "warn", "ajax", "multiple", "allowNull", "suppressFilters", "dropdownCssClass", "templateResult", "selection", "loading", "element", "nodeName", "$selection", "strEscape", "templateSelection", "target", "parents", "onKeyDownSelect", "addProFields", "PROFieldTypes", "$layoutGroup", "$contentGroup", "entries", "$useGroup", "$existing", "menu_order", "required", "parseInt", "strSanitize", "strSlugify", "refresh", "isOpen", "onClickCopy", "stopPropagation", "copyValue", "writeText", "then", "onClickEdit", "$target", "hasOwnProperty", "onChangeSettingsTab", "onFocusEdit", "$rowOptions", "onBlurEdit", "focusDelayMilliseconds", "$rowOptionsBlurElement", "$rowOptionsFocusElement", "activeElement", "hideEmptyTabs", "slideDown", "which", "slideUp", "serialize", "submit", "onChange", "onChanged", "onChangeLabel", "safeLabel", "encode", "applyFilters", "onChangeName", "sanitizedName", "alert", "onChangeRequired", "newVal", "removeAnimate", "onClickDelete", "shift<PERSON>ey", "tooltip", "newTooltip", "confirmRemove", "confirm", "cancel", "$list", "$fields", "findFieldObjects", "sibling", "endHeight", "complete", "new<PERSON>ey", "uniqid", "$newField", "replace", "end", "copy", "isNumeric", "i", "$label", "wipe", "prevId", "prev<PERSON><PERSON>", "rename", "move", "has<PERSON><PERSON>ed", "popup", "step1", "newPopup", "title", "width", "ajaxData", "action", "field_id", "prepareForAjax", "dataType", "success", "step2", "content", "step3", "preventDefault", "startButtonLoading", "field_group_id", "step4", "wp", "a11y", "speak", "browseFields", "modal", "onChangeType", "changeTimeout", "clearTimeout", "changeType", "newType", "prevType", "prevClass", "newClass", "abort", "$oldSettings", "tab", "$tabSettings", "removeData", "$newSettings", "showFieldTypeSettings", "$loading", "before", "prefix", "xhr", "response", "isAjaxSuccess", "settings", "tabs", "$tab", "tab<PERSON>ontent", "prepend", "updateParent", "ID", "$tabContent", "tabName", "$tabLink", "list", "newFieldObject", "fields", "eventManager", "priority", "addFieldActions", "pluralAction", "singleAction", "singleEvent", "fieldObjects", "arrayArgs", "plural<PERSON><PERSON><PERSON>", "unshift", "singleCallback", "variations", "variation", "fieldManager", "removed_field_object", "onSubmit", "setFieldMenuOrder", "renderFields", "onHoverSortable", "sortable", "helper", "currentName", "Math", "random", "toString", "handle", "connectWith", "start", "ui", "item", "placeholder", "height", "update", "onRemovedField", "onReorderField", "onDeleteField", "onDuplicateField", "eq", "addField", "$el2", "$type", "locationManager", "wait", "addProLocations", "updateGroupsClass", "PROLocationTypes", "$formsGroup", "proOnlyText", "$addNewOptionsPage", "onClickAddRule", "addRule", "onClickRemoveRule", "removeRule", "onChangeRemoveRule", "changeRule", "ajaxdata", "rule", "group", "replaceWith", "$ruleGroups", "rows_count", "modelId", "strPascalCase", "proto", "mid", "newFieldSetting", "getFieldSetting", "getField", "settingsManager", "new_field", "onNewField", "$fieldObject", "EndpointFieldSetting", "$endpoint_setting", "$endpoint_field", "AccordionEndpointFieldSetting", "TabEndpointFieldSetting", "DisplayFormatFieldSetting", "DatePickerDisplayFormatFieldSetting", "DatePickerReturnFormatFieldSetting", "DateTimePickerDisplayFormatFieldSetting", "DateTimePickerReturnFormatFieldSetting", "TimePickerDisplayFormatFieldSetting", "TimePickerReturnFormatFieldSetting", "ColorPickerReturnFormat", "$return_format_setting", "$default_value_setting", "$labelText", "contents", "last", "$defaultPlaceholder", "l10n", "rgba_string", "hex_string", "fieldGroupManager", "filters", "find_fields_args", "find_fields_selector", "maybeInitNewFieldGroup", "setBidirectionalSelect2Args", "setBidirectionalSelect2AjaxDataArgs", "instance", "_field$data", "call", "escHtml", "human_field_type", "field_type", "this_field", "field_key", "parent_key", "post_type", "findFields", "$field_list_wrapper", "$title", "unlockForm", "onClick", "onClickDeleteFieldGroup", "location", "href", "validateTitle", "$submitButton", "filterFindFieldArgs", "visible", "excludeSubFields", "filterFindFieldsSelector", "screenOptionsManager", "$append", "isFieldKeysChecked", "isFieldSettingsTabsChecked", "getSelectedColumnCount", "onFieldKeysChange", "updateUserSetting", "onFieldSettingsTabsChange", "tabFields", "appendFieldManager", "$sibling", "$ul", "wrapInner", "$li"], "sourceRoot": ""}