# BaumGroups Template Integration

## Overview

The BaumGroups plugin now integrates with the BaumPress theme to provide consistent styling and functionality for wall posts. This integration allows wall posts to use the same meme-style layout and comments system as the theme's post types.

## Template Hierarchy

The plugin follows WordPress template hierarchy best practices by checking for theme template parts first, then falling back to plugin defaults.

### Wall Post Rendering

When rendering wall posts, the plugin checks for templates in this order:

1. **Theme Template Part**: `parts/baum-groups-wall-post.php` (in active theme)
2. **Plugin Template Part**: `parts/activity-content.php` (in plugin)
3. **Fallback Rendering**: Built-in PHP rendering method

## Theme Integration

### Required Template Part

To enable meme-style wall posts, create this file in your theme:

```
/wp-content/themes/your-theme/parts/baum-groups-wall-post.php
```

This template part receives the following arguments:

```php
$args = [
    'activity' => $activity,      // WP_Post object for the wall post
    'group_id' => $group_id,      // ID of the group
    'can_delete' => $can_delete,  // Boolean - can current user delete
    'can_moderate' => $can_moderate // Boolean - can current user moderate
];
```

### CSS Integration

The plugin includes comprehensive CSS that matches the theme's meme post styling:

- **Meme-style cards**: `.baum-card` with proper spacing and borders
- **Author headers**: `.baum-meme-author` with avatar and info
- **Content styling**: `.baum-meme-text` for post content
- **Interactive buttons**: `.baum-meme-buttons` for actions
- **Comments integration**: Full comment thread styling

### JavaScript Integration

The plugin includes JavaScript for:

- **Comment loading**: AJAX-powered comment threads
- **Native sharing**: Web Share API with clipboard fallback
- **Delete functionality**: Inline post deletion
- **Form handling**: Comment submission and validation

## Features Included

### Meme-Style Layout

- Author avatar and name display
- Group context and timestamp
- Content with proper typography
- Media support (images/videos)
- Interactive action buttons

### Comments System

- AJAX comment loading via `/comments/{post_id}` endpoint
- Threaded comment display
- Comment form integration
- Real-time comment submission

### Interactive Elements

- Bookmark integration (if theme supports `[baum_bookmark]` shortcode)
- Native sharing with fallback
- Delete functionality for post authors/moderators
- Pinned post indicators

### Responsive Design

- Mobile-optimized layouts
- Touch-friendly buttons
- Scalable typography
- Flexible grid systems

## Implementation Details

### Template Part Structure

The theme template part (`parts/baum-groups-wall-post.php`) follows this structure:

```php
<article class="baum-post baum-meme baum-wall-post">
  <div class="baum-card baum-card-container baum-meme-container">
    <!-- Pinned indicator -->
    <!-- Author header -->
    <!-- Content area -->
    <!-- Media content -->
    <!-- Action buttons -->
    <!-- Comments container -->
  </div>
</article>
```

### CSS Classes

Key CSS classes used:

- `.baum-wall-post` - Main post container
- `.baum-meme-author` - Author header section
- `.baum-meme-text` - Post content area
- `.baum-meme-buttons` - Action buttons container
- `.baum-comments-container` - Comments section

### JavaScript Events

The plugin binds to these events:

- `.load-comments` click - Toggle comment visibility
- `.baum-share-native` click - Native sharing
- `.baum-delete-activity` click - Delete post
- Comment form submission - AJAX comment posting

## Customization

### Styling

Override plugin styles by adding CSS with higher specificity:

```css
.baum-groups .baum-wall-post .baum-meme-author {
    /* Your custom styles */
}
```

### Template Customization

Modify the theme template part to customize layout:

```php
// In parts/baum-groups-wall-post.php
// Add custom elements, modify structure, etc.
```

### JavaScript Hooks

Extend functionality by listening for custom events:

```javascript
$(document).on('baum-wall-post-loaded', function(e, postId) {
    // Custom functionality
});
```

## Compatibility

### Theme Requirements

- BaumPress theme (or compatible theme)
- Support for `get_template_part()` with arguments
- CSS custom properties support
- jQuery for JavaScript functionality

### WordPress Requirements

- WordPress 5.0+
- Comments system enabled
- AJAX support
- Proper nonce handling

### Browser Support

- Modern browsers with ES6 support
- Web Share API (with fallback)
- CSS Grid and Flexbox
- Fetch API or jQuery AJAX

## Troubleshooting

### Template Not Loading

1. Check file path: `parts/baum-groups-wall-post.php`
2. Verify file permissions
3. Clear any caching plugins
4. Check for PHP errors in logs

### Styling Issues

1. Ensure CSS is properly enqueued
2. Check for theme conflicts
3. Verify CSS custom properties support
4. Use browser dev tools to debug

### JavaScript Errors

1. Check browser console for errors
2. Verify jQuery is loaded
3. Ensure proper AJAX endpoints
4. Check nonce validation

## Migration Notes

### From Previous Versions

If upgrading from a previous version:

1. The plugin will automatically use the new template system
2. Existing wall posts will render with new styling
3. No database changes required
4. Old CSS may need cleanup

### Theme Updates

When updating the theme:

1. Backup custom template parts
2. Re-apply customizations after update
3. Test wall post rendering
4. Update any custom CSS if needed

## Support

For issues related to template integration:

1. Check this documentation first
2. Verify theme compatibility
3. Test with default WordPress theme
4. Check plugin and theme logs
5. Contact support with specific error details
