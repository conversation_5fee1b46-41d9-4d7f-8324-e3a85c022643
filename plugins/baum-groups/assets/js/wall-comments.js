/**
 * Baum Groups - Wall Comments JavaScript
 * Handles comment loading and interaction for wall posts
 */

(function($) {
  'use strict';

  /**
   * Initialize comments functionality
   */
  function initWallComments() {
    // Bind comment loading
    bindCommentLoaders();
    
    // Bind native sharing
    bindNativeSharing();
    
    // Bind delete functionality
    bindDeleteActions();
  }

  /**
   * Bind comment loading functionality
   */
  function bindCommentLoaders() {
    $(document).on('click', '.load-comments', function(e) {
      e.preventDefault();
      
      const $button = $(this);
      const postId = $button.data('post-id');
      const commentsId = $button.data('comments-id');
      const $container = $('#comments-container-' + postId);
      
      if (!postId) {
        console.error('No post ID found for comments button');
        return;
      }
      
      // Toggle comments visibility
      if ($container.is(':visible')) {
        $container.slideUp(300);
        $button.removeClass('active');
        return;
      }
      
      // Show loading state
      $button.addClass('active').prop('disabled', true);
      $container.html('<div class="baum-comments-loading"><i class="fas fa-spinner fa-spin"></i> Loading comments...</div>').slideDown(300);
      
      // Load comments via AJAX
      loadCommentsForPost(postId, $container, $button);
    });
  }

  /**
   * Load comments for a specific post
   */
  function loadCommentsForPost(postId, $container, $button) {
    // Use WordPress comments endpoint if available
    const commentsUrl = window.location.origin + '/comments/' + postId;
    
    $.ajax({
      url: commentsUrl,
      type: 'GET',
      success: function(response) {
        // Extract comments content from response
        const $response = $(response);
        const $comments = $response.find('#comments');
        
        if ($comments.length > 0) {
          $container.html('<div class="baum-comments-thread">' + $comments.prop('outerHTML') + '</div>');
        } else {
          $container.html('<div class="baum-no-comments"><p>No comments yet. Be the first to comment!</p></div>');
        }
        
        // Re-enable button
        $button.prop('disabled', false);
        
        // Initialize comment form if present
        initCommentForm($container);
        
      },
      error: function() {
        $container.html('<div class="baum-comments-error"><p>Error loading comments. Please try again.</p></div>');
        $button.prop('disabled', false).removeClass('active');
      }
    });
  }

  /**
   * Initialize comment form functionality
   */
  function initCommentForm($container) {
    const $form = $container.find('#commentform');
    
    if ($form.length === 0) {
      return;
    }
    
    // Handle form submission
    $form.on('submit', function(e) {
      e.preventDefault();
      
      const $submitButton = $form.find('input[type="submit"]');
      const originalText = $submitButton.val();
      
      // Show loading state
      $submitButton.val('Posting...').prop('disabled', true);
      
      // Submit via AJAX
      $.ajax({
        url: $form.attr('action') || window.location.href,
        type: 'POST',
        data: $form.serialize(),
        success: function(response) {
          // Reload comments to show new comment
          const postId = $form.find('input[name="comment_post_ID"]').val();
          const $commentsContainer = $('#comments-container-' + postId);
          const $commentsButton = $('#comments-button-' + postId);
          
          if (postId && $commentsContainer.length) {
            loadCommentsForPost(postId, $commentsContainer, $commentsButton);
          }
          
          // Reset form
          $form[0].reset();
          $submitButton.val(originalText).prop('disabled', false);
          
        },
        error: function() {
          alert('Error posting comment. Please try again.');
          $submitButton.val(originalText).prop('disabled', false);
        }
      });
    });
  }

  /**
   * Bind native sharing functionality
   */
  function bindNativeSharing() {
    $(document).on('click', '.baum-share-native', function(e) {
      e.preventDefault();
      
      const $button = $(this);
      const url = $button.data('url') || window.location.href;
      const title = $button.data('title') || document.title;
      const text = $button.data('text') || '';
      
      if (navigator.share) {
        navigator.share({
          title: title,
          text: text,
          url: url
        }).catch(function(error) {
          console.log('Error sharing:', error);
          fallbackShare(url, title);
        });
      } else {
        fallbackShare(url, title);
      }
    });
  }

  /**
   * Fallback sharing method
   */
  function fallbackShare(url, title) {
    // Copy to clipboard
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(function() {
        showNotification('Link copied to clipboard!', 'success');
      }).catch(function() {
        promptCopyLink(url);
      });
    } else {
      promptCopyLink(url);
    }
  }

  /**
   * Prompt user to copy link manually
   */
  function promptCopyLink(url) {
    const message = 'Copy this link to share:\n\n' + url;
    if (window.prompt) {
      window.prompt(message, url);
    } else {
      alert(message);
    }
  }

  /**
   * Bind delete functionality
   */
  function bindDeleteActions() {
    $(document).on('click', '.baum-delete-activity', function(e) {
      e.preventDefault();
      
      const $button = $(this);
      const activityId = $button.data('activity-id');
      
      if (!activityId) {
        return;
      }
      
      if (!confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
        return;
      }
      
      // Show loading state
      $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Deleting...');
      
      // Delete via AJAX
      $.ajax({
        url: baumGroupsWall.ajaxurl,
        type: 'POST',
        data: {
          action: 'baum_delete_wall_activity',
          activity_id: activityId,
          nonce: baumGroupsWall.nonce
        },
        success: function(response) {
          if (response.success) {
            // Remove the activity from DOM
            $button.closest('.baum-wall-post, .baum-activity-item').fadeOut(300, function() {
              $(this).remove();
            });
            showNotification('Post deleted successfully.', 'success');
          } else {
            const errorMessage = (response.data && response.data.message) ? response.data.message : 'Error deleting post.';
            showNotification(errorMessage, 'error');
            $button.prop('disabled', false).html('<i class="fa-fw fas fa-trash"></i> &nbsp; Delete');
          }
        },
        error: function() {
          showNotification('Network error. Please try again.', 'error');
          $button.prop('disabled', false).html('<i class="fa-fw fas fa-trash"></i> &nbsp; Delete');
        }
      });
    });
  }

  /**
   * Show notification message
   */
  function showNotification(message, type) {
    // Use existing notification system if available
    if (typeof showNotification === 'function' && showNotification !== arguments.callee) {
      showNotification(message, type);
      return;
    }
    
    // Simple fallback notification
    const $notification = $('<div class="baum-notification baum-notification-' + type + '">' + message + '</div>');
    $notification.css({
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '4px',
      color: 'white',
      backgroundColor: type === 'success' ? '#28a745' : '#dc3545',
      zIndex: 10000,
      fontSize: '14px'
    });
    
    $('body').append($notification);
    
    setTimeout(function() {
      $notification.fadeOut(300, function() {
        $(this).remove();
      });
    }, 3000);
  }

  // Initialize when document is ready
  $(document).ready(function() {
    initWallComments();
  });

})(jQuery);
